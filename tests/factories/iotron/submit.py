from datetime import timezone

import factory
from factory import Factory, Faker, List, SubFactory
from factory.fuzzy import FuzzyChoice

from nga.apps.agreements.enums import TaxTypeEnum
from nga.apps.iotron.submit.consts import (
    IOTRON_DEFAULT_AGREEMENT_STATUS,
    IOTRON_DEFAULT_AGREEMENT_TYPE,
    IOTRON_NO_VALUE,
    IOTRON_YES_VALUE,
    IOTRONDiscountCalculationTypeEnum,
    IOTRONDiscountDirectionEnum,
    IOTRONEventTypeEnum,
    IOTRONServiceTypeEnum,
)
from nga.apps.iotron.submit.models import (
    IOTRONAgreement,
    IOTRONAgreementIncludeAccessFeeInFinancialCommitment,
    IOTRONAgreementPremiumNumbers,
    IOTRONDiscountParameter,
)
from nga.apps.iotron.submit.nga_agreement import NGAAgreement
from nga.core.enums import VolumeTypeEnum
from tests.factories.agreements import BudgetAgreementFactory
from tests.factories.agreements.domain import DiscountFactory


class FuzzyIOTRONStringBoolean(FuzzyChoice):
    def __init__(self):
        choices = [IOTRON_YES_VALUE, IOTRON_NO_VALUE]

        super().__init__(choices)


class NGAAgreementFactory(Factory):
    budget_agreement = SubFactory(BudgetAgreementFactory)

    discounts = List(
        [
            SubFactory(DiscountFactory),
        ]
    )

    class Meta:
        model = NGAAgreement


class _IOTRONAgreementPremiumNumbersFactory(Factory):
    in_discount_rate = FuzzyIOTRONStringBoolean()

    in_commitment = FuzzyIOTRONStringBoolean()

    class Meta:
        model = IOTRONAgreementPremiumNumbers


class _IOTRONAgreementIncludeAccessFeeInFinancialCommitmentFactory(Factory):
    customer_outbound = FuzzyIOTRONStringBoolean()

    visitor_inbound = FuzzyIOTRONStringBoolean()

    class Meta:
        model = IOTRONAgreementIncludeAccessFeeInFinancialCommitment


class IOTRONDiscountParameterFactory(Factory):
    discount_id = Faker("pyint")

    home_operators = ["AAAAA"]

    partner_operators = ["BBBBB"]

    direction = Faker("random_element", elements=IOTRONDiscountDirectionEnum)

    service_type = Faker("random_element", elements=IOTRONServiceTypeEnum)

    event_type = Faker("random_element", elements=IOTRONEventTypeEnum)

    currency_code = "AAA"

    tax_type = TaxTypeEnum.NET.name

    volume_type = VolumeTypeEnum.ACTUAL.name

    calculation_type = Faker("random_element", elements=IOTRONDiscountCalculationTypeEnum)

    basis = None
    basis_value = None

    bound_type = None

    balancing_type = None

    call_destinations = None

    fair_usage = None

    qualifying_service = None

    airtime_toll = None

    access_fee = None

    intermediate_dates = None

    traffic_segments = None

    class Meta:
        model = IOTRONDiscountParameter


class IOTRONAgreementFactory(Factory):
    agreement_id = Faker("pyint")

    status = IOTRON_DEFAULT_AGREEMENT_STATUS

    agreement_reference = Faker("pystr")

    agreement_type = IOTRON_DEFAULT_AGREEMENT_TYPE

    agreement_start_date = Faker("date_time", tzinfo=timezone.utc)

    agreement_end_date = Faker("date_time", tzinfo=timezone.utc)

    negotiator = None

    home_operators = []

    partner_operators = []

    include_satellite = True

    premium_numbers = factory.SubFactory(_IOTRONAgreementPremiumNumbersFactory)

    include_access_fee_in_financial_commitment = factory.SubFactory(
        _IOTRONAgreementIncludeAccessFeeInFinancialCommitmentFactory
    )

    discounts = []

    class Meta:
        model = IOTRONAgreement
