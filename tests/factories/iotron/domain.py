import factory
from factory import DictFactory, Factory, Faker

from nga.apps.agreements.enums import DiscountCalculationTypeEnum, DiscountSettlementMethodEnum, TaxTypeEnum
from nga.apps.iotron.domain.dto import DiscountDict, DiscountParameterDict
from nga.apps.iotron.domain.models import ExternalAgreement
from nga.apps.iotron.enums import ExternalAgreementProcessingStatusEnum
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum, VolumeTypeEnum
from nga.core.types import DatePeriod
from tests.factories.types import DatePeriodFactory


class DiscountParameterDictFactory(DictFactory):
    calculation_type = DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL.name

    class Meta:
        model = DiscountParameterDict


class DiscountDictFactory(DictFactory):
    home_operators = Faker("pylist", value_types=("int",))
    partner_operators = Faker("pylist", value_types=("int",))

    direction = TrafficDirectionEnum.OUTBOUND.name
    service_types = [ServiceTypeEnum.SMS_MO.name]

    start_date = "2024-01-01"
    end_date = "2024-12-01"

    currency_code = "EUR"

    tax_type = TaxTypeEnum.NET.name
    volume_type = VolumeTypeEnum.ACTUAL.name

    settlement_method = DiscountSettlementMethodEnum.CREDIT_NOTE_EOA.name

    above_commitment_rate = None

    discount_parameters = factory.List([factory.SubFactory(DiscountParameterDictFactory)])

    class Meta:
        model = DiscountDict


class ExternalAgreementFactory(Factory):
    id = Faker("pyint")

    external_id = Faker("pyint")
    name = Faker("pystr")

    period: DatePeriod = DatePeriodFactory()

    home_operators = Faker("pylist", value_types=("int",))
    partner_operators = Faker("pylist", value_types=("int",))

    negotiator = None

    do_not_calculate = False

    include_satellite = True

    include_premium = True

    include_premium_in_commitment = True

    is_rolling = True

    include_access_fee_in_sop_financial_inbound = True

    include_access_fee_in_sop_financial_outbound = True

    discounts = factory.List([factory.SubFactory(DiscountDictFactory)])

    terminated_at = None

    processed_at = None
    processing_status = ExternalAgreementProcessingStatusEnum.NOT_PROCESSED

    failed_message = ""

    class Meta:
        model = ExternalAgreement
