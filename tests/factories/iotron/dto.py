from factory import DictFactory

from nga.apps.agreements.enums import (
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.iotron.domain.dto import DiscountDict, DiscountParameterDict
from nga.core.enums import ServiceTypeEnum, VolumeTypeEnum


class DiscountParameterDictFactory(DictFactory):
    calculation_type = DiscountCalculationTypeEnum.STEPPED_TIERED.name

    basis = None
    basis_value = None

    balancing = None

    bound_type = None
    lower_bound = None
    upper_bound = None

    toll_rate = None
    airtime_rate = None

    fair_usage_rate = None
    fair_usage_threshold = None

    access_fee_rate = None
    incremental_rate = None

    class Meta:
        model = DiscountParameterDict


class DiscountDictFactory(DictFactory):
    home_operators = [1, 2, 3]
    partner_operators = [5, 6]

    direction = DiscountDirectionEnum.BIDIRECTIONAL.name
    service_types = [ServiceTypeEnum.ACCESS_FEE.name]

    start_date = "2024-07-01"
    end_date = "2024-08-01"

    currency_code = "EUR"

    tax_type = TaxTypeEnum.NET.name
    volume_type = VolumeTypeEnum.ACTUAL.name
    settlement_method = DiscountSettlementMethodEnum.CREDIT_NOTE_EOA.name

    call_destinations = None
    called_countries = None
    traffic_segments = None

    imsi_count_type = None

    above_commitment_rate = None

    discount_parameters = []

    qualifying_direction = None
    qualifying_service_types = None
    qualifying_basis = None
    qualifying_lower_bound = None

    class Meta:
        model = DiscountDict
