from unittest.mock import Mock

from django.contrib.auth.models import AnonymousUser, User
from django.test import RequestFactory
from rest_framework import status
from rest_framework.response import Response
from rest_framework.reverse import reverse_lazy
from rest_framework.test import force_authenticate

from nga.apps.agreements.api.schemas import AgreementErrorResponseSchema
from nga.apps.agreements.api.views import AgreementBulkStatusChangeAPIView
from nga.apps.agreements.commands import BulkStatusChangeBudgetAgreementCommand
from nga.apps.agreements.dto import BudgetAgreementError
from nga.apps.agreements.enums import AgreementStatusEnum
from tests.apps.budgets.fakes import InMemoryBudgetProvider, InMemoryBudgetRepository
from tests.core.fakes import InMemoryMediator
from tests.factories.budgets import BudgetFactory
from tests.internal.fakes import InMemoryUnitOfWork


class TestAgreementBulkStatusChangeAPIView:
    view_class = AgreementBulkStatusChangeAPIView
    url_name = "agreements_status_change_bulk"

    def test_authentication_required(self, rf: RequestFactory):
        url = reverse_lazy(self.url_name, kwargs={"budget_id": 1})
        request = rf.post(
            url, data=dict(initial_status=AgreementStatusEnum.DRAFT.name, target_status=AgreementStatusEnum.LIVE.name)
        )
        request.user = AnonymousUser()

        response = self.view_class.as_view()(request)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_201_created(
        self,
        staff_user,
        rf,
        in_memory_uow,
        override_deps,
    ):
        data = {
            "initial_status": AgreementStatusEnum.IN_REVIEW.name,
            "target_status": AgreementStatusEnum.APPROVED.name,
            "agreement_ids": [5, 8, 10],
            "only_active": None,
        }

        budget = BudgetFactory()
        budget_provider = InMemoryBudgetProvider(budgets=[budget])

        mediator = InMemoryMediator([[], []])

        with override_deps(uow=in_memory_uow, budget_provider=budget_provider, mediator=mediator):
            response = self.status_change_agreements(data, staff_user, rf, budget.id)

        assert response.status_code == status.HTTP_201_CREATED

        bulk_status_change_cmd = mediator.get_message_by_type(BulkStatusChangeBudgetAgreementCommand)

        assert bulk_status_change_cmd.budget_id == budget.id
        assert sorted(bulk_status_change_cmd.budget_agreement_ids) == sorted(data["agreement_ids"])

    def test_404_when_budget_does_not_exist(
        self,
        in_memory_uow: InMemoryUnitOfWork,
        in_memory_mediator: InMemoryMediator,
        staff_user: User,
        rf: RequestFactory,
        override_deps,
    ):
        budget_repository = InMemoryBudgetRepository(budgets=[])

        with override_deps(uow=in_memory_uow, budget_repository=budget_repository, mediator=in_memory_mediator):
            response = self.status_change_agreements(
                {
                    "initial_status": AgreementStatusEnum.IN_REVIEW.name,
                    "target_status": AgreementStatusEnum.APPROVED.name,
                },
                staff_user,
                rf,
                budget_id=44,
            )

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_when_agreements_cannot_change_status(
        self,
        in_memory_uow: InMemoryUnitOfWork,
        staff_user: User,
        rf: RequestFactory,
        override_deps,
    ):
        budget = BudgetFactory()
        budget_repository = InMemoryBudgetRepository(budgets=[budget])

        failed_agreement_id_1 = 12
        failed_agreement_id_2 = 44
        error_message_nga = "Intersects other agreements"
        error_message_py = "Python exception"

        mediator_response = [
            [],
            [
                BudgetAgreementError(agreement_id=failed_agreement_id_1, detail=error_message_nga),
                BudgetAgreementError(agreement_id=failed_agreement_id_2, detail=error_message_py),
            ],
        ]

        mediator = Mock()
        mediator.send.side_effect = [mediator_response]

        with override_deps(uow=in_memory_uow, budget_repository=budget_repository, mediator=mediator):
            response = self.status_change_agreements(
                {
                    "initial_status": AgreementStatusEnum.IN_REVIEW.name,
                    "target_status": AgreementStatusEnum.APPROVED.name,
                    "agreement_ids": [5, failed_agreement_id_1, failed_agreement_id_2],
                    "only_active": None,
                },
                staff_user,
                rf,
                budget.id,
            )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

        expected_response_schema = AgreementErrorResponseSchema(
            failed_agreements=[
                BudgetAgreementError(agreement_id=failed_agreement_id_1, detail=error_message_nga),
                BudgetAgreementError(agreement_id=failed_agreement_id_2, detail=error_message_py),
            ]
        )

        s = self.view_class.error_serializer_class(data=response.data)
        s.is_valid()

        actual_response_schema = s.save()

        assert actual_response_schema == expected_response_schema

    def status_change_agreements(
        self,
        data: dict,
        user: User,
        rf: RequestFactory,
        budget_id: int,
    ) -> Response:
        url = reverse_lazy(self.url_name, kwargs={"budget_id": budget_id})

        request = rf.post(url, data=data, content_type="application/json")

        request.user = user
        force_authenticate(request, user)

        response = self.view_class.as_view()(request, budget_id=budget_id)

        return response
