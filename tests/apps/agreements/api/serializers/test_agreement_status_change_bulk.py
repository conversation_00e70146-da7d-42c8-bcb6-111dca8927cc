from nga.apps.agreements.api.schemas import AgreementBulkStatusChangeSchema
from nga.apps.agreements.api.serializers import AgreementBulkStatusChangeSchemaSerializer
from nga.apps.agreements.enums import AgreementStatusEnum


class TestAgreementBulkStatusChangeSchemaSerializer:
    serializer_class = AgreementBulkStatusChangeSchemaSerializer

    def test_schema(self):
        expected_fields = (
            "initial_status",
            "target_status",
            "agreement_ids",
            "only_active",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_serialize_required_fields(self):
        data = {
            "initial_status": AgreementStatusEnum.DRAFT.name,
            "target_status": AgreementStatusEnum.IN_REVIEW.name,
        }

        s = self.serializer_class(data=data)
        s.is_valid(raise_exception=True)
        schema = s.save()

        assert isinstance(schema, AgreementBulkStatusChangeSchema)
        assert schema.initial_status == AgreementStatusEnum.DRAFT
        assert schema.target_status == AgreementStatusEnum.IN_REVIEW
        assert schema.agreement_ids is None
        assert schema.only_active is None

    def test_serialize_all_fields(self):
        data = {
            "initial_status": AgreementStatusEnum.DRAFT.name,
            "target_status": AgreementStatusEnum.IN_REVIEW.name,
            "agreement_ids": [5, 6, 7],
            "only_active": True,
        }

        s = self.serializer_class(data=data)
        s.is_valid(raise_exception=True)
        schema = s.save()

        assert isinstance(schema, AgreementBulkStatusChangeSchema)
        assert schema.initial_status == AgreementStatusEnum.DRAFT
        assert schema.target_status == AgreementStatusEnum.IN_REVIEW
        assert schema.agreement_ids == [5, 6, 7]
        assert schema.only_active is True
