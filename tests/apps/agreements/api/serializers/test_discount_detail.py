from decimal import Decimal

from nga.apps.agreements.api.schemas import (
    CommitmentDistributionParameterSchema,
    DiscountQualifyingRuleSchema,
    DiscountSchema,
)
from nga.apps.agreements.api.serializers import DiscountSchemaSerializer
from nga.apps.agreements.api.serializers.discount_detail import (
    CommitmentDistributionParameterSchemaSerializer,
    DiscountParameterSerializer,
    DiscountQualifyingRuleSerializer,
    SubDiscountSchemaSerializer,
)
from nga.apps.agreements.domain.models import DiscountParameter
from nga.apps.agreements.enums import DiscountDirectionEnum, DiscountQualifyingBasisEnum
from nga.apps.references.api.serializers import (
    CountryORMSerializer,
    OperatorORMSerializer,
    TrafficSegmentSchemaSerializer,
)
from nga.core.enums import ServiceTypeEnum
from nga.utils.string import date_to_str
from tests.factories.agreements.api_schemas import DiscountSchemaFactory
from tests.factories.agreements.domain import DiscountParameterFactory
from tests.factories.references import OperatorFactory


class TestDiscountSchemaSerializer:
    serializer_class = DiscountSchemaSerializer

    def test_schema(self):
        expected_fields = (
            "id",
            "agreement_id",
            "home_operators",
            "partner_operators",
            "direction",
            "start_date",
            "end_date",
            "service_types",
            "model_type",
            "currency_code",
            "tax_type",
            "volume_type",
            "settlement_method",
            "call_destinations",
            "called_countries",
            "traffic_segments",
            "imsi_count_type",
            "qualifying_rule",
            "inbound_market_share",
            "commitment_distribution_parameters",
            "parameters",
            "sub_discounts",
            "financial_threshold",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_serialize(self):
        sub_discount_schema = DiscountSchemaFactory()

        schema: DiscountSchema = DiscountSchemaFactory(
            qualifying_rule=DiscountQualifyingRuleSchema(
                direction=DiscountDirectionEnum.INBOUND,
                service_types=(ServiceTypeEnum.VOICE_MO,),
                basis=DiscountQualifyingBasisEnum.VOLUME,
                lower_bound=Decimal("4000000.59"),
                upper_bound=Decimal("10000000.09"),
            ),
            inbound_market_share=Decimal("25.12"),
            commitment_distribution_parameters=[
                CommitmentDistributionParameterSchema(
                    home_operators=(OperatorFactory(),),
                    partner_operators=(OperatorFactory(),),
                    charge=Decimal("45"),
                )
            ],
            sub_discounts=[sub_discount_schema],
        )

        data = self.serializer_class(schema).data

        assert data["id"] == schema.id
        assert data["agreement_id"] == schema.agreement_id
        assert data["home_operators"] == OperatorORMSerializer(schema.home_operators, many=True).data
        assert data["partner_operators"] == OperatorORMSerializer(schema.partner_operators, many=True).data
        assert data["direction"] == schema.direction.name
        assert data["start_date"] == date_to_str(schema.period.start_date)
        assert data["end_date"] == date_to_str(schema.period.end_date)
        assert data["model_type"] == schema.model_type.name
        assert data["service_types"] == [s.name for s in schema.service_types]
        assert data["currency_code"] == schema.currency_code
        assert data["tax_type"] == schema.tax_type.name
        assert data["volume_type"] == schema.volume_type.name
        assert data["settlement_method"] == schema.settlement_method.name
        assert data["call_destinations"] == [cd.name for cd in schema.call_destinations]
        assert data["called_countries"] == CountryORMSerializer(schema.called_countries, many=True).data
        assert data["qualifying_rule"] == DiscountQualifyingRuleSerializer(schema.qualifying_rule).data
        assert data["traffic_segments"] == TrafficSegmentSchemaSerializer(schema.traffic_segments, many=True).data
        assert data["inbound_market_share"] == "25.12"
        assert (
            data["commitment_distribution_parameters"]
            == CommitmentDistributionParameterSchemaSerializer(
                schema.commitment_distribution_parameters, many=True
            ).data
        )
        assert data["parameters"] == DiscountParameterSerializer(schema.parameters, many=True).data
        assert data["sub_discounts"] == SubDiscountSchemaSerializer([sub_discount_schema], many=True).data


class TestSubDiscountSchemaSerializer:
    serializer_class = SubDiscountSchemaSerializer

    def test_schema(self):
        expected_fields = (
            "id",
            "agreement_id",
            "home_operators",
            "partner_operators",
            "direction",
            "start_date",
            "end_date",
            "service_types",
            "model_type",
            "currency_code",
            "tax_type",
            "volume_type",
            "settlement_method",
            "call_destinations",
            "called_countries",
            "traffic_segments",
            "imsi_count_type",
            "parent_id",
            "above_commitment_rate",
            "qualifying_rule",
            "inbound_market_share",
            "parameters",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_serialize(self):
        schema: DiscountSchema = DiscountSchemaFactory(
            parent_id=11,
            qualifying_rule=DiscountQualifyingRuleSchema(
                direction=DiscountDirectionEnum.INBOUND,
                service_types=(ServiceTypeEnum.VOICE_MO,),
                basis=DiscountQualifyingBasisEnum.VOLUME,
                lower_bound=Decimal("0"),
                upper_bound=None,
            ),
            above_commitment_rate=Decimal("2456.78903"),
            inbound_market_share=Decimal("12.43"),
        )

        data = self.serializer_class(schema).data

        assert data["id"] == schema.id
        assert data["agreement_id"] == schema.agreement_id
        assert data["home_operators"] == OperatorORMSerializer(schema.home_operators, many=True).data
        assert data["partner_operators"] == OperatorORMSerializer(schema.partner_operators, many=True).data
        assert data["direction"] == schema.direction.name
        assert data["start_date"] == date_to_str(schema.period.start_date)
        assert data["end_date"] == date_to_str(schema.period.end_date)
        assert data["model_type"] == schema.model_type.name
        assert data["service_types"] == [s.name for s in schema.service_types]
        assert data["currency_code"] == schema.currency_code
        assert data["tax_type"] == schema.tax_type.name
        assert data["volume_type"] == schema.volume_type.name
        assert data["settlement_method"] == schema.settlement_method.name
        assert data["call_destinations"] == [cd.name for cd in schema.call_destinations]
        assert data["called_countries"] == CountryORMSerializer(schema.called_countries, many=True).data
        assert data["qualifying_rule"] == DiscountQualifyingRuleSerializer(schema.qualifying_rule).data
        assert data["traffic_segments"] == TrafficSegmentSchemaSerializer(schema.traffic_segments, many=True).data
        assert data["parent_id"] == schema.parent_id
        assert data["above_commitment_rate"] == "2456.7890300000"
        assert data["inbound_market_share"] == "12.43"
        assert data["parameters"] == DiscountParameterSerializer(schema.parameters, many=True).data


class TestNewDiscountParameterSerializer:
    serializer_class = DiscountParameterSerializer

    def test_schema(self):
        expected_fields = (
            "discount_id",
            "calculation_type",
            "basis",
            "basis_value",
            "balancing",
            "bound_type",
            "lower_bound",
            "upper_bound",
            "toll_rate",
            "airtime_rate",
            "fair_usage_rate",
            "fair_usage_threshold",
            "access_fee_rate",
            "incremental_rate",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_serialize(self):
        discount_dec_val = Decimal("2131.230000")
        discount_bound_dec_val = Decimal("11.12")

        param: DiscountParameter = DiscountParameterFactory(
            basis_value=discount_dec_val,
            lower_bound=discount_bound_dec_val,
            upper_bound=discount_bound_dec_val,
            toll_rate=discount_dec_val,
            airtime_rate=discount_dec_val,
            fair_usage_rate=discount_dec_val,
            fair_usage_threshold=discount_bound_dec_val,
        )

        data = self.serializer_class(param).data

        expected_discount_dec_value = "2131.2300000000"
        expected_discount_bound_dec_val = "11.12"

        assert data["discount_id"] == param.discount_id
        assert data["calculation_type"] == param.calculation_type.name
        assert data["basis"] == param.basis.name
        assert data["basis_value"] == expected_discount_dec_value
        assert data["balancing"] == param.balancing.name
        assert data["bound_type"] == param.bound_type.name
        assert data["lower_bound"] == expected_discount_bound_dec_val
        assert data["upper_bound"] == expected_discount_bound_dec_val
        assert data["toll_rate"] == expected_discount_dec_value
        assert data["airtime_rate"] == expected_discount_dec_value
        assert data["fair_usage_rate"] == expected_discount_dec_value
        assert data["fair_usage_threshold"] == expected_discount_bound_dec_val
