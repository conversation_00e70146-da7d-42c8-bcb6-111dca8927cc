import itertools
from decimal import Decimal

import pytest

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    above_commitment_rate_only_for_sub_discounts,
    access_fee_rate_filled,
    bound_type_is_volume,
    credit_note_eoa_settlement_method,
    financial_threshold_only_for_sop_financial,
    lower_bound_filled,
    no_balancing,
    only_access_fee,
    param_basis_is_value,
    param_basis_value_is_required,
    service_types_with_combo,
    single_discount_parameter,
    sub_discounts_only_for_sop_financial,
    verify_number_of_parameters,
    verify_single_calculation_type,
)
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountModelTypeEnum,
    DiscountSettlementMethodEnum,
)
from nga.core.enums import ServiceTypeEnum
from tests.factories.agreements.domain import (
    SoPFinancialDiscountFactory,
    SREDiscountFactory,
    SREDiscountParameterFactory,
)


class TestServiceTypesCombo:
    def test_violates_by_combination(self):
        service_type_combinations = list(itertools.product(ServiceTypeEnum, ServiceTypeEnum))

        # sort pairs to have a valid comparison
        service_type_combinations = set(map(lambda c: tuple(sorted(c)), service_type_combinations))

        # Drop valid pairs
        service_type_combinations = list(service_type_combinations)
        service_type_combinations.remove(ServiceTypeEnum.voice_services())
        service_type_combinations.remove(ServiceTypeEnum.sms_services())

        for service_type_combo in service_type_combinations:
            discount = SREDiscountFactory(service_types=service_type_combo)

            with pytest.raises(DiscountValidationError) as exc_info:
                service_types_with_combo(discount)

            assert "invalid combination of service types" in exc_info.value.message

    def test_violates_by_service_types_amount(self):
        service_types = [ServiceTypeEnum.VOICE_MO, ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO]

        discount = SREDiscountFactory(service_types=service_types)

        with pytest.raises(DiscountValidationError) as exc_info:
            service_types_with_combo(discount)

        assert "invalid number of service types" in exc_info.value.message

    @pytest.mark.parametrize(
        "service_types",
        [
            (ServiceTypeEnum.VOICE_MT,),
            (ServiceTypeEnum.VOICE_MO,),
            (ServiceTypeEnum.SMS_MT,),
            (ServiceTypeEnum.SMS_MO,),
            (ServiceTypeEnum.DATA,),
            (ServiceTypeEnum.VOLTE,),
            (ServiceTypeEnum.ACCESS_FEE,),
            ServiceTypeEnum.voice_services(),
            ServiceTypeEnum.sms_services(),
        ],
    )
    def test_when_ok(self, service_types):
        discount = SREDiscountFactory(service_types=service_types)

        service_types_with_combo(discount)


class TestCreditNoteEoa:
    @pytest.mark.parametrize(
        "settlement_method",
        [s for s in DiscountSettlementMethodEnum if s != DiscountSettlementMethodEnum.CREDIT_NOTE_EOA],
    )
    def test_violates_by_settlement_method(self, settlement_method):
        discount = SREDiscountFactory(settlement_method=settlement_method)

        with pytest.raises(DiscountValidationError) as exc_info:
            credit_note_eoa_settlement_method(discount)

        assert "settlement method" in exc_info.value.message

    def test_when_ok(self):
        discount = SREDiscountFactory(settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA)

        credit_note_eoa_settlement_method(discount)


class TestParamBasisIsValue:
    @pytest.mark.parametrize("basis", [None, *(b for b in DiscountBasisEnum if b != DiscountBasisEnum.VALUE)])
    def test_violates_by_basis(self, basis):
        param = SREDiscountParameterFactory(basis=basis)

        with pytest.raises(DiscountValidationError) as exc_info:
            param_basis_is_value(param)

        assert "basis" in exc_info.value.message

    def test_when_ok(self):
        param = SREDiscountParameterFactory(basis=DiscountBasisEnum.VALUE)

        param_basis_is_value(param)


class TestParamBasisValueIsRequired:
    def test_violates_by_basis_value(self):
        param = SREDiscountParameterFactory(basis_value=None)

        with pytest.raises(DiscountValidationError) as exc_info:
            param_basis_value_is_required(param)

        assert "basis value" in exc_info.value.message

    def test_when_ok(self):
        param = SREDiscountParameterFactory(basis_value=Decimal("55"))

        param_basis_value_is_required(param)


class TestNoBalancing:
    @pytest.mark.parametrize("balancing", [b for b in DiscountBalancingEnum if b != DiscountBalancingEnum.NO_BALANCING])
    def test_violates_by_balancing(self, balancing):
        param = SREDiscountParameterFactory(balancing=balancing)

        with pytest.raises(DiscountValidationError) as exc_info:
            no_balancing(param)

        assert "balancing" in exc_info.value.message

    def test_when_ok(self):
        param = SREDiscountParameterFactory(balancing=DiscountBalancingEnum.NO_BALANCING)

        no_balancing(param)


class TestBoundTypeIsVolume:
    @pytest.mark.parametrize("bound_type", [b for b in DiscountBoundTypeEnum if b != DiscountBoundTypeEnum.VOLUME])
    def test_violates_by_bound_type(self, bound_type):
        param = SREDiscountParameterFactory(bound_type=bound_type)

        with pytest.raises(DiscountValidationError) as exc_info:
            bound_type_is_volume(param)

        assert "bound type" in exc_info.value.message

    def test_when_ok(self):
        param = SREDiscountParameterFactory(bound_type=DiscountBoundTypeEnum.VOLUME)

        bound_type_is_volume(param)


class TestLowerBoundFilled:
    def test_when_empty(self):
        p = SREDiscountParameterFactory(lower_bound=None)

        with pytest.raises(DiscountValidationError) as exc_info:
            lower_bound_filled(p)

        assert "lower bound must be set" in exc_info.value.message

    def test_when_ok(self):
        p = SREDiscountParameterFactory(lower_bound=Decimal("34"))

        lower_bound_filled(p)


class TestOnlyAccessFee:
    def test_when_ok(self):
        discount = SREDiscountFactory(service_types=(ServiceTypeEnum.ACCESS_FEE,))

        only_access_fee(discount)

    @pytest.mark.parametrize(
        "service_type",
        (
            ServiceTypeEnum.VOICE_MO,
            ServiceTypeEnum.VOICE_MT,
            ServiceTypeEnum.SMS_MO,
            ServiceTypeEnum.SMS_MT,
            ServiceTypeEnum.VOLTE,
            ServiceTypeEnum.DATA,
        ),
    )
    def test_violates(self, service_type):
        discount = SREDiscountFactory(service_types=(service_type,))

        with pytest.raises(DiscountValidationError) as exc_info:
            only_access_fee(discount)

        assert ServiceTypeEnum.ACCESS_FEE.name in exc_info.value.message


class TestAccessFeeRateFilled:
    def test_when_ok(self):
        discount_parameter = SREDiscountParameterFactory(access_fee_rate=Decimal("34"))

        access_fee_rate_filled(discount_parameter)

    def test_violates(self):
        discount_parameter = SREDiscountParameterFactory(access_fee_rate=None)

        with pytest.raises(DiscountValidationError) as exc_info:
            access_fee_rate_filled(discount_parameter)

        assert "access fee rate" in exc_info.value.message


class TestVerifySingleCalculationType:
    def test_when_ok(self):
        discount = SREDiscountFactory()

        verify_single_calculation_type(discount, DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE)

    def test_violates_by_calculation_type(self):
        discount = SREDiscountFactory(parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL)

        with pytest.raises(DiscountValidationError) as exc_info:
            verify_single_calculation_type(discount, DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE)

        assert DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name in exc_info.value.message

    def test_violates_by_number_of_parameters(self):
        discount = SREDiscountFactory(
            parameters=[
                SREDiscountParameterFactory(),
                SREDiscountParameterFactory(),
            ]
        )

        with pytest.raises(DiscountValidationError) as exc_info:
            verify_single_calculation_type(discount, DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE)

        assert "1" in exc_info.value.message


class TestVerifyNumberOfParameters:
    def test_when_ok(self):
        discount = SREDiscountFactory()

        verify_number_of_parameters(discount, expected_total_parameters=1)

    def test_violates(self):
        discount = SREDiscountFactory()

        with pytest.raises(DiscountValidationError) as exc_info:
            verify_number_of_parameters(discount, expected_total_parameters=2)

        assert "2" in exc_info.value.message


class TestSingleDiscountParameter:
    def test_when_ok(self):
        discount = SREDiscountFactory()

        single_discount_parameter(discount)

    def test_violates(self):
        discount = SREDiscountFactory(
            parameters=[
                SREDiscountParameterFactory(),
                SREDiscountParameterFactory(),
            ]
        )

        with pytest.raises(DiscountValidationError) as exc_info:
            single_discount_parameter(discount)

        assert "1" in exc_info.value.message


class TestFinancialThresholdOnlyForSopFinancial:
    def test_when_ok_with_sop_financial_and_threshold(self):
        discount = SREDiscountFactory(
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
            financial_threshold=Decimal("1000.00")
        )

        financial_threshold_only_for_sop_financial(discount)

    def test_when_ok_with_sop_financial_and_no_threshold(self):
        discount = SREDiscountFactory(
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
            financial_threshold=None
        )

        financial_threshold_only_for_sop_financial(discount)

    def test_when_ok_with_other_model_and_no_threshold(self):
        discount = SREDiscountFactory(
            model_type=DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE,
            financial_threshold=None
        )

        financial_threshold_only_for_sop_financial(discount)

    @pytest.mark.parametrize(
        "model_type",
        [m for m in DiscountModelTypeEnum if m != DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL]
    )
    def test_violates_when_non_sop_financial_has_threshold(self, model_type):
        discount = SREDiscountFactory(
            model_type=model_type,
            financial_threshold=Decimal("1000.00")
        )

        with pytest.raises(DiscountValidationError) as exc_info:
            financial_threshold_only_for_sop_financial(discount)

        assert "Financial threshold can only be set for Send-or-Pay Financial discounts" in exc_info.value.message


class TestSubDiscountsOnlyForSopFinancial:
    def test_when_ok_with_sop_financial_and_sub_discounts(self):
        sub_discount = SREDiscountFactory()
        discount = SoPFinancialDiscountFactory(
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
            sub_discounts=(sub_discount,)
        )

        sub_discounts_only_for_sop_financial(discount)

    def test_when_ok_with_sop_financial_and_no_sub_discounts(self):
        discount = SoPFinancialDiscountFactory(
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
            sub_discounts=()
        )

        sub_discounts_only_for_sop_financial(discount)

    def test_when_ok_with_other_model_and_no_sub_discounts(self):
        discount = SREDiscountFactory(
            model_type=DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE,
            sub_discounts=()
        )

        sub_discounts_only_for_sop_financial(discount)

    @pytest.mark.parametrize(
        "model_type",
        [m for m in DiscountModelTypeEnum if m != DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL]
    )
    def test_violates_when_non_sop_financial_has_sub_discounts(self, model_type):
        sub_discount = SREDiscountFactory()
        discount = SREDiscountFactory(
            model_type=model_type,
            sub_discounts=(sub_discount,)
        )

        with pytest.raises(DiscountValidationError) as exc_info:
            sub_discounts_only_for_sop_financial(discount)

        assert "Setting sub-discounts is available only for Send of Pay Financial discounts" in exc_info.value.message


class TestAboveCommitmentRateOnlyForSubDiscounts:
    def test_when_ok_with_sub_discount_and_above_commitment_rate(self):
        discount = SREDiscountFactory(
            parent_id=123,  # Makes it a sub-discount
            above_commitment_rate=Decimal("0.05")
        )

        above_commitment_rate_only_for_sub_discounts(discount)

    def test_when_ok_with_sub_discount_and_no_above_commitment_rate(self):
        discount = SREDiscountFactory(
            parent_id=123,  # Makes it a sub-discount
            above_commitment_rate=None
        )

        above_commitment_rate_only_for_sub_discounts(discount)

    def test_when_ok_with_parent_and_no_above_commitment_rate(self):
        discount = SREDiscountFactory(
            parent_id=None,  # Makes it a parent discount
            above_commitment_rate=None
        )

        above_commitment_rate_only_for_sub_discounts(discount)

    def test_violates_when_parent_has_above_commitment_rate(self):
        discount = SREDiscountFactory(
            parent_id=None,  # Makes it a parent discount
            above_commitment_rate=Decimal("0.05")
        )

        with pytest.raises(DiscountValidationError) as exc_info:
            above_commitment_rate_only_for_sub_discounts(discount)

        assert "Above commitment rate can only be set for sub-discounts" in exc_info.value.message
