from datetime import date

import pytest
from mediatr import Mediator

from nga.apps.agreements.commands import AutoRenewBudgetAgreementCommand
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from tests.apps.agreements.fakes import InMemoryBudgetAgreementRepository, InMemoryDiscountRepository
from tests.apps.budgets.fakes import InMemoryBudgetRepository
from tests.factories.agreements import BudgetAgreementFactory
from tests.factories.budgets import BudgetFactory


class TestAutoRenewBudgetAgreementCommandHandler:
    def test_agreements_auto_renewed_successfully(
        self,
        override_deps,
        in_memory_mediator,
        in_memory_uow,
        in_memory_event_dispatcher,
    ):
        budget = BudgetFactory(
            period__start_date=date(2025, 1, 1),
            period__end_date=date(2025, 12, 1),
        )

        budget_agreement = BudgetAgreementFactory(
            budget_id=budget.id,
            home_operators=[12, 14],
            partner_operators=[38, 56],
            period__start_date=date(2025, 1, 1),
            period__end_date=date(2025, 4, 1),
        )

        nested_budget_agreement = BudgetAgreementFactory(
            budget_id=budget.id,
            parent_id=budget_agreement.agreement_id,
            home_operators=budget_agreement.home_operators,
            partner_operators=budget_agreement.partner_operators,
            period__start_date=date(2025, 5, 1),
            period__end_date=date(2025, 8, 1),
            status=AgreementStatusEnum.AUTO_RENEWED,
        )

        discount_repository = InMemoryDiscountRepository(discounts=[])
        budget_agreement_repository = InMemoryBudgetAgreementRepository([budget_agreement, nested_budget_agreement])

        auto_renew_cmd = AutoRenewBudgetAgreementCommand(budget_agreement.id)

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            event_dispatcher=in_memory_event_dispatcher,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=discount_repository,
        ):
            Mediator().send(auto_renew_cmd)

        budget_agreements = sorted(
            budget_agreement_repository.get_many(budget_id=budget.id), key=lambda b: b.period.start_date
        )

        assert len(budget_agreements) == 3

        assert budget_agreements[1].parent_id == budget_agreements[0].agreement_id
        assert budget_agreements[1].id == nested_budget_agreement.id

        assert budget_agreements[2].parent_id == budget_agreements[1].agreement_id
        assert budget_agreements[2].name == budget_agreements[1].renew_name()
        assert budget_agreements[2].period == budget_agreements[1].renew_period()
        assert budget_agreements[2].home_operators == budget_agreements[1].home_operators
        assert budget_agreements[2].partner_operators == budget_agreements[1].partner_operators
        assert budget_agreements[2].include_premium == budget_agreements[1].include_premium
        assert budget_agreements[2].include_satellite == budget_agreements[1].include_satellite
        assert budget_agreements[2].include_premium_in_commitment == budget_agreements[1].include_premium_in_commitment
        assert budget_agreements[2].is_rolling == budget_agreements[1].is_rolling

        update_budget_cmd = in_memory_mediator.get_message_by_type(UpdateBudgetAfterAgreementsModifiedCommand)
        assert update_budget_cmd.budget_id == budget_agreement.budget_id

    def test_with_period_error_during_first_renew(
        self,
        override_deps,
        in_memory_mediator,
        in_memory_uow,
        in_memory_event_dispatcher,
    ):
        budget = BudgetFactory(
            period__start_date=date(2025, 1, 1),
            period__end_date=date(2025, 5, 1),
        )
        budget_agreement = BudgetAgreementFactory(
            budget_id=budget.id,
            period__start_date=date(2025, 1, 1),
            period__end_date=date(2025, 3, 1),
        )

        discount_repository = InMemoryDiscountRepository(discounts=[])
        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement])

        auto_renew_cmd = AutoRenewBudgetAgreementCommand(budget_agreement.id)

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            event_dispatcher=in_memory_event_dispatcher,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=discount_repository,
        ):
            Mediator().send(auto_renew_cmd)

        assert len(budget_agreement_repository.get_many(budget_id=budget.id)) == 1

        with pytest.raises(StopIteration):
            in_memory_mediator.get_message_by_type(UpdateBudgetAfterAgreementsModifiedCommand)
