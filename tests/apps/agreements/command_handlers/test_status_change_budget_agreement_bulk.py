from datetime import date, datetime
from unittest.mock import Mock

import pytest
from mediatr import Mediator

from nga.apps.agreements.commands import AutoRenewBudgetAgreementCommand, BulkStatusChangeBudgetAgreementCommand
from nga.apps.agreements.domain.events import BudgetAgreementConfirmedEvent
from nga.apps.agreements.domain.exceptions import AgreementIntersectionError
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.agreements.dto import BudgetAgreementError
from nga.apps.agreements.enums import AgreementCalculationStatusEnum, AgreementStatusEnum
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryBudgetAgreementRepository
from tests.factories.agreements import BudgetAgreementFactory
from tests.factories.budgets import BudgetFactory


class TestBulkStatusChangeBudgetAgreementCommandHandler:
    def test_agreement_status_changed_successfully(self, override_deps, in_memory_mediator, in_memory_uow):
        budget_id = 12

        budget_agreement = BudgetAgreementFactory(
            budget_id=budget_id,
            status=AgreementStatusEnum.APPROVED,
            calculation_status=AgreementCalculationStatusEnum.APPLIED,
            updated_at=datetime(2025, 1, 1),
        )

        cmd = BulkStatusChangeBudgetAgreementCommand(
            budget_id=budget_id,
            initial_status=AgreementStatusEnum.APPROVED,
            target_status=AgreementStatusEnum.LIVE,
            budget_agreement_ids=[budget_agreement.id],
            only_active=None,
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_agreement_repository=InMemoryBudgetAgreementRepository(agreements=[budget_agreement]),
        ):
            status_changed_budget_agreements, errors = Mediator().send(cmd)

        assert len(status_changed_budget_agreements) == 1

        status_changed_budget_agreement = status_changed_budget_agreements[0]

        assert status_changed_budget_agreement.status == AgreementStatusEnum.LIVE
        assert status_changed_budget_agreement.calculation_status == AgreementCalculationStatusEnum.OUTDATED

    def test_when_agreement_is_intersected_with_other_agreements(
        self, override_deps, in_memory_mediator, in_memory_uow
    ):
        budget_id = 12

        existing_budget_agreement = BudgetAgreementFactory(
            budget_id=budget_id,
            status=AgreementStatusEnum.SUBMITTED,
            period=DatePeriod(date(2025, 1, 1), date(2025, 2, 1)),
        )

        budget_agreement_to_approve = BudgetAgreementFactory(
            budget_id=budget_id,
            home_operators=existing_budget_agreement.home_operators,
            partner_operators=existing_budget_agreement.partner_operators,
            status=AgreementStatusEnum.IN_REVIEW,
            period=DatePeriod(date(2025, 2, 1), date(2023, 3, 1)),
        )

        budget_agreement_repository = InMemoryBudgetAgreementRepository(
            agreements=[existing_budget_agreement, budget_agreement_to_approve]
        )

        cmd = BulkStatusChangeBudgetAgreementCommand(
            budget_id=budget_id,
            initial_status=AgreementStatusEnum.IN_REVIEW,
            target_status=AgreementStatusEnum.APPROVED,
            budget_agreement_ids=[budget_agreement_to_approve.id],
            only_active=None,
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_agreement_repository=budget_agreement_repository,
        ):
            status_changed_budget_agreements, errors = Mediator().send(cmd)

        assert len(status_changed_budget_agreements) == 0

        assert len(errors) == 1

        expected_error = BudgetAgreementError(
            agreement_id=budget_agreement_to_approve.id,
            detail=AgreementIntersectionError(budget_agreement_to_approve.id).message,
        )

        assert expected_error in errors

    def test_intersection_check_call(self, override_deps, in_memory_mediator, in_memory_uow):
        budget_agreement = BudgetAgreementFactory(status=AgreementStatusEnum.IN_REVIEW)

        budget_agreement_repository = Mock(spec=AbstractBudgetAgreementRepository)
        budget_agreement_repository.get_many.return_value = [budget_agreement]

        budget_agreement_repository.has_intersection.return_value = False

        cmd = BulkStatusChangeBudgetAgreementCommand(
            budget_id=budget_agreement.budget_id,
            initial_status=AgreementStatusEnum.IN_REVIEW,
            target_status=AgreementStatusEnum.APPROVED,
            budget_agreement_ids=[budget_agreement.id],
            only_active=None,
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_agreement_repository=budget_agreement_repository,
        ):
            Mediator().send(cmd)

        budget_agreement_repository.has_intersection.assert_called_once_with(
            budget_agreement,
            with_statuses=AgreementStatusEnum.get_confirmed_statuses(),
        )

    def test_agreement_approval_flow(self, override_deps, in_memory_mediator, in_memory_uow):
        budget_agreement = BudgetAgreementFactory(
            status=AgreementStatusEnum.IN_REVIEW,
            is_rolling=True,
        )

        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement])

        cmd = BulkStatusChangeBudgetAgreementCommand(
            budget_id=budget_agreement.budget_id,
            initial_status=AgreementStatusEnum.IN_REVIEW,
            target_status=AgreementStatusEnum.APPROVED,
            budget_agreement_ids=None,
            only_active=None,
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_agreement_repository=budget_agreement_repository,
        ):
            Mediator().send(cmd)

        updated_budget_agreement = budget_agreement_repository.get_by_id(budget_agreement_id=budget_agreement.id)
        assert updated_budget_agreement.status == AgreementStatusEnum.APPROVED

        auto_renew_cmd = in_memory_mediator.get_message_by_type(AutoRenewBudgetAgreementCommand)
        assert auto_renew_cmd.budget_agreement_id == budget_agreement.id

    def test_agreement_rejection_flow(self, override_deps, in_memory_mediator, in_memory_uow):
        budget_agreement = BudgetAgreementFactory(status=AgreementStatusEnum.DRAFT)

        linked_agreement = BudgetAgreementFactory(
            budget_id=budget_agreement.budget_id,
            parent_id=budget_agreement.agreement_id,
            status=AgreementStatusEnum.AUTO_RENEWED,
        )

        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement, linked_agreement])

        cmd = BulkStatusChangeBudgetAgreementCommand(
            budget_id=budget_agreement.budget_id,
            initial_status=AgreementStatusEnum.DRAFT,
            target_status=AgreementStatusEnum.REJECTED,
            budget_agreement_ids=None,
            only_active=None,
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_agreement_repository=budget_agreement_repository,
        ):
            Mediator().send(cmd)

        budget_agreements = budget_agreement_repository.get_many()

        assert len(budget_agreements) == 1
        assert budget_agreements[0].status == AgreementStatusEnum.REJECTED

    def test_update_budget_command_is_sent(self, override_deps, in_memory_mediator, in_memory_uow):
        budget_agreement = BudgetAgreementFactory(status=AgreementStatusEnum.DRAFT)

        cmd = BulkStatusChangeBudgetAgreementCommand(
            budget_id=budget_agreement.budget_id,
            initial_status=AgreementStatusEnum.DRAFT,
            target_status=AgreementStatusEnum.REJECTED,
            budget_agreement_ids=None,
            only_active=None,
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_agreement_repository=InMemoryBudgetAgreementRepository(agreements=[budget_agreement]),
        ):
            Mediator().send(cmd)

        update_budget_cmd = in_memory_mediator.get_message_by_type(UpdateBudgetAfterAgreementsModifiedCommand)
        assert update_budget_cmd.budget_id == budget_agreement.budget_id

    def test_status_change_many_agreements(self, override_deps, in_memory_mediator, in_memory_uow):
        budget = BudgetFactory()

        budget_agreement_1 = BudgetAgreementFactory(budget_id=budget.id, status=AgreementStatusEnum.LIVE)
        budget_agreement_2 = BudgetAgreementFactory(budget_id=budget.id, status=AgreementStatusEnum.LIVE)
        budget_agreement_3 = BudgetAgreementFactory(budget_id=budget.id, status=AgreementStatusEnum.REJECTED)

        cmd = BulkStatusChangeBudgetAgreementCommand(
            budget_id=budget.id,
            initial_status=AgreementStatusEnum.LIVE,
            target_status=AgreementStatusEnum.CLOSED,
            budget_agreement_ids=None,
            only_active=None,
        )

        budget_agreement_repository = InMemoryBudgetAgreementRepository(
            agreements=[budget_agreement_1, budget_agreement_2, budget_agreement_3],
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_agreement_repository=budget_agreement_repository,
        ):
            status_changed_budget_agreements, errors = Mediator().send(cmd)

        assert len(status_changed_budget_agreements) == 2
        assert len(errors) == 0

        assert in_memory_uow.total_commits == 2

    @pytest.mark.parametrize(
        "status,next_status",
        [
            (AgreementStatusEnum.IN_REVIEW, AgreementStatusEnum.APPROVED),
            (AgreementStatusEnum.DRAFT, AgreementStatusEnum.BUDGETING),
        ],
    )
    def test_budget_agreement_change_status_event_is_sent(
        self,
        status,
        next_status,
        in_memory_uow,
        in_memory_mediator,
        in_memory_event_dispatcher,
        agreement_negotiator_repository_mock,
        override_deps,
    ):
        budget_agreement = BudgetAgreementFactory(status=status)
        budget_agreement_repository = InMemoryBudgetAgreementRepository([budget_agreement])

        cmd = BulkStatusChangeBudgetAgreementCommand(
            budget_id=budget_agreement.budget_id,
            initial_status=status,
            target_status=next_status,
            budget_agreement_ids=None,
            only_active=None,
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            event_dispatcher=in_memory_event_dispatcher,
            budget_agreement_repository=budget_agreement_repository,
            agreement_negotiator_repository=agreement_negotiator_repository_mock,
        ):
            Mediator().send(cmd)

        updated_budget_agreement = budget_agreement_repository.get_by_id(budget_agreement.id)

        event = in_memory_event_dispatcher.get_event_by_name(BudgetAgreementConfirmedEvent.get_event_name())

        assert isinstance(event, BudgetAgreementConfirmedEvent)

        assert event.budget_agreement == updated_budget_agreement
