import pytest
from mediatr import Mediator

from nga.apps.agreements.commands import CreateDiscountCommand, UpdateBudgetAgreementAfterDiscountModifiedCommand
from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_scope import (
    DiscountIntersectionSpecification,
    SubDiscountExceedingDiscountSpecification,
)
from nga.apps.agreements.domain.specifications.discount_setup.main import DiscountSetupSpecification
from nga.apps.agreements.enums import (
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountModelTypeEnum,
    DiscountSettlementMethodEnum,
)
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import FakeDiscountSpecification, InMemoryDiscountRepository
from tests.factories.agreements import BudgetAgreementFactory
from tests.factories.agreements.domain import (
    AgreementFactory,
    DiscountDTOFactory,
    DiscountFactory,
    DiscountParameterDTOFactory,
    SoPFinancialDiscountParameterFactory,
)


class TestCreateDiscountCommandHandler:
    def test_ok(self, override_deps, in_memory_mediator):
        agreement = AgreementFactory()
        budget_agreement = BudgetAgreementFactory(agreement_id=agreement.id)

        discount_repository = InMemoryDiscountRepository()

        discount_dto = DiscountDTOFactory()

        cmd = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=discount_dto)

        discount_modification_spec = FakeDiscountSpecification()

        with override_deps(
            mediator=in_memory_mediator,
            discount_repository=discount_repository,
            discount_modification_spec=discount_modification_spec,
        ):
            created_discount = Mediator().send(cmd)

        assert isinstance(created_discount, Discount)

        created_discount = discount_repository.get_by_id(created_discount.id)

        assert created_discount.agreement_id == agreement.id
        assert created_discount.home_operators == discount_dto.home_operators
        assert created_discount.partner_operators == discount_dto.partner_operators
        assert created_discount.period == DatePeriod(discount_dto.start_date, discount_dto.end_date)

        assert discount_modification_spec.verified is True

    def test_sop_financial_discount_with_sub_discounts(self, override_deps, in_memory_uow, in_memory_mediator):
        agreement = AgreementFactory()
        budget_agreement = BudgetAgreementFactory(agreement_id=agreement.id)

        sub_discount = DiscountFactory(agreement_id=agreement.id)

        discount_repository = InMemoryDiscountRepository([sub_discount])

        discount_dto = DiscountDTOFactory(
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA,
            parameters=[SoPFinancialDiscountParameterFactory()],
            sub_discounts=[sub_discount.id],
        )

        cmd = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=discount_dto)

        discount_modification_spec = FakeDiscountSpecification()

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            discount_repository=discount_repository,
            discount_modification_spec=discount_modification_spec,
            sub_discount_modification_spec=FakeDiscountSpecification(),
        ):
            created_discount = Mediator().send(cmd)

        assert isinstance(created_discount, Discount)

        created_discount = discount_repository.get_by_id(created_discount.id)

        assert sub_discount.parent_id == created_discount.id

    def test_sop_financial_discount_with_equal_sub_discount(self, override_deps, in_memory_uow, in_memory_mediator):
        agreement = AgreementFactory()
        budget_agreement = BudgetAgreementFactory(agreement_id=agreement.id)

        sub_discount_dto = DiscountDTOFactory(
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA,
            parameters=[SoPFinancialDiscountParameterFactory()],
        )

        sub_discount_cmd = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=sub_discount_dto)

        discount_repository = InMemoryDiscountRepository()

        sub_discount_modification_spec = FakeDiscountSpecification()

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            discount_repository=discount_repository,
            discount_modification_spec=FakeDiscountSpecification(),
            sub_discount_modification_spec=sub_discount_modification_spec,
        ):
            created_sub_discount = Mediator().send(sub_discount_cmd)

        discount_dto = sub_discount_dto
        discount_dto.sub_discounts = [created_sub_discount.id]

        cmd = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=discount_dto)

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            discount_repository=discount_repository,
            discount_modification_spec=DiscountIntersectionSpecification(discount_repository),
            sub_discount_modification_spec=FakeDiscountSpecification(),
        ):
            created_discount = Mediator().send(cmd)

        created_discount = discount_repository.get_by_id(created_discount.id)
        created_sub_discount = discount_repository.get_by_id(created_sub_discount.id)

        assert created_sub_discount.parent_id == created_discount.id

    def test_when_sub_discount_has_more_service_types(self, override_deps, in_memory_uow, in_memory_mediator):
        agreement = AgreementFactory()
        budget_agreement = BudgetAgreementFactory(agreement_id=agreement.id)

        sub_discount_dto = DiscountDTOFactory(
            service_types=(ServiceTypeEnum.VOICE_MO, ServiceTypeEnum.VOICE_MT),
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA,
            parameters=[SoPFinancialDiscountParameterFactory()],
        )

        sub_discount_cmd = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=sub_discount_dto)

        discount_repository = InMemoryDiscountRepository()

        discount_modification_spec = FakeDiscountSpecification()

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            discount_repository=discount_repository,
            discount_modification_spec=discount_modification_spec,
            sub_discount_modification_spec=FakeDiscountSpecification(),
        ):
            created_sub_discount = Mediator().send(sub_discount_cmd)

        discount_dto = sub_discount_dto
        discount_dto.service_types = (ServiceTypeEnum.VOICE_MO,)
        discount_dto.sub_discounts = [created_sub_discount.id]

        cmd = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=discount_dto)

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            discount_repository=discount_repository,
            discount_modification_spec=discount_modification_spec,
            sub_discount_modification_spec=SubDiscountExceedingDiscountSpecification(),
        ):
            with pytest.raises(DiscountValidationError) as error:
                Mediator().send(cmd)

        assert "Sub-Discount Service types exceed Discount service types" in error.value.message

    def test_when_spec_is_failed(self, fake_error_discount_spec, override_deps, in_memory_mediator):
        agreement = AgreementFactory()
        budget_agreement = BudgetAgreementFactory(agreement_id=agreement.id)

        cmd = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=DiscountDTOFactory())

        with override_deps(
            mediator=in_memory_mediator,
            discount_repository=InMemoryDiscountRepository(),
            discount_modification_spec=fake_error_discount_spec,
        ):
            with pytest.raises(DiscountValidationError) as exc_info:
                Mediator().send(cmd)

        assert "fake spec error" in exc_info.value.message

    def test_when_discount_not_sop_financial_with_sub_discounts(self, override_deps, in_memory_mediator):
        agreement = AgreementFactory()
        budget_agreement = BudgetAgreementFactory(agreement_id=agreement.id)

        discount_dto = DiscountDTOFactory(
            model_type=DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE,
            sub_discounts=[12],
            parameters=[
                DiscountParameterDTOFactory(
                    calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
                    basis=DiscountBasisEnum.VALUE,
                    balancing=None,
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                )
            ],
        )

        cmd = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=discount_dto)

        with override_deps(
            mediator=in_memory_mediator,
            discount_repository=InMemoryDiscountRepository(),
            discount_modification_spec=DiscountSetupSpecification(),
        ):
            with pytest.raises(DiscountValidationError) as error:
                Mediator().send(cmd)

        assert "Discount" in error.value.message

    def test_when_sub_discount_spec_is_failed(
        self, in_memory_uow, in_memory_mediator, override_deps, fake_error_discount_spec
    ):
        agreement = AgreementFactory()
        budget_agreement = BudgetAgreementFactory(agreement_id=agreement.id)

        sub_discount = DiscountFactory(agreement_id=agreement.id)

        discount_repository = InMemoryDiscountRepository([sub_discount])

        discount_dto = DiscountDTOFactory(
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA,
            parameters=[SoPFinancialDiscountParameterFactory()],
            sub_discounts=[sub_discount.id],
        )

        cmd = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=discount_dto)

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            discount_repository=discount_repository,
            discount_modification_spec=FakeDiscountSpecification(),
            sub_discount_modification_spec=fake_error_discount_spec,
        ):
            with pytest.raises(DiscountValidationError):
                Mediator().send(cmd)

    def test_update_budget_agreement_command_is_sent(self, override_deps, in_memory_mediator):
        agreement = AgreementFactory()
        budget_agreement = BudgetAgreementFactory(agreement_id=agreement.id)

        cmd = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=DiscountDTOFactory())

        with override_deps(
            mediator=in_memory_mediator,
            discount_repository=InMemoryDiscountRepository(),
            discount_modification_spec=FakeDiscountSpecification(),
        ):
            Mediator().send(cmd)

        update_budget_agreement_cmd = in_memory_mediator.get_message_by_type(
            UpdateBudgetAgreementAfterDiscountModifiedCommand
        )

        assert update_budget_agreement_cmd.budget_agreement_id == budget_agreement.id
