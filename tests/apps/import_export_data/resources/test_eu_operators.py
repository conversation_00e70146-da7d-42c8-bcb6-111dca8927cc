import pytest
from django.utils.encoding import force_str
from import_export.formats.base_formats import CSV
from import_export.results import Result

from nga.apps.import_export_data.resources.references import EUOperatorResource
from nga.apps.references.infra.orm.models import EUOperator
from tests.apps.import_export_data.consts import EU_OPERATORS_CSV_FILE_PATH
from tests.factories.references import OperatorORMFactory


@pytest.mark.django_db
class TestEUOperatorResource:
    @classmethod
    def import_records_from_file(cls) -> Result:
        resource = EUOperatorResource()

        with open(EU_OPERATORS_CSV_FILE_PATH, "r") as f:
            data = f.read()

        csv_format = CSV()
        data = force_str(data, "utf8")
        dataset = csv_format.create_dataset(data)

        result = resource.import_data(dataset, dry_run=False)

        return result

    def test_eu_operators_are_imported(self):
        OperatorORMFactory(pmn_code="AAAA1"),
        OperatorORMFactory(pmn_code="AAAA2"),
        OperatorORMFactory(pmn_code="BBBB1"),

        import_result = self.import_records_from_file()

        assert import_result.totals["new"] == 3
        assert import_result.totals["skip"] == 2
        assert import_result.totals["error"] == 1

        assert EUOperator.objects.count() == 3
