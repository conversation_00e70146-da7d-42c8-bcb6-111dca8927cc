from datetime import date
from typing import Optional

import pytest

from tests.factories.iotron.domain import ExternalAgreementFactory


class TestExternalAgreement:
    @pytest.mark.parametrize(
        "terminated_at, expected_is_terminated",
        [
            (None, False),
            (date(1, 1, 1), True),
            (date(2024, 12, 15), True),
            (date(2048, 1, 1), True),
        ],
    )
    def test_is_terminated(self, terminated_at: Optional[date], expected_is_terminated: bool):
        external_agreement = ExternalAgreementFactory(terminated_at=terminated_at)

        assert external_agreement.is_terminated == expected_is_terminated
