from decimal import Decimal

import pytest

from nga.apps.agreements.domain.dto import DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.domain.value_objects import DiscountQualifyingRule
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.iotron.domain.factories.discount_dto import DiscountDTOFactory as DomainDiscountDTOFactory
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import Month
from tests.factories.iotron.dto import DiscountDictFactory, DiscountParameterDictFactory


class TestMapping:
    def test_discount_required_fields(self):
        external_discount = DiscountDictFactory(
            direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
            service_types=[ServiceTypeEnum.ACCESS_FEE.name],
            start_date="2024-07-01",
            end_date="2024-08-01",
            currency_code="EUR",
            tax_type=TaxTypeEnum.NET.name,
            volume_type=VolumeTypeEnum.ACTUAL.name,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA.name,
        )

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        expected_discount_dto = DiscountDTO(
            home_operators=tuple(external_discount["home_operators"]),
            partner_operators=tuple(external_discount["partner_operators"]),
            direction=DiscountDirectionEnum.BIDIRECTIONAL,
            service_types=(ServiceTypeEnum.ACCESS_FEE,),
            start_date=Month(2024, 7, 1),
            end_date=Month(2024, 8, 1),
            tax_type=TaxTypeEnum.NET,
            volume_type=VolumeTypeEnum.ACTUAL,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA,
            currency_code="EUR",
            call_destinations=None,
            called_countries=None,
            imsi_count_type=None,
            qualifying_rule=None,
            parameters=(),
            traffic_segments=None,
            model_type=None,
            above_commitment_rate=None,
            inbound_market_share=None,
            commitment_distribution_parameters=None,
        )

        assert discount_dto == expected_discount_dto

    def test_discount_optional_fields(self):
        external_discount = DiscountDictFactory(
            direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
            service_types=[ServiceTypeEnum.ACCESS_FEE.name],
            start_date="2024-07-01",
            end_date="2024-08-01",
            currency_code="EUR",
            tax_type=TaxTypeEnum.NET.name,
            volume_type=VolumeTypeEnum.ACTUAL.name,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA.name,
            call_destinations=[CallDestinationEnum.UNKNOWN.name, CallDestinationEnum.HOME.name],
            called_countries=[1, 2],
            traffic_segments=[1, 6],
            imsi_count_type=IMSICountTypeEnum.NO_DATA.name,
            qualifying_direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
            qualifying_service_types=[ServiceTypeEnum.ACCESS_FEE.name, ServiceTypeEnum.SMS_MO.name],
            qualifying_basis=DiscountQualifyingBasisEnum.VOLUME.name,
            qualifying_lower_bound="36.89",
        )

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        expected_discount_dto = DiscountDTO(
            home_operators=tuple(external_discount["home_operators"]),
            partner_operators=tuple(external_discount["partner_operators"]),
            direction=DiscountDirectionEnum.BIDIRECTIONAL,
            service_types=(ServiceTypeEnum.ACCESS_FEE,),
            start_date=Month(2024, 7, 1),
            end_date=Month(2024, 8, 1),
            tax_type=TaxTypeEnum.NET,
            volume_type=VolumeTypeEnum.ACTUAL,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA,
            currency_code="EUR",
            call_destinations=(CallDestinationEnum.UNKNOWN, CallDestinationEnum.HOME),
            called_countries=(1, 2),
            imsi_count_type=IMSICountTypeEnum.NO_DATA,
            qualifying_rule=DiscountQualifyingRule(
                direction=DiscountDirectionEnum.BIDIRECTIONAL,
                service_types=(ServiceTypeEnum.ACCESS_FEE, ServiceTypeEnum.SMS_MO),
                volume_type=VolumeTypeEnum.ACTUAL,
                basis=DiscountQualifyingBasisEnum.VOLUME,
                lower_bound=Decimal("36.89"),
                upper_bound=None,
            ),
            parameters=(),
            traffic_segments=(1, 6),
            model_type=None,
            above_commitment_rate=None,
            inbound_market_share=None,
            commitment_distribution_parameters=None,
        )

        assert discount_dto == expected_discount_dto

    def test_discount_parameter_required_fields(self):
        external_discount_parameter = DiscountParameterDictFactory(
            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
        )

        external_discount = DiscountDictFactory(discount_parameters=[external_discount_parameter])

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        assert len(discount_dto.parameters) == 1

        expected_param = DiscountParameterDTO(
            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
            basis=None,
            basis_value=None,
            balancing=None,
            bound_type=None,
            lower_bound=None,
            upper_bound=None,
            toll_rate=None,
            airtime_rate=None,
            fair_usage_rate=None,
            fair_usage_threshold=None,
            access_fee_rate=None,
            incremental_rate=None,
        )

        assert discount_dto.parameters[0] == expected_param

    def test_discount_parameter_optional_fields(self):
        external_discount_parameter = DiscountParameterDictFactory(
            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
            basis=DiscountBasisEnum.VALUE.name,
            basis_value="34.31",
            balancing=DiscountBalancingEnum.NO_BALANCING.name,
            bound_type=DiscountBoundTypeEnum.VOLUME.name,
            lower_bound="11.1",
            upper_bound="22.2",
            toll_rate="3",
            airtime_rate="4",
            fair_usage_rate="5",
            fair_usage_threshold="6",
            access_fee_rate="7",
            incremental_rate="8",
        )

        external_discount = DiscountDictFactory(discount_parameters=[external_discount_parameter])

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        assert len(discount_dto.parameters) == 1

        expected_param = DiscountParameterDTO(
            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
            basis=DiscountBasisEnum.VALUE,
            basis_value=Decimal("34.31"),
            balancing=DiscountBalancingEnum.NO_BALANCING,
            bound_type=DiscountBoundTypeEnum.VOLUME,
            lower_bound=Decimal("11.1"),
            upper_bound=Decimal("22.2"),
            toll_rate=Decimal("3"),
            airtime_rate=Decimal("4"),
            fair_usage_rate=Decimal("5"),
            fair_usage_threshold=Decimal("6"),
            access_fee_rate=Decimal("7"),
            incremental_rate=Decimal("8"),
        )

        assert discount_dto.parameters[0] == expected_param


class TestAdjustIfModelIsPMPIWithIncrementalCharging:
    def test_when_discount_is_not_needed_to_be_changed(self):
        external_discount = DiscountDictFactory(
            service_types=[ServiceTypeEnum.ACCESS_FEE.name, ServiceTypeEnum.DATA.name],
            imsi_count_type=IMSICountTypeEnum.NO_DATA.name,
            discount_parameters=[
                DiscountParameterDictFactory(
                    calculation_type=DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING.name,
                ),
            ],
        )

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        assert discount_dto.service_types == (ServiceTypeEnum.ACCESS_FEE, ServiceTypeEnum.DATA)
        assert discount_dto.imsi_count_type == IMSICountTypeEnum.NO_DATA

    def test_when_number_of_parameters_is_invalid(self):
        external_discount = DiscountDictFactory(
            service_types=[ServiceTypeEnum.DATA.name],
            imsi_count_type=None,
            discount_parameters=[
                DiscountParameterDictFactory(
                    calculation_type=DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING.name,
                ),
                DiscountParameterDictFactory(
                    calculation_type=DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING.name,
                ),
            ],
        )

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        assert discount_dto.service_types == (ServiceTypeEnum.DATA,)
        assert discount_dto.imsi_count_type is None

    def test_when_discount_model_is_invalid(self):
        external_discount = DiscountDictFactory(
            service_types=[ServiceTypeEnum.DATA.name],
            imsi_count_type=IMSICountTypeEnum.NO_DATA.name,
            discount_parameters=[
                DiscountParameterDictFactory(
                    calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
                ),
            ],
        )

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        assert discount_dto.service_types == (ServiceTypeEnum.DATA,)
        assert discount_dto.imsi_count_type == IMSICountTypeEnum.NO_DATA

    def test_discount_was_adjusted(self):
        external_discount = DiscountDictFactory(
            service_types=[ServiceTypeEnum.DATA.name],
            imsi_count_type=None,
            discount_parameters=[
                DiscountParameterDictFactory(
                    calculation_type=DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING.name,
                ),
            ],
        )

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        assert discount_dto.service_types == (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE)
        assert discount_dto.imsi_count_type == IMSICountTypeEnum.DATA


class TestAdjustIfModelIsPMPISteppedTiered:
    def test_when_discount_is_not_needed_to_be_changed(self):
        external_discount = DiscountDictFactory(
            imsi_count_type=IMSICountTypeEnum.NO_DATA.name,
            discount_parameters=[
                DiscountParameterDictFactory(
                    calculation_type=DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED.name,
                ),
            ],
        )

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        assert discount_dto.imsi_count_type == IMSICountTypeEnum.NO_DATA

    def test_discount_is_adjusted(self):
        external_discount = DiscountDictFactory(
            imsi_count_type=None,
            discount_parameters=[
                DiscountParameterDictFactory(
                    calculation_type=DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED.name,
                ),
            ],
        )

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        assert discount_dto.imsi_count_type == IMSICountTypeEnum.DATA


class TestAdjustIfModelIsSoPFinancial:
    def test_when_model_is_not_sop_financial(self):
        external_discount = DiscountDictFactory(
            service_types=(ServiceTypeEnum.DATA.name,),
            discount_parameters=[
                DiscountParameterDictFactory(
                    calculation_type=DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED.name,
                ),
            ],
        )

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(external_discount, True, True)

        assert discount_dto.service_types == (ServiceTypeEnum.DATA,)

    @pytest.mark.parametrize(
        "direction, service_types, include_inbound, include_outbound, expected_service_types",
        (
            [
                DiscountDirectionEnum.INBOUND,
                (ServiceTypeEnum.DATA,),
                True,
                False,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
            ],
            [
                DiscountDirectionEnum.INBOUND,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
                True,
                False,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
            ],
            [DiscountDirectionEnum.INBOUND, (ServiceTypeEnum.DATA,), False, False, (ServiceTypeEnum.DATA,)],
            [
                DiscountDirectionEnum.INBOUND,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
                False,
                False,
                (ServiceTypeEnum.DATA,),
            ],
            [
                DiscountDirectionEnum.INBOUND,
                (ServiceTypeEnum.ACCESS_FEE,),
                False,
                False,
                (ServiceTypeEnum.ACCESS_FEE,),
            ],
            [
                DiscountDirectionEnum.OUTBOUND,
                (ServiceTypeEnum.DATA,),
                False,
                True,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
            ],
            [
                DiscountDirectionEnum.OUTBOUND,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
                False,
                True,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
            ],
            [DiscountDirectionEnum.OUTBOUND, (ServiceTypeEnum.DATA,), False, False, (ServiceTypeEnum.DATA,)],
            [
                DiscountDirectionEnum.OUTBOUND,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
                False,
                False,
                (ServiceTypeEnum.DATA,),
            ],
            [
                DiscountDirectionEnum.OUTBOUND,
                (ServiceTypeEnum.ACCESS_FEE,),
                False,
                False,
                (ServiceTypeEnum.ACCESS_FEE,),
            ],
            [
                DiscountDirectionEnum.BIDIRECTIONAL,
                (ServiceTypeEnum.DATA,),
                False,
                True,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
            ],
            [
                DiscountDirectionEnum.BIDIRECTIONAL,
                (ServiceTypeEnum.DATA,),
                True,
                False,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
            ],
            [
                DiscountDirectionEnum.BIDIRECTIONAL,
                (ServiceTypeEnum.DATA,),
                True,
                True,
                (ServiceTypeEnum.DATA, ServiceTypeEnum.ACCESS_FEE),
            ],
            [DiscountDirectionEnum.BIDIRECTIONAL, (ServiceTypeEnum.DATA,), False, False, (ServiceTypeEnum.DATA,)],
        ),
    )
    def test_discount_is_adjusted(
        self,
        direction: DiscountDirectionEnum,
        service_types: tuple[ServiceTypeEnum],
        include_inbound: bool,
        include_outbound: bool,
        expected_service_types: tuple[ServiceTypeEnum],
    ):
        external_discount = DiscountDictFactory(
            direction=direction.name,
            service_types=tuple(s.name for s in service_types),
            discount_parameters=[
                DiscountParameterDictFactory(
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL.name,
                ),
            ],
        )

        discount_dto = DomainDiscountDTOFactory.create_from_external_discount(
            external_discount,
            include_access_fee_in_sop_financial_inbound=include_inbound,
            include_access_fee_in_sop_financial_outbound=include_outbound,
        )

        assert sorted(discount_dto.service_types) == sorted(expected_service_types)
