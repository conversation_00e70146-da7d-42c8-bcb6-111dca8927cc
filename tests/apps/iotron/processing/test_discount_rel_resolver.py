from datetime import date
from functools import partial

from nga.apps.agreements.enums import DiscountCalculationTypeEnum, DiscountDirectionEnum
from nga.apps.iotron.processing.discount_rel_resolver import DiscountRelationResolver
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryDiscountRepository
from tests.factories.agreements.domain import DiscountFactory
from tests.factories.references import OperatorFactory


class TestDiscountRelationResolver:
    resolver_cls = DiscountRelationResolver

    agreement_id = 55

    home_operators = (4, 5)
    partner_operators = (6, 7)
    period = DatePeriod(date(2024, 1, 1), date(2024, 7, 1))
    service_types = (ServiceTypeEnum.DATA,)

    d_factory = staticmethod(
        partial(
            DiscountFactory,
            agreement_id=agreement_id,
            home_operators=home_operators,
            partner_operators=partner_operators,
            period=period,
            service_types=service_types,
            model_type=None,
        )
    )

    def test_sets_sub_discount_relation(self):
        discount_1 = self.d_factory(
            direction=DiscountDirectionEnum.INBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
        )
        discount_2 = self.d_factory(
            direction=DiscountDirectionEnum.INBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )

        discount_repository = InMemoryDiscountRepository([discount_1, discount_2])

        rel_resolver = self.resolver_cls(discount_repository)

        rel_resolver.resolve_sub_discount_rel(self.agreement_id)

        parent_discount = discount_repository.get_by_id(discount_1.id)

        assert parent_discount.is_parent is True
        assert parent_discount.has_sub_discounts is True
        assert discount_2 in parent_discount.sub_discounts

    def test_sub_discount_should_not_be_set_by_diff_direction(self):
        discount_1 = self.d_factory(
            direction=DiscountDirectionEnum.INBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
        )
        discount_2 = self.d_factory(
            direction=DiscountDirectionEnum.OUTBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )

        discount_repository = InMemoryDiscountRepository([discount_1, discount_2])

        rel_resolver = self.resolver_cls(discount_repository)

        rel_resolver.resolve_sub_discount_rel(self.agreement_id)

        resolved_discounts = discount_repository.get_many(self.agreement_id)

        assert all(d.has_sub_discounts is False for d in resolved_discounts)

    def test_sub_discount_should_not_be_set_by_diff_operators(self):
        discount_1 = self.d_factory(
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
        )
        discount_2 = self.d_factory(
            home_operators=(7, 8),  # full diff
            partner_operators=(6, 7),
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )
        discount_3 = self.d_factory(
            home_operators=(4, 5),
            partner_operators=(8, 9),  # full diff
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )

        discount_repository = InMemoryDiscountRepository([discount_1, discount_2, discount_3])

        rel_resolver = self.resolver_cls(discount_repository)

        rel_resolver.resolve_sub_discount_rel(self.agreement_id)

        resolved_discounts = discount_repository.get_many(self.agreement_id)

        assert all(d.has_sub_discounts is False for d in resolved_discounts)

    def test_sub_discount_should_be_set_by_partly_diff_operators(self):
        home_1 = OperatorFactory(pmn_code="HOME1")
        home_2 = OperatorFactory(pmn_code="HOME2")
        home_3 = OperatorFactory(pmn_code="HOME3")

        partner_1 = OperatorFactory(pmn_code="TEST1")
        partner_2 = OperatorFactory(pmn_code="TEST2")
        partner_3 = OperatorFactory(pmn_code="TEST3")

        discount_1 = self.d_factory(
            home_operators=[home_1.id, home_3.id],
            partner_operators=[partner_1.id, partner_2.id],
            direction=DiscountDirectionEnum.INBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
        )
        discount_2 = self.d_factory(
            home_operators=[home_1.id, home_2.id],
            partner_operators=[partner_1.id, partner_2.id, partner_3.id],
            direction=DiscountDirectionEnum.INBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )

        discount_repository = InMemoryDiscountRepository([discount_1, discount_2])

        rel_resolver = self.resolver_cls(discount_repository)

        rel_resolver.resolve_sub_discount_rel(self.agreement_id)

        resolved_discounts = discount_repository.get_many(self.agreement_id)

        sop_financial_discount = [d for d in resolved_discounts if d.id == discount_1.id][0]

        assert len(sop_financial_discount.sub_discounts) == 1

        sub_discount = sop_financial_discount.sub_discounts[0]
        assert sub_discount.home_operators == (home_1.id,)
        assert sorted(sub_discount.partner_operators) == sorted([partner_1.id, partner_2.id])

        sre_discounts = sorted([d for d in resolved_discounts if d.id != discount_1.id], key=lambda d: d.id)

        sre_discount_1 = sre_discounts[0]
        assert sre_discount_1.home_operators == (home_2.id,)
        assert sorted(sre_discount_1.partner_operators) == sorted([partner_1.id, partner_2.id])

        sre_discount_2 = sre_discounts[1]
        assert sorted(sre_discount_2.home_operators) == sorted([home_1.id, home_2.id])
        assert sre_discount_2.partner_operators == (partner_3.id,)

    def test_single_direction_discount_with_bidirectional_sub_discount_is_split(self):
        discount_1 = self.d_factory(
            direction=DiscountDirectionEnum.INBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
        )
        discount_2 = self.d_factory(
            direction=DiscountDirectionEnum.BIDIRECTIONAL,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )

        discount_repository = InMemoryDiscountRepository([discount_1, discount_2])

        rel_resolver = self.resolver_cls(discount_repository)

        rel_resolver.resolve_sub_discount_rel(self.agreement_id)

        parent_discounts = discount_repository.get_many(self.agreement_id)

        assert len(parent_discounts) == 2

        # inbound parent
        inbound_discount = next(d for d in parent_discounts if d.direction == DiscountDirectionEnum.INBOUND)
        assert inbound_discount.has_sub_discounts is True
        assert inbound_discount.home_operators == self.home_operators
        assert inbound_discount.partner_operators == self.partner_operators
        assert inbound_discount.period == self.period
        assert len(inbound_discount.parameters) == 1
        assert inbound_discount.parameters[0].calculation_type == DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL

        assert len(inbound_discount.sub_discounts) == 1

        # inbound sub-discount
        inbound_sub_discount = inbound_discount.sub_discounts[0]
        assert inbound_sub_discount.home_operators == self.home_operators
        assert inbound_sub_discount.partner_operators == self.partner_operators
        assert inbound_sub_discount.period == self.period
        assert inbound_sub_discount.direction == DiscountDirectionEnum.INBOUND
        assert len(inbound_sub_discount.parameters) == 1
        assert inbound_sub_discount.parameters[0].calculation_type == DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE

        # outbound parent
        outbound_discount = next(d for d in parent_discounts if d.direction == DiscountDirectionEnum.OUTBOUND)
        assert outbound_discount.has_sub_discounts is False
        assert outbound_discount.home_operators == self.home_operators
        assert outbound_discount.partner_operators == self.partner_operators
        assert outbound_discount.period == self.period
        assert len(outbound_discount.parameters) == 1
        assert outbound_discount.parameters[0].calculation_type == DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE

    def test_single_direction_discount_with_multiple_diff_direction_sub_discounts_is_split(self):
        discount_1 = self.d_factory(
            direction=DiscountDirectionEnum.INBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
        )
        discount_2 = self.d_factory(
            direction=DiscountDirectionEnum.BIDIRECTIONAL,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )
        discount_3 = self.d_factory(
            direction=DiscountDirectionEnum.INBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )

        discount_repository = InMemoryDiscountRepository([discount_1, discount_2, discount_3])

        rel_resolver = self.resolver_cls(discount_repository)

        rel_resolver.resolve_sub_discount_rel(self.agreement_id)

        parent_discounts = discount_repository.get_many(self.agreement_id)

        assert len(parent_discounts) == 2

        # inbound parent
        inbound_discount = next(d for d in parent_discounts if d.direction == DiscountDirectionEnum.INBOUND)
        assert inbound_discount.parameters[0].calculation_type == DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL

        assert len(inbound_discount.sub_discounts) == 2
        assert all(sb.direction == DiscountDirectionEnum.INBOUND for sb in inbound_discount.sub_discounts)
        assert all(
            sb.parameters[0].calculation_type == DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE
            for sb in inbound_discount.sub_discounts
        )

        # outbound parent
        outbound_discount = next(d for d in parent_discounts if d.direction == DiscountDirectionEnum.OUTBOUND)
        assert outbound_discount.has_sub_discounts is False
        assert outbound_discount.parameters[0].calculation_type == DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE

    def test_bidirectional_discount_with_single_direction_sub_discount_is_not_split(self):
        discount_1 = self.d_factory(
            direction=DiscountDirectionEnum.BIDIRECTIONAL,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
        )
        discount_2 = self.d_factory(
            direction=DiscountDirectionEnum.INBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )

        discount_repository = InMemoryDiscountRepository([discount_1, discount_2])

        rel_resolver = self.resolver_cls(discount_repository)

        rel_resolver.resolve_sub_discount_rel(self.agreement_id)

        parent_discounts = discount_repository.get_many(self.agreement_id)

        assert len(parent_discounts) == 1

        parent_discount = parent_discounts[0]

        assert parent_discount.has_sub_discounts is True
        assert len(parent_discount.sub_discounts) == 1

    def test_bidirectional_discount_with_bidirectional_sub_discount_is_not_split(self):
        discount_1 = self.d_factory(
            direction=DiscountDirectionEnum.BIDIRECTIONAL,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
        )
        discount_2 = self.d_factory(
            direction=DiscountDirectionEnum.BIDIRECTIONAL,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )

        discount_repository = InMemoryDiscountRepository([discount_1, discount_2])

        rel_resolver = self.resolver_cls(discount_repository)

        rel_resolver.resolve_sub_discount_rel(self.agreement_id)

        parent_discounts = discount_repository.get_many(self.agreement_id)

        assert len(parent_discounts) == 1

        parent_discount = parent_discounts[0]

        assert parent_discount.has_sub_discounts is True
        assert len(parent_discount.sub_discounts) == 1

    def test_without_discounts(self):
        discount_repository = InMemoryDiscountRepository()

        rel_resolver = self.resolver_cls(discount_repository)

        rel_resolver.resolve_sub_discount_rel(self.agreement_id)

        assert len(discount_repository.discounts) == 0

    def test_two_single_direction_sop_financials_with_bidirectional_sub_discounts(self):
        sop_in = self.d_factory(
            direction=DiscountDirectionEnum.INBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
        )
        sop_out = self.d_factory(
            direction=DiscountDirectionEnum.OUTBOUND,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
        )

        sre1 = self.d_factory(
            direction=DiscountDirectionEnum.BIDIRECTIONAL,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )
        sre2 = self.d_factory(
            direction=DiscountDirectionEnum.BIDIRECTIONAL,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )
        sre3 = self.d_factory(
            direction=DiscountDirectionEnum.BIDIRECTIONAL,
            parameters__0__calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
        )

        discount_repository = InMemoryDiscountRepository(
            [
                sop_in,
                sop_out,
                sre1,
                sre2,
                sre3,
            ]
        )

        rel_resolver = self.resolver_cls(discount_repository)

        rel_resolver.resolve_sub_discount_rel(self.agreement_id)

        parent_discounts = discount_repository.get_many(self.agreement_id)

        assert len(parent_discounts) == 2

        parent_sop_in = next(d for d in parent_discounts if d.direction == DiscountDirectionEnum.INBOUND)
        assert parent_sop_in.parameters[0].calculation_type == DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL
        assert parent_sop_in.has_sub_discounts is True

        assert all(sb.direction == DiscountDirectionEnum.INBOUND for sb in parent_sop_in.sub_discounts)
        assert all(
            sb.parameters[0].calculation_type == DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE
            for sb in parent_sop_in.sub_discounts
        )

        parent_sop_out = next(d for d in parent_discounts if d.direction == DiscountDirectionEnum.OUTBOUND)
        assert parent_sop_out.parameters[0].calculation_type == DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL
        assert parent_sop_out.has_sub_discounts is True

        assert all(sb.direction == DiscountDirectionEnum.OUTBOUND for sb in parent_sop_out.sub_discounts)
        assert all(
            sb.parameters[0].calculation_type == DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE
            for sb in parent_sop_out.sub_discounts
        )
