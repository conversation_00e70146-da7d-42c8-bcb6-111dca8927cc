from datetime import date
from decimal import Decimal
from functools import partial

from nga.apps.agreements.enums import DiscountDirectionEnum, DiscountQualifyingBasisEnum
from nga.apps.iotron.processing.discount_qualifying_upper_bound_filler import DiscountQualifyingUpperBoundFiller
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryDiscountRepository
from tests.factories.agreements.domain import DiscountFactory, DiscountQualifyingRuleFactory


class TestDiscountQualifyingUpperBoundFiller:
    resolver_cls = DiscountQualifyingUpperBoundFiller

    agreement_id = 55

    home_operators = (4, 5)
    partner_operators = (6, 7)
    period = DatePeriod(date(2024, 1, 1), date(2024, 7, 1))
    service_types = (ServiceTypeEnum.DATA,)

    d_factory = staticmethod(
        partial(
            DiscountFactory,
            agreement_id=agreement_id,
            direction=DiscountDirectionEnum.INBOUND,
            home_operators=home_operators,
            partner_operators=partner_operators,
            period=period,
            service_types=service_types,
            model_type=None,
        )
    )

    def test_fill_qualifying_upper_bound(self):
        qr_factory = partial(
            DiscountQualifyingRuleFactory,
            direction=DiscountDirectionEnum.INBOUND.name,
            service_types=[ServiceTypeEnum.SMS_MO.name],
            basis=DiscountQualifyingBasisEnum.MARKET_SHARE_PERCENTAGE.name,
            upper_bound=None,
        )

        discount_1 = self.d_factory(qualifying_rule=qr_factory(lower_bound=Decimal("100")))

        discount_2 = self.d_factory(qualifying_rule=qr_factory(lower_bound=Decimal("1000")))

        discount_3 = self.d_factory(qualifying_rule=qr_factory(lower_bound=Decimal("0")))

        discount_4 = self.d_factory(qualifying_rule=qr_factory(lower_bound=Decimal("5000")))

        discount_5 = self.d_factory(  # Noize
            qualifying_rule=qr_factory(
                basis=DiscountQualifyingBasisEnum.VOLUME.name,
                lower_bound=Decimal("200"),
            )
        )

        discounts = [discount_1, discount_2, discount_3, discount_4, discount_5]

        discount_repository = InMemoryDiscountRepository(discounts)

        upper_bound_filler = self.resolver_cls(discount_repository)

        upper_bound_filler.fill_qualifying_upper_bound(discounts)

        assert discount_1.qualifying_rule.upper_bound == Decimal("1000")
        assert discount_2.qualifying_rule.upper_bound == Decimal("5000")
        assert discount_3.qualifying_rule.upper_bound == Decimal("100")
        assert discount_4.qualifying_rule.upper_bound is None
