from datetime import date
from decimal import Decimal
from functools import partial

from nga.apps.agreements.enums import DiscountBasisEnum, DiscountCalculationTypeEnum, DiscountDirectionEnum
from nga.apps.iotron.processing.discount_parent_unifier import DiscountParentUnifier
from nga.core.enums import CallDestinationEnum, ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryDiscountRepository
from tests.factories.agreements.domain import (
    CommitmentDistributionParameterFactory,
    DiscountFactory,
    DiscountParameterFactory,
)


class TestDiscountParentUnifier:
    unifier_cls = DiscountParentUnifier

    agreement_id = 77

    home_operators = (2, 3)
    partner_operators = (12, 13)
    period = DatePeriod(date(2024, 1, 1), date(2024, 7, 1))
    service_types = (ServiceTypeEnum.VOICE_MT,)

    d_factory = staticmethod(
        partial(
            DiscountFactory,
            agreement_id=agreement_id,
            home_operators=home_operators,
            partner_operators=partner_operators,
            period=period,
            service_types=service_types,
            direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
            call_destinations=[CallDestinationEnum.LOCAL.name, CallDestinationEnum.HOME.name],
            model_type=None,
        )
    )

    def test_unify_and_combine_parent_discount_into_one(self, override_deps):
        discount_1 = self.d_factory(
            parameters=[
                DiscountParameterFactory(
                    basis=DiscountBasisEnum.VALUE,
                    lower_bound=Decimal("3.3333"),
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
                )
            ]
        )

        discount_2 = self.d_factory(
            parameters=[
                DiscountParameterFactory(
                    basis=DiscountBasisEnum.VALUE,
                    lower_bound=Decimal("2.5423"),
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
                )
            ]
        )

        discount_3 = self.d_factory(
            service_types=[ServiceTypeEnum.DATA],
            parameters=[
                DiscountParameterFactory(
                    basis=DiscountBasisEnum.VALUE,
                    lower_bound=Decimal("0.87223"),
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
                )
            ],
        )

        discount_repository = InMemoryDiscountRepository([discount_1, discount_2, discount_3])

        assert len(discount_repository.get_many(agreement_id=self.agreement_id)) == 3

        discount_parent_unifier = DiscountParentUnifier(discount_repository)

        discount_parent_unifier.unify_parent_discounts(self.agreement_id)

        assert len(discount_repository.get_many(agreement_id=self.agreement_id)) == 2

        unified_discount = discount_repository.get_many(
            agreement_id=self.agreement_id, service_types=self.service_types
        )[0]

        assert len(unified_discount.commitment_distribution_parameters) == 2

        assert sorted(unified_discount.commitment_distribution_parameters, key=lambda x: x.charge) == [
            CommitmentDistributionParameterFactory(
                home_operators=self.home_operators,
                partner_operators=self.partner_operators,
                charge=Decimal("2.5423"),
            ),
            CommitmentDistributionParameterFactory(
                home_operators=self.home_operators,
                partner_operators=self.partner_operators,
                charge=Decimal("3.3333"),
            ),
        ]
