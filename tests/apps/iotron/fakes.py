from typing import Optional

from nga.apps.iotron.domain.repositories.external_agreements import AbstractExternalAgreementRepository
from nga.apps.iotron.enums import ExternalAgreementProcessingStatusEnum
from nga.apps.iotron.infra.orm.models import ExternalAgreement


class InMemoryExternalAgreementRepository(AbstractExternalAgreementRepository):
    def __init__(self, agreements: list[ExternalAgreement]) -> None:
        self._agreements = agreements

    def save(self, external_agreement: ExternalAgreement) -> ExternalAgreement:
        self._agreements.remove(external_agreement)
        self._agreements.append(external_agreement)

        return external_agreement

    def get_by_id(self, external_agreement_id: int) -> ExternalAgreement:
        return next(a for a in self._agreements if a.id == external_agreement_id)

    def get_many(
        self,
        ids: Optional[list[int]] = None,
        processing_status: Optional[ExternalAgreementProcessingStatusEnum] = None,
    ) -> list[ExternalAgreement]:
        return self._agreements
