from datetime import date
from decimal import Decimal
from functools import partial
from typing import Any

import pytest

from nga.apps.agreements.enums import DiscountDirectionEnum
from nga.apps.iotron.submit.consts import (
    IOTRON_ALL_PMNS_VALUE,
    IOTRON_DEFAULT_AGREEMENT_STATUS,
    IOTRON_DEFAULT_AGREEMENT_TYPE,
    IOTRON_NO_VALUE,
    IOTRON_YES_VALUE,
    IOTRONDiscountCalculationTypeEnum,
)
from nga.apps.iotron.submit.mappers.agreement import IOTRONAgreementMapper
from nga.apps.iotron.submit.nga_agreement import NGAAgreement
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryAgreementNegotiatorRepository
from tests.apps.references.fakes import (
    InMemoryCountryProvider,
    InMemoryOperatorProvider,
    InMemoryTrafficSegmentProvider,
)
from tests.factories.agreements import BudgetAgreementFactory
from tests.factories.agreements.domain import (
    AgreementNegotiatorFactory,
    CommitmentDistributionParameterFactory,
    SoPFinancialDiscountFactory,
    SREDiscountFactory,
)
from tests.factories.iotron.submit import NGAAgreementFactory
from tests.factories.references import OperatorFactory


class TestAgreementMapper:
    mapper_cls = IOTRONAgreementMapper

    def setup_method(self, method):
        self.hpmn = OperatorFactory()
        self.ppmn = OperatorFactory()

        self.operator_provider = InMemoryOperatorProvider([self.hpmn, self.ppmn])

        self.country_provider = InMemoryCountryProvider()
        self.traffic_segment_provider = InMemoryTrafficSegmentProvider()
        self.negotiator_repository = InMemoryAgreementNegotiatorRepository([])

    def create_mapper(self, **kwargs: Any) -> IOTRONAgreementMapper:
        default_params = dict(
            operator_provider=self.operator_provider,
            traffic_segment_provider=self.traffic_segment_provider,
            negotiator_repository=self.negotiator_repository,
            country_provider=self.country_provider,
        )

        params = {**default_params, **kwargs}

        return self.mapper_cls(**params)

    def test_map_agreement_parameters(self):
        hpmn1, hpmn2, ppmn = OperatorFactory.create_batch(size=3)

        budget_agreement = BudgetAgreementFactory(
            home_operators=[hpmn1.id, hpmn2.id],
            partner_operators=[ppmn.id],
            period=DatePeriod(date(2024, 11, 1), date(2024, 12, 1)),
        )

        mapper = self.create_mapper(operator_provider=InMemoryOperatorProvider([hpmn1, hpmn2, ppmn]))

        nga_agreement = NGAAgreement(budget_agreement, discounts=())

        iotron_agreement = mapper.map(nga_agreement)

        assert iotron_agreement.agreement_id == budget_agreement.id
        assert iotron_agreement.agreement_type == IOTRON_DEFAULT_AGREEMENT_TYPE
        assert iotron_agreement.agreement_reference == budget_agreement.name

        assert iotron_agreement.status == IOTRON_DEFAULT_AGREEMENT_STATUS

        assert iotron_agreement.agreement_start_date == budget_agreement.period.start_date
        assert iotron_agreement.agreement_end_date == budget_agreement.period.end_date

        assert iotron_agreement.home_operators == [hpmn1.pmn_code, hpmn2.pmn_code]
        assert iotron_agreement.partner_operators == [ppmn.pmn_code]

    @pytest.mark.parametrize(
        "include_satellite,expected_value",
        [
            (True, IOTRON_YES_VALUE),
            (False, IOTRON_NO_VALUE),
        ],
    )
    def test_map_include_satellite_field(self, include_satellite: bool, expected_value: str):
        budget_agreement = BudgetAgreementFactory(
            home_operators=[self.hpmn.id],
            partner_operators=[self.ppmn.id],
            include_satellite=include_satellite,
        )

        mapper = self.create_mapper()

        nga_agreement = NGAAgreement(budget_agreement, discounts=())

        iotron_agreement = mapper.map(nga_agreement)

        assert iotron_agreement.include_satellite == expected_value

    @pytest.mark.parametrize(
        "include_premium,include_premium_in_commitment,expected_premium,expected_commitment_premium",
        [
            (True, True, IOTRON_YES_VALUE, IOTRON_YES_VALUE),
            (True, False, IOTRON_YES_VALUE, IOTRON_NO_VALUE),
            (False, True, IOTRON_NO_VALUE, IOTRON_YES_VALUE),
            (False, False, IOTRON_NO_VALUE, IOTRON_NO_VALUE),
        ],
    )
    def test_map_premium_numbers(
        self,
        include_premium: bool,
        include_premium_in_commitment: bool,
        expected_premium: str,
        expected_commitment_premium: str,
    ):
        budget_agreement = BudgetAgreementFactory(
            home_operators=[self.hpmn.id],
            partner_operators=[self.ppmn.id],
            include_premium=include_premium,
            include_premium_in_commitment=include_premium_in_commitment,
        )

        mapper = self.create_mapper()

        nga_agreement = NGAAgreement(budget_agreement, discounts=())

        iotron_agreement = mapper.map(nga_agreement)

        assert iotron_agreement.premium_numbers.in_discount_rate == expected_premium
        assert iotron_agreement.premium_numbers.in_commitment == expected_commitment_premium

    def test_map_empty_negotiator(self):
        budget_agreement = BudgetAgreementFactory(
            home_operators=[self.hpmn.id],
            partner_operators=[self.ppmn.id],
            negotiator_id=None,
        )

        mapper = self.create_mapper()

        nga_agreement = NGAAgreement(budget_agreement, discounts=())

        iotron_agreement = mapper.map(nga_agreement)

        assert iotron_agreement.negotiator is None

    def test_map_negotiator(self):
        negotiator = AgreementNegotiatorFactory()

        budget_agreement = BudgetAgreementFactory(
            home_operators=[self.hpmn.id],
            partner_operators=[self.ppmn.id],
            negotiator_id=negotiator.id,
        )

        mapper = self.create_mapper(negotiator_repository=InMemoryAgreementNegotiatorRepository([negotiator]))

        nga_agreement = NGAAgreement(budget_agreement, discounts=())

        iotron_agreement = mapper.map(nga_agreement)

        assert iotron_agreement.negotiator.id == negotiator.id
        assert iotron_agreement.negotiator.name == negotiator.name

    @pytest.mark.parametrize(
        "discount_factory",
        [
            SREDiscountFactory,  # not SoP Financial
            partial(SoPFinancialDiscountFactory, service_types=(ServiceTypeEnum.VOICE_MO,)),  # no ACCESS_FEE
        ],
    )
    def test_map_include_access_fee_in_financial_commitment_empty(self, discount_factory):
        nga_agreement = NGAAgreementFactory(
            budget_agreement__home_operators=[self.hpmn.id],
            budget_agreement__partner_operators=[self.ppmn.id],
            discounts=(discount_factory(),),
        )

        mapper = self.create_mapper()

        iotron_agreement = mapper.map(nga_agreement)

        assert iotron_agreement.include_access_fee_in_financial_commitment.visitor_inbound == IOTRON_NO_VALUE
        assert iotron_agreement.include_access_fee_in_financial_commitment.customer_outbound == IOTRON_NO_VALUE

    def test_map_include_access_fee_in_financial_commitment_without_access(self):
        nga_agreement = NGAAgreementFactory(
            budget_agreement__home_operators=[self.hpmn.id],
            budget_agreement__partner_operators=[self.ppmn.id],
            discounts=(
                SoPFinancialDiscountFactory(
                    direction=DiscountDirectionEnum.INBOUND,
                    service_types=(ServiceTypeEnum.ACCESS_FEE,),
                ),
            ),
        )

        mapper = self.create_mapper()

        in_iotron_agreement = mapper.map(nga_agreement)
        assert in_iotron_agreement.include_access_fee_in_financial_commitment.visitor_inbound == IOTRON_YES_VALUE
        assert in_iotron_agreement.include_access_fee_in_financial_commitment.customer_outbound == IOTRON_NO_VALUE

        nga_agreement.discounts[0].direction = DiscountDirectionEnum.OUTBOUND

        out_iotron_agreement = mapper.map(nga_agreement)
        assert out_iotron_agreement.include_access_fee_in_financial_commitment.visitor_inbound == IOTRON_NO_VALUE
        assert out_iotron_agreement.include_access_fee_in_financial_commitment.customer_outbound == IOTRON_YES_VALUE

        nga_agreement.discounts[0].direction = DiscountDirectionEnum.BIDIRECTIONAL

        bi_iotron_agreement = mapper.map(nga_agreement)
        assert bi_iotron_agreement.include_access_fee_in_financial_commitment.visitor_inbound == IOTRON_YES_VALUE
        assert bi_iotron_agreement.include_access_fee_in_financial_commitment.customer_outbound == IOTRON_YES_VALUE

    def test_map_discounts(self):
        budget_agreement = BudgetAgreementFactory(home_operators=[self.hpmn.id], partner_operators=[self.ppmn.id])

        mapper = self.create_mapper()

        nga_agreement = NGAAgreement(budget_agreement, discounts=(SREDiscountFactory(),))

        iotron_agreement = mapper.map(nga_agreement)

        assert len(iotron_agreement.discounts) > 0

    def test_map_sub_discounts(self):
        budget_agreement = BudgetAgreementFactory(home_operators=[self.hpmn.id], partner_operators=[self.ppmn.id])

        mapper = self.create_mapper()

        discount = SoPFinancialDiscountFactory()

        assert len(discount.parameters) > 0

        assert discount.has_sub_discounts is True

        nga_agreement = NGAAgreement(budget_agreement, discounts=(discount,))

        iotron_agreement = mapper.map(nga_agreement)

        assert len(iotron_agreement.discounts) == 2  # SoP Financial discount parameter + SRE sub-discount parameter

    def test_map_sop_financial_discount(self):
        hpmn_1 = OperatorFactory()
        hpmn_2 = OperatorFactory()

        ppmn_1 = OperatorFactory()
        ppmn_2 = OperatorFactory()
        ppmn_3 = OperatorFactory()

        budget_agreement = BudgetAgreementFactory(
            home_operators=[hpmn_1.id, hpmn_2.id],
            partner_operators=[ppmn_1.id, ppmn_2.id, ppmn_3.id],
        )

        discount = SoPFinancialDiscountFactory(
            home_operators=[hpmn_1.id, hpmn_2.id],
            partner_operators=[ppmn_1.id, ppmn_2.id, ppmn_3.id],
            commitment_distribution_parameters=[
                CommitmentDistributionParameterFactory(
                    home_operators=[hpmn_1.id, hpmn_2.id],
                    partner_operators=[ppmn_1.id],
                    charge=Decimal("500"),
                ),
                CommitmentDistributionParameterFactory(
                    home_operators=[hpmn_1.id],
                    partner_operators=[ppmn_2.id, ppmn_3.id],
                    charge=Decimal("300"),
                ),
                CommitmentDistributionParameterFactory(
                    home_operators=[hpmn_2.id],
                    partner_operators=[ppmn_2.id, ppmn_3.id],
                    charge=Decimal("200"),
                ),
            ],
        )

        nga_agreement = NGAAgreement(budget_agreement, discounts=(discount,))

        mapper = self.create_mapper(
            operator_provider=InMemoryOperatorProvider([hpmn_1, hpmn_2, ppmn_1, ppmn_2, ppmn_3])
        )

        iotron_agreement = mapper.map(nga_agreement)

        assert len(iotron_agreement.discounts) == 4  # 3 SoP Financial discounts parameter + SRE sub-discount parameter

        sop_discounts = [
            d
            for d in iotron_agreement.discounts
            if d.calculation_type == IOTRONDiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL.value
        ]

        sop_discount_1 = [s for s in sop_discounts if s.bound_type.lower == float(500)][0]

        assert sop_discount_1.home_operators == IOTRON_ALL_PMNS_VALUE
        assert sop_discount_1.partner_operators == [ppmn_1.pmn_code]

        sop_discount_2 = [s for s in sop_discounts if s.bound_type.lower == float(300)][0]

        assert sop_discount_2.home_operators == [hpmn_1.pmn_code]
        assert sorted(sop_discount_2.partner_operators) == sorted([ppmn_2.pmn_code, ppmn_3.pmn_code])

        sop_discount_3 = [s for s in sop_discounts if s.bound_type.lower == float(200)][0]

        assert sop_discount_3.home_operators == [hpmn_2.pmn_code]
        assert sorted(sop_discount_3.partner_operators) == sorted([ppmn_2.pmn_code, ppmn_3.pmn_code])
