import pytest
from django.utils import timezone

from nga.apps.agreements.infra.repositories import AgreementNegotiatorDjangoORMRepository
from nga.apps.iotron.enums import ExternalAgreementProcessingStatusEnum
from nga.apps.iotron.exceptions import ExternalAgreementDoesNotExist
from nga.apps.iotron.infra.repositories.external_agreement import ExternalAgreementDjangoORMRepository
from nga.apps.references.infra.providers import CountryProvider, OperatorProvider, TrafficSegmentProvider
from nga.utils.collections import to_id_list
from tests.factories.agreements import AgreementNegotiatorORMFactory
from tests.factories.iotron import ExternalAgreementORMFactory
from tests.factories.iotron.dto import DiscountDictFactory
from tests.factories.references import (
    CountryORMFactory,
    OperatorORMFactory,
    TrafficSegmentORMFactory,
)


@pytest.fixture
def _repository() -> ExternalAgreementDjangoORMRepository:
    return ExternalAgreementDjangoORMRepository(
        operator_provider=OperatorProvider(),
        country_provider=CountryProvider(),
        traffic_segment_provider=TrafficSegmentProvider(),
        agreement_negotiator=AgreementNegotiatorDjangoORMRepository(),
    )


@pytest.mark.django_db
class TestExternalAgreementRepository:
    def test_get_by_id(self):
        home_operator = OperatorORMFactory()
        partner_operator = OperatorORMFactory()

        called_country = CountryORMFactory()
        traffic_segment = TrafficSegmentORMFactory(home_operator=home_operator)

        negotiator = AgreementNegotiatorORMFactory()

        repository = ExternalAgreementDjangoORMRepository(
            operator_provider=OperatorProvider(),
            country_provider=CountryProvider(),
            traffic_segment_provider=TrafficSegmentProvider(),
            agreement_negotiator=AgreementNegotiatorDjangoORMRepository(),
        )

        discount = DiscountDictFactory(
            home_operators=[home_operator.pmn_code],
            partner_operators=[partner_operator.pmn_code],
            called_countries=[called_country.code],
            traffic_segments=[f"{traffic_segment.home_operator.pmn_code}-{traffic_segment.name}"],
        )

        orm_record = ExternalAgreementORMFactory(
            include_satellite=False,
            include_premium=False,
            include_premium_in_commitment=False,
            is_rolling=False,
            discounts=[discount],
            negotiator=negotiator.name,
        )

        domain_record = repository.get_by_id(orm_record.id)

        assert orm_record.id == domain_record.id
        assert orm_record.name == domain_record.name
        assert orm_record.include_satellite == domain_record.include_satellite
        assert orm_record.include_premium == domain_record.include_premium
        assert orm_record.include_premium_in_commitment == domain_record.include_premium_in_commitment
        assert orm_record.is_rolling == domain_record.is_rolling

        assert negotiator.id == domain_record.negotiator

        domain_discount = domain_record.discounts[0]

        assert domain_discount["home_operators"] == [home_operator.id]
        assert domain_discount["partner_operators"] == [partner_operator.id]
        assert domain_discount["called_countries"] == [called_country.id]
        assert domain_discount["traffic_segments"] == [traffic_segment.id]

    def test_get_wrong_by_id(self, _repository):
        with pytest.raises(ExternalAgreementDoesNotExist):
            _repository.get_by_id(12)

    def test_get_many_by_status(self, _repository):
        orm_records = ExternalAgreementORMFactory.create_batch(
            3, processing_status=ExternalAgreementProcessingStatusEnum.NOT_PROCESSED
        )

        # noize
        ExternalAgreementORMFactory.create_batch(
            5, processing_status=ExternalAgreementProcessingStatusEnum.MOVED_TO_LIVE
        )

        records = _repository.get_many(processing_status=ExternalAgreementProcessingStatusEnum.NOT_PROCESSED)

        assert len(records) == len(orm_records)

    def test_get_many_by_ids(self, _repository):
        orm_records = ExternalAgreementORMFactory.create_batch(
            3, processing_status=ExternalAgreementProcessingStatusEnum.NOT_PROCESSED
        )

        # noize
        ExternalAgreementORMFactory.create_batch(
            5, processing_status=ExternalAgreementProcessingStatusEnum.MOVED_TO_LIVE
        )

        orm_records.extend(
            ExternalAgreementORMFactory.create_batch(
                4, processing_status=ExternalAgreementProcessingStatusEnum.INTERSECTED
            )
        )

        records = _repository.get_many(ids=to_id_list(orm_records))

        assert len(records) == len(orm_records)
        assert sorted(to_id_list(orm_records)) == sorted(to_id_list(records))

    def test_save(self, _repository):
        orm_record = ExternalAgreementORMFactory(
            processed_at=None,
            processing_status=ExternalAgreementProcessingStatusEnum.NOT_PROCESSED,
            failed_message="",
        )
        domain_record = _repository.get_by_id(orm_record.id)

        domain_record.processed_at = timezone.now()
        domain_record.processing_status = ExternalAgreementProcessingStatusEnum.FAILED
        domain_record.failed_message = "Failed message"
        domain_record.name = "New test External Agreement"

        _repository.save(domain_record)

        new_domain_record = _repository.get_by_id(orm_record.id)

        assert new_domain_record.id == domain_record.id
        assert new_domain_record.processed_at == domain_record.processed_at
        assert new_domain_record.processing_status == domain_record.processing_status
        assert new_domain_record.failed_message == domain_record.failed_message

        assert new_domain_record.name != domain_record.name
        assert new_domain_record.name == orm_record.name
