import pytest
from mediatr import Mediator

from nga.apps.iotron.commands import ProcessExternalAgreementCommand
from nga.apps.iotron.exceptions import ExternalAgreementDoesNotExist
from tests.apps.iotron.fakes import InMemoryExternalAgreementRepository
from tests.factories.iotron import ExternalAgreementFactory


class TestProcessExternalAgreementCommandHandler:

    def test_with_exists_external_agreement(self, external_agreement_service_mock, override_deps):
        external_agreement = ExternalAgreementFactory()

        cmd = ProcessExternalAgreementCommand(external_agreement.id, budget_id=48)

        with override_deps(
            external_agreement_service=external_agreement_service_mock,
            external_agreement_repository=InMemoryExternalAgreementRepository([external_agreement]),
        ):
            Mediator().send(cmd)

    @pytest.mark.django_db
    def test_with_non_exists_external_agreement(self):
        external_id = 76

        cmd = ProcessExternalAgreementCommand(external_id, budget_id=48)

        with pytest.raises(ExternalAgreementDoesNotExist) as exc_info:
            Mediator().send(cmd)

        assert exc_info.value.message == f"External Agreement does not exist id={external_id}"
