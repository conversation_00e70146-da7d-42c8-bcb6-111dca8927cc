from datetime import date
from typing import Type

import pytest
from django.contrib.auth.models import AnonymousUser, User
from django.test import RequestFactory
from django.urls import reverse_lazy
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.test import force_authenticate
from rest_framework.viewsets import GenericViewSet

from nga.apps.iot_rates.api.views import IOTRateFiltersAPIView
from nga.apps.iot_rates.enums import IOTRateTypeEnum
from nga.apps.references.api.serializers import CountrySerializer, OperatorSerializer
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum
from tests.factories.iot_rates.orm import IOTRateORMFactory
from tests.factories.references import CountryORMFactory, OperatorORMFactory


@pytest.mark.django_db
class TestIOTRateFiltersAPIView:
    view_class = IOTRateFiltersAPIView
    url = reverse_lazy("iot_rate_filters")

    def test_authentication_required(self, rf: RequestFactory):
        request = rf.get(self.url, content_type="application/json")
        request.user = AnonymousUser()

        response = self.perform_request(request)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @classmethod
    def perform_request(cls, request: Request, view_class: Type[GenericViewSet] = view_class) -> Response:
        return view_class.as_view()(request)

    def test_200_ok(self, rf: RequestFactory, staff_user: User):
        home_operators = OperatorORMFactory.create_batch(2)
        partner_operators = OperatorORMFactory.create_batch(3)

        called_countries = CountryORMFactory.create_batch(4)

        _ = [
            IOTRateORMFactory(
                home_operator=home_operators[0],
                partner_operator=partner_operators[1],
                start_date=date(2024, 1, 1),
                end_date=date(2025, 12, 31),
                traffic_direction=TrafficDirectionEnum.INBOUND,
                service_type=ServiceTypeEnum.SMS_MT,
                type=IOTRateTypeEnum.EU_REGULATED,
                called_countries=None,
                currency_code="USD",
                is_premium=True,
            ),
            IOTRateORMFactory(
                home_operator=home_operators[1],
                partner_operator=partner_operators[2],
                start_date=date(2023, 12, 1),
                end_date=date(2024, 6, 30),
                traffic_direction=TrafficDirectionEnum.OUTBOUND,
                service_type=ServiceTypeEnum.DATA,
                type=IOTRateTypeEnum.STANDARD,
                called_countries=called_countries[:4],
                currency_code="UAH",
                is_premium=None,
            ),
            IOTRateORMFactory(
                home_operator=home_operators[0],
                partner_operator=partner_operators[0],
                start_date=date(2020, 6, 1),
                end_date=date(2023, 12, 31),
                traffic_direction=TrafficDirectionEnum.INBOUND,
                service_type=ServiceTypeEnum.VOICE_MT,
                type=IOTRateTypeEnum.DISCOUNTED,
                called_countries=called_countries[2:5],
                currency_code="EUR",
                is_premium=False,
            ),
        ]

        response = self.get_iot_rate_filters(rf, staff_user)
        assert response.status_code == status.HTTP_200_OK

        response_data = response.data

        assert response_data["home_operators"] == OperatorSerializer(home_operators, many=True).data
        assert response_data["partner_operators"] == OperatorSerializer(partner_operators, many=True).data

        assert response_data["start_date_min"] == "2020-06"
        assert response_data["start_date_max"] == "2024-01"
        assert response_data["end_date_min"] == "2023-12"
        assert response_data["end_date_max"] == "2025-12"

        assert response_data["traffic_directions"] == [
            TrafficDirectionEnum.INBOUND.name,
            TrafficDirectionEnum.OUTBOUND.name,
        ]
        assert response_data["service_types"] == [
            ServiceTypeEnum.VOICE_MT.name,
            ServiceTypeEnum.SMS_MT.name,
            ServiceTypeEnum.DATA.name,
        ]

        assert response_data["called_countries"] == CountrySerializer(called_countries, many=True).data

        assert response_data["is_premium"] == [False, True, None]

        assert response_data["types"] == [
            IOTRateTypeEnum.STANDARD.name,
            IOTRateTypeEnum.EU_REGULATED.name,
            IOTRateTypeEnum.DISCOUNTED.name,
        ]
        assert response_data["currency_codes"] == ["EUR", "UAH", "USD"]

    def get_iot_rate_filters(
        self,
        rf: RequestFactory,
        user: User,
        view_class: Type[GenericViewSet] = view_class,
    ) -> Response:
        request = rf.get(self.url)
        request.user = user

        force_authenticate(request, user)

        response = self.perform_request(request, view_class)

        return response
