from datetime import date

from nga.apps.iot_rates.api.serializers import IOTRateFiltersSchemaSerializer
from nga.apps.iot_rates.enums import IOTRateTypeEnum
from nga.apps.references.api.serializers import CountrySerializer, OperatorSerializer
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum
from tests.factories.references import CountryFactory, OperatorFactory


class TestIOTRateFiltersSchemaSerializer:
    serializer_class = IOTRateFiltersSchemaSerializer

    def test_schema(self):
        expected_fields = (
            "home_operators",
            "partner_operators",
            "traffic_directions",
            "service_types",
            "called_countries",
            "is_premium",
            "start_date_min",
            "start_date_max",
            "end_date_min",
            "end_date_max",
            "types",
            "currency_codes",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_serialize(self):
        home_operators = OperatorFactory.create_batch(2)
        partner_operators = OperatorFactory.create_batch(2)
        called_countries = CountryFactory.create_batch(2)

        data = {
            "home_operators": OperatorSerializer(home_operators, many=True).data,
            "partner_operators": OperatorSerializer(partner_operators, many=True).data,
            "traffic_directions": [TrafficDirectionEnum.INBOUND.name, TrafficDirectionEnum.OUTBOUND.name],
            "service_types": [ServiceTypeEnum.DATA.name, ServiceTypeEnum.ACCESS_FEE.name],
            "called_countries": CountrySerializer(called_countries, many=True).data,
            "is_premium": [True, False],
            "start_date_min": date(year=2023, month=12, day=1),
            "start_date_max": date(year=2024, month=12, day=1),
            "end_date_min": date(year=2024, month=6, day=1),
            "end_date_max": date(year=2025, month=12, day=1),
            "types": [IOTRateTypeEnum.DISCOUNTED.name, IOTRateTypeEnum.STANDARD.name],
            "currency_codes": ["USD", "EUR"],
        }

        serializer_obj = self.serializer_class(data=data)
        serializer_obj.is_valid(raise_exception=True)
        serialized_data = serializer_obj.data

        assert serialized_data["home_operators"] == OperatorSerializer(home_operators, many=True).data
        assert serialized_data["partner_operators"] == OperatorSerializer(partner_operators, many=True).data
        assert serialized_data["start_date_min"] == "2023-12"
        assert serialized_data["start_date_max"] == "2024-12"
        assert serialized_data["end_date_min"] == "2024-06"
        assert serialized_data["end_date_max"] == "2025-12"
        assert serialized_data["traffic_directions"] == [
            TrafficDirectionEnum.INBOUND.name,
            TrafficDirectionEnum.OUTBOUND.name,
        ]
        assert serialized_data["service_types"] == [ServiceTypeEnum.DATA.name, ServiceTypeEnum.ACCESS_FEE.name]
        assert serialized_data["called_countries"] == CountrySerializer(called_countries, many=True).data
        assert serialized_data["is_premium"] == [True, False]
        assert serialized_data["types"] == [IOTRateTypeEnum.DISCOUNTED.name, IOTRateTypeEnum.STANDARD.name]
        assert serialized_data["currency_codes"] == ["USD", "EUR"]
