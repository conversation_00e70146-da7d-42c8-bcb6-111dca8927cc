import pytest

from nga.apps.calculation.iot_rates.eu_operator_django_orm_provider import EUOperatorDjangoORMProvider
from tests.factories.references.orm import EUOperatorORMFactory, OperatorORMFactory


@pytest.mark.django_db
class TestEUOperatorDjangoORMProvider:
    def test_get_many(self):
        op1 = EUOperatorORMFactory()
        op2 = EUOperatorORMFactory()

        OperatorORMFactory()
        OperatorORMFactory()

        provider = EUOperatorDjangoORMProvider()

        eu_operator_store = provider.get_many()

        assert eu_operator_store.operators == tuple([op1.operator_id, op2.operator_id])
