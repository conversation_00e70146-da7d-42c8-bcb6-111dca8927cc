from nga.apps.calculation.iot_rates.eu_operator_store import EUOperatorStore


class TestEUOperatorStore:
    def test_is_eu_operator(self):
        eu_operators = (34, 45, 53767)

        store = EUOperatorStore(eu_operators)

        assert store.is_eu_operator(34) is True
        assert store.is_eu_operator(45) is True

        assert store.is_eu_operator(-1) is False
        assert store.is_eu_operator(-2) is False
