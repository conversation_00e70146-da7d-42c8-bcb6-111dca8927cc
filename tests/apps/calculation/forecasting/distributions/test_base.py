from datetime import date
from decimal import Decimal
from functools import partial

from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.distributions.base import BaseForecastDistributionModel
from nga.apps.calculation.forecasting.dto import ForecastDistributionRecord, MonthlyForecastRecord
from nga.apps.forecasts.domain import ForecastRule
from nga.core.types import DatePeriod, Month
from tests.apps.forecasts.fakes import InMemoryBudgetTrafficProvider
from tests.factories.budgets import BudgetFactory, BudgetTrafficRecordFactory
from tests.factories.calculation.models import ForecastDistributionRecordFactory, MonthlyForecastRecordFactory
from tests.factories.forecasts import ManualVolumeForecastRuleFactory
from tests.utils import assert_decimal_equal


class _DistributionModel(BaseForecastDistributionModel):
    def get_calculation_budget_traffic(self, forecast_rule: ForecastRule) -> BudgetTraffic[BudgetTrafficRecord]:
        return BudgetTraffic[BudgetTrafficRecord](records=[])

    def calculate_with_budget_traffic(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
        historical_traffic: BudgetTraffic[BudgetTrafficRecord],
    ) -> tuple[ForecastDistributionRecord, ...]:

        result_records = []

        for r in historical_traffic.records:
            result_records.append(ForecastDistributionRecordFactory(traffic_month=r.traffic_month))

        return result_records


class TestBaseForecastDistributionModel:
    def test_get_full_budget_traffic_when_calculation_traffic_is_empty(self):
        hpmn, ppmn = 314, 564

        budget = BudgetFactory(
            home_operators=[hpmn],
            period=DatePeriod(date(2023, 1, 1), date(2026, 12, 1)),
            last_historical_month=date(2024, 6, 1),
        )

        forecast_rule = ManualVolumeForecastRuleFactory(
            home_operators=budget.home_operators,
            partner__operators_ids=[ppmn],
            period=DatePeriod.create_from_month(Month.create_from_year_month(2025, 7)),
            budget=budget,
        )

        tr_factory = staticmethod(
            partial(
                BudgetTrafficRecordFactory,
                home_operator_id=hpmn,
                partner_operator_id=ppmn,
                service_type=forecast_rule.service_type,
                traffic_direction=forecast_rule.traffic_direction,
            )
        )

        records = [
            tr_factory(traffic_month=date(2023, 5, 1)),
            tr_factory(traffic_month=date(2024, 5, 1)),
            tr_factory(traffic_month=date(2025, 5, 1)),
        ]

        budget_traffic_provider = InMemoryBudgetTrafficProvider(records=records)

        monthly_records = [
            MonthlyForecastRecordFactory(traffic_month=date(2025, 7, 1)),
        ]

        distribution_model = _DistributionModel(budget_traffic_provider=budget_traffic_provider)
        distribution_records = distribution_model.calculate(monthly_records, forecast_rule)

        assert len(distribution_records) == 1

    def test_calculate_without_traffic_when_budget_does_not_have_traffic_for_distribution_model(self):
        budget = BudgetFactory(
            period=DatePeriod(date(2023, 1, 1), date(2026, 12, 1)),
            last_historical_month=date(2024, 6, 1),
        )

        forecast_rule = ManualVolumeForecastRuleFactory(
            home_operators=[213],
            partner__operators_ids=[52, 45, 456],
            period=DatePeriod.create_from_month(Month.create_from_year_month(2025, 7)),
            budget=budget,
            volume=Decimal("100"),
        )

        budget_traffic_provider = InMemoryBudgetTrafficProvider(records=[])

        monthly_records = [
            MonthlyForecastRecordFactory(traffic_month=date(2025, 3, 1), volume_actual=Decimal("100")),
        ]

        distribution_model = _DistributionModel(budget_traffic_provider=budget_traffic_provider)
        distribution_records = distribution_model.calculate(monthly_records, forecast_rule)

        assert len(distribution_records) == len(forecast_rule.partner.operators_ids)

        for r in distribution_records:
            assert_decimal_equal(r.volume_actual, Decimal("33.333333"))
