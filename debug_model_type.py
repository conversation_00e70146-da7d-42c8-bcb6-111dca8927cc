#!/usr/bin/env python

import os
import sys
import django

# Add the project root to the Python path
sys.path.insert(0, '/home/<USER>/projects/nga/nga-api')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nga.infra.settings')
django.setup()

from decimal import Decimal
from nga.apps.agreements.domain.discount_model_type import evaluate_discount_model_type
from nga.apps.agreements.enums import DiscountBoundTypeEnum, DiscountCalculationTypeEnum, DiscountModelTypeEnum
from nga.core.enums import CallDestinationEnum
from tests.factories.agreements.domain import SREDiscountParameterFactory, SteppedTieredDiscountParameterFactory
from functools import partial
from tests.factories.agreements.domain import SREDiscountFactory
from nga.apps.agreements.enums import DiscountDirectionEnum
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from datetime import date

# Create the same discount factory as in the test
home_operators = (7, 8)
partner_operators = (9, 10)
period = DatePeriod(date(2025, 1, 1), date(2025, 10, 1))
service_types = (ServiceTypeEnum.VOICE_MO,)
direction = DiscountDirectionEnum.BIDIRECTIONAL

discount_factory = partial(
    SREDiscountFactory,
    home_operators=home_operators,
    partner_operators=partner_operators,
    direction=direction,
    service_types=service_types,
    period=period,
    traffic_segments=tuple(),
    above_commitment_rate=None,
    model_type=None,
)

# Create sub_discount_2 as in the test
sub_discount_2 = discount_factory(
    call_destinations=tuple(),
    parameters=[
        SREDiscountParameterFactory(
            bound_type=None,
            basis_value=Decimal("0.2"),
        ),
        SteppedTieredDiscountParameterFactory(
            bound_type=DiscountBoundTypeEnum.FINANCIAL_COMMITMENT,
            basis_value=Decimal("0.007"),
        ),
    ],
)

print("Original sub_discount_2:")
print(f"  Parameters count: {len(sub_discount_2.parameters)}")
for i, param in enumerate(sub_discount_2.parameters):
    print(f"  Parameter {i}: calculation_type={param.calculation_type}, bound_type={param.bound_type}")

print(f"  Model type: {sub_discount_2.model_type}")

# Test evaluate_discount_model_type on original discount
original_model_type = evaluate_discount_model_type(sub_discount_2)
print(f"  evaluate_discount_model_type result: {original_model_type}")

# Now simulate what the filler does - filter out stepped tiered parameters with financial commitment
valid_parameters = []
for discount_parameter in sub_discount_2.parameters:
    is_stepped_tiered = discount_parameter.calculation_type == DiscountCalculationTypeEnum.STEPPED_TIERED
    bound_is_financial = discount_parameter.bound_type == DiscountBoundTypeEnum.FINANCIAL_COMMITMENT
    
    if is_stepped_tiered and bound_is_financial:
        print(f"  Filtering out parameter: {discount_parameter.calculation_type}, {discount_parameter.bound_type}")
    else:
        valid_parameters.append(discount_parameter)

print(f"\nAfter filtering:")
print(f"  Valid parameters count: {len(valid_parameters)}")
for i, param in enumerate(valid_parameters):
    print(f"  Parameter {i}: calculation_type={param.calculation_type}, bound_type={param.bound_type}")

# Update the discount parameters
sub_discount_2.parameters = tuple(valid_parameters)

# Test evaluate_discount_model_type on filtered discount
filtered_model_type = evaluate_discount_model_type(sub_discount_2)
print(f"  evaluate_discount_model_type result after filtering: {filtered_model_type}")

# Let's also test the SREDiscountSetupSpecification directly
from nga.apps.agreements.domain.specifications.discount_setup import SREDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.utils import discount_fulfills_spec

sre_spec = SREDiscountSetupSpecification()
fulfills_sre = discount_fulfills_spec(sub_discount_2, sre_spec)
print(f"  Fulfills SRE specification: {fulfills_sre}")

if not fulfills_sre:
    try:
        sre_spec.verify(sub_discount_2)
    except Exception as e:
        print(f"  SRE verification error: {e}")
