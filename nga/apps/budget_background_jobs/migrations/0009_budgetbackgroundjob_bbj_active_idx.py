# Generated by Django 4.2 on 2024-11-01 13:20

from django.db import migrations, models
import nga.apps.budget_background_jobs.enums


class Migration(migrations.Migration):

    dependencies = [
        ('budget_background_jobs', '0008_budgetbackgroundjob_bbj_scheduled_idx'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='budgetbackgroundjob',
            index=models.Index(condition=models.Q(('status__in', (nga.apps.budget_background_jobs.enums.BudgetBackgroundJobStatusEnum['SCHEDULED'], nga.apps.budget_background_jobs.enums.BudgetBackgroundJobStatusEnum['STARTED']))), fields=['status', 'created_at'], include=('id', 'type', 'started_at'), name='bbj_active_idx'),
        ),
    ]
