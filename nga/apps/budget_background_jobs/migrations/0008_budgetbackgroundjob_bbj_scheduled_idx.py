# Generated by Django 4.2 on 2024-11-01 09:39

from django.db import migrations, models
import nga.apps.budget_background_jobs.enums


class Migration(migrations.Migration):

    dependencies = [
        ('budget_background_jobs', '0007_budgetbackgroundjob_params'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='budgetbackgroundjob',
            index=models.Index(condition=models.Q(('status', nga.apps.budget_background_jobs.enums.BudgetBackgroundJobStatusEnum['SCHEDULED'])), fields=['status'], name='bbj_scheduled_idx'),
        ),
    ]
