from dataclasses import dataclass
from typing import Any, Optional

from mediatr.mediator import GenericQuery

from nga.apps.budget_background_jobs import orm_models
from nga.apps.budget_background_jobs.dto import BudgetBackgroundJobError
from nga.apps.budget_background_jobs.enums import BudgetBackgroundJobTypeEnum


@dataclass
class ScheduleBudgetBackgroundJobCommand(GenericQuery):
    budget_id: int

    type: BudgetBackgroundJobTypeEnum

    task_params: dict[str, Any]


@dataclass
class StartBudgetBackgroundJobCommand(GenericQuery):
    budget_background_job: orm_models.BudgetBackgroundJob


@dataclass
class FinishBudgetBackgroundJobCommand(GenericQuery):
    budget_background_job_id: str

    error: Optional[BudgetBackgroundJobError]
