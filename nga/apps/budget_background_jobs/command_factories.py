from dataclasses import asdict
from typing import Optional

from nga.apps.budget_background_jobs.commands import ScheduleBudgetBackgroundJobCommand
from nga.apps.budget_background_jobs.enums import BudgetBackgroundJobTypeEnum
from nga.apps.forecasts.domain.dto import ForecastRuleSetup


class ScheduleBudgetBackgroundJobCommandFactory:
    @classmethod
    def bulk_copy_agreements(
        cls,
        budget_id: int,
        budget_agreements_to_copy: Optional[list[int]],
        source_budget_id: Optional[int],
        only_active: Optional[bool],
    ) -> ScheduleBudgetBackgroundJobCommand:
        cmd = ScheduleBudgetBackgroundJobCommand(
            budget_id=budget_id,
            type=BudgetBackgroundJobTypeEnum.COPY_AGREEMENTS,
            task_params=dict(
                target_budget_id=budget_id,
                budget_agreement_ids=budget_agreements_to_copy,
                source_budget_id=source_budget_id,
                only_active=only_active,
            ),
        )

        return cmd

    @classmethod
    def bulk_clone_agreements(
        cls,
        budget_id: int,
        budget_agreement_ids: list[int],
    ) -> ScheduleBudgetBackgroundJobCommand:
        cmd = ScheduleBudgetBackgroundJobCommand(
            budget_id=budget_id,
            type=BudgetBackgroundJobTypeEnum.CLONE_AGREEMENTS,
            task_params=dict(
                budget_id=budget_id,
                budget_agreement_ids=budget_agreement_ids,
            ),
        )

        return cmd

    @classmethod
    def bulk_activate_agreements(cls, budget_id: int) -> ScheduleBudgetBackgroundJobCommand:
        cmd = ScheduleBudgetBackgroundJobCommand(
            budget_id=budget_id,
            type=BudgetBackgroundJobTypeEnum.ACTIVATE_AGREEMENTS,
            task_params=dict(budget_id=budget_id),
        )

        return cmd

    @classmethod
    def bulk_deactivate_agreements(cls, budget_id: int) -> ScheduleBudgetBackgroundJobCommand:
        cmd = ScheduleBudgetBackgroundJobCommand(
            budget_id=budget_id,
            type=BudgetBackgroundJobTypeEnum.DEACTIVATE_AGREEMENTS,
            task_params=dict(budget_id=budget_id),
        )

        return cmd

    @classmethod
    def bulk_copy_forecast_rules(
        cls,
        target_budget_id: int,
        source_budget_id: int,
    ) -> ScheduleBudgetBackgroundJobCommand:
        cmd = ScheduleBudgetBackgroundJobCommand(
            budget_id=target_budget_id,
            type=BudgetBackgroundJobTypeEnum.COPY_FORECAST_RULES,
            task_params=dict(target_budget_id=target_budget_id, source_budget_id=source_budget_id),
        )

        return cmd

    @classmethod
    def bulk_delete_forecast_rules(cls, budget_id: int) -> ScheduleBudgetBackgroundJobCommand:
        cmd = ScheduleBudgetBackgroundJobCommand(
            budget_id=budget_id,
            type=BudgetBackgroundJobTypeEnum.DELETE_FORECAST_RULES,
            task_params=dict(budget_id=budget_id),
        )

        return cmd

    @classmethod
    def submit_agreements_to_iotron(
        cls,
        budget_id: int,
        budget_agreement_ids: list[int],
    ) -> ScheduleBudgetBackgroundJobCommand:
        cmd = ScheduleBudgetBackgroundJobCommand(
            budget_id=budget_id,
            type=BudgetBackgroundJobTypeEnum.SUBMIT_AGREEMENTS_TO_IOTRON,
            task_params=dict(budget_agreement_ids=budget_agreement_ids),
        )

        return cmd

    @classmethod
    def create_default_forecast_rules(
        cls,
        budget_id: int,
        setups: list[ForecastRuleSetup],
    ) -> ScheduleBudgetBackgroundJobCommand:

        cmd = ScheduleBudgetBackgroundJobCommand(
            budget_id=budget_id,
            type=BudgetBackgroundJobTypeEnum.CREATE_DEFAULT_FORECAST_RULES,
            task_params=dict(budget_id=budget_id, setups=[asdict(s) for s in setups]),
        )

        return cmd

    @classmethod
    def fill_budget_with_agreements(
        cls,
        budget_id: int,
        budget_agreement_ids: Optional[list[int]],
    ) -> ScheduleBudgetBackgroundJobCommand:
        cmd = ScheduleBudgetBackgroundJobCommand(
            budget_id=budget_id,
            type=BudgetBackgroundJobTypeEnum.FILL_BUDGET_WITH_AGREEMENTS,
            task_params=dict(budget_id=budget_id, budget_agreement_ids=budget_agreement_ids),
        )

        return cmd
