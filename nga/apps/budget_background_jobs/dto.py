from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from nga.apps.budget_background_jobs.enums import BudgetBackgroundJobStatusEnum, BudgetBackgroundJobTypeEnum


@dataclass
class BudgetBackgroundJob:
    id: str

    budget_id: int

    type: BudgetBackgroundJobTypeEnum

    status: BudgetBackgroundJobStatusEnum

    created_at: datetime

    started_at: datetime

    finished_at: datetime

    error_message: Optional[str]


@dataclass
class BudgetBackgroundJobError:
    message: str

    traceback: str
