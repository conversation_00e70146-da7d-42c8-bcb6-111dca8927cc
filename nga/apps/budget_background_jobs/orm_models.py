import uuid
from typing import Any

from django.db import models
from django.db.models import Q

from nga.apps.budget_background_jobs import dto
from nga.apps.budget_background_jobs.enums import BudgetBackgroundJobStatusEnum, BudgetBackgroundJobTypeEnum
from nga.core.enums import get_choices


class BudgetBackgroundJobQuerySet(models.QuerySet["BudgetBackgroundJob"]):
    def scheduled(self) -> models.QuerySet["BudgetBackgroundJob"]:
        return self.filter(status=BudgetBackgroundJobStatusEnum.SCHEDULED)

    def active(self) -> models.QuerySet["BudgetBackgroundJob"]:
        return self.filter(status__in=BudgetBackgroundJobStatusEnum.active())


class BudgetBackgroundJob(models.Model):
    """Model is used for keeping track of the Budget's background process."""

    id = models.UUIDField(default=uuid.uuid4, primary_key=True, editable=False, unique=True)

    task_id = models.CharField(max_length=36, null=True, blank=True)

    budget = models.ForeignKey(
        "budgets.Budget",
        on_delete=models.CASCADE,
        related_name="background_jobs",
        related_query_name="background_job",
    )

    type = models.PositiveSmallIntegerField(choices=get_choices(BudgetBackgroundJobTypeEnum))

    status = models.PositiveSmallIntegerField(choices=get_choices(BudgetBackgroundJobStatusEnum))

    params = models.JSONField(null=True)

    created_at = models.DateTimeField(auto_now_add=True)

    started_at = models.DateTimeField(null=True, blank=True)

    finished_at = models.DateTimeField(null=True, blank=True)

    error_message = models.CharField(max_length=256, null=True, blank=True)

    error_traceback = models.CharField(max_length=3000, null=True, blank=True)

    class Meta:
        db_table = "budget_background_jobs"

        indexes = [
            models.Index(
                name="bbj_scheduled_idx",
                fields=("status",),
                condition=Q(status=BudgetBackgroundJobStatusEnum.SCHEDULED),
            ),
            models.Index(
                name="bbj_active_idx",
                fields=("status", "created_at"),
                condition=Q(status__in=BudgetBackgroundJobStatusEnum.active()),
                include=("id", "type", "started_at"),
            ),
        ]

    objects = BudgetBackgroundJobQuerySet.as_manager()

    def to_dto(self) -> dto.BudgetBackgroundJob:
        return dto.BudgetBackgroundJob(
            id=str(self.id),
            budget_id=self.budget_id,
            type=BudgetBackgroundJobTypeEnum(self.type),
            status=BudgetBackgroundJobStatusEnum(self.status),
            created_at=self.created_at,
            started_at=self.started_at,
            finished_at=self.finished_at,
            error_message=self.error_message,
        )

    def get_params(self) -> dict[str, Any]:
        return self.params or {}
