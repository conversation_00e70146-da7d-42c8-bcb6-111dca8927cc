from django.apps import AppConfig


class BudgetBackgroundJobsConfig(AppConfig):
    name = "nga.apps.budget_background_jobs"

    verbose_name = "Budget Background Jobs"

    def ready(self) -> None:
        # wire package for injecting dependencies

        from nga.infra.di import di_container

        di_container.wire(
            # TODO: NGA-2155. Temporary hack. Add a proper method of Mediator initialization
            packages=[
                "nga.apps.budget_background_jobs.command_handlers",
            ],
            modules=[
                "nga.apps.budget_background_jobs.tasks",
            ],
        )
