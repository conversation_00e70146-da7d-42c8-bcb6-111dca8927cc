from enum import IntEnum


class BudgetBackgroundJobTypeEnum(IntEnum):
    COPY_AGREEMENTS = 1
    ACTIVATE_AGREEMENTS = 2
    DEACTIVATE_AGREEMENTS = 3

    COPY_FORECAST_RULES = 4
    DELETE_FORECAST_RULES = 5

    SUBMIT_AGREEMENTS_TO_IOTRON = 6

    CREATE_DEFAULT_FORECAST_RULES = 7

    FILL_BUDGET_WITH_AGREEMENTS = 8

    CLONE_AGREEMENTS = 9


class BudgetBackgroundJobStatusEnum(IntEnum):
    SCHEDULED = 1
    STARTED = 2
    FINISHED = 3
    FAILED = 4

    @classmethod
    def active(cls) -> tuple["BudgetBackgroundJobStatusEnum", ...]:
        return cls.SCHEDULED, cls.STARTED
