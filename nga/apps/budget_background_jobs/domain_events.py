from dataclasses import dataclass

from nga.apps.budget_background_jobs import dto
from nga.internal.domain import Event, EventName, EventPayload


@dataclass(kw_only=True)
class BudgetBackgroundJobScheduledEvent(Event):
    budget_background_job: dto.BudgetBackgroundJob

    @classmethod
    def get_event_name(cls) -> EventName:
        return "budget_background_jobs.BudgetBackgroundJobScheduled"

    def get_payload(self) -> EventPayload:
        return {
            "id": self.budget_background_job.id,
            "type": self.budget_background_job.type.name,
            "budget_id": self.budget_background_job.budget_id,
            "created_at": str(self.budget_background_job.created_at),
        }


@dataclass(kw_only=True)
class BudgetBackgroundJobFinishedEvent(Event):
    budget_background_job: dto.BudgetBackgroundJob

    @classmethod
    def get_event_name(cls) -> EventName:
        return "budget_background_jobs.BudgetBackgroundJobFinished"

    def get_payload(self) -> EventPayload:
        return {
            "id": self.budget_background_job.id,
            "type": self.budget_background_job.type.name,
            "budget_id": self.budget_background_job.budget_id,
            "finished_at": str(self.budget_background_job.finished_at),
        }


@dataclass(kw_only=True)
class BudgetBackgroundJobFailedEvent(Event):
    budget_background_job: dto.BudgetBackgroundJob

    @classmethod
    def get_event_name(cls) -> EventName:
        return "budget_background_jobs.BudgetBackgroundJobFailed"

    def get_payload(self) -> EventPayload:
        return {
            "id": self.budget_background_job.id,
            "type": self.budget_background_job.type.name,
            "budget_id": self.budget_background_job.budget_id,
            "failed_at": str(self.budget_background_job.finished_at),
            "error_message": self.budget_background_job.error_message,
        }
