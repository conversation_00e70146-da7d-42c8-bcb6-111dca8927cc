import logging

from dependency_injector.wiring import Provide, inject
from mediatr import Mediator

from nga.apps.budget_background_jobs.commands import FinishBudgetBackgroundJobCommand
from nga.apps.budget_background_jobs.domain_events import (
    BudgetBackgroundJobFailedEvent,
    BudgetBackgroundJobFinishedEvent,
)
from nga.apps.budget_background_jobs.enums import BudgetBackgroundJobStatusEnum, BudgetBackgroundJobTypeEnum
from nga.apps.budget_background_jobs.orm_models import BudgetBackgroundJob
from nga.internal.domain import Event
from nga.internal.messaging import EventStream
from nga.internal.messaging.topics import get_budget_topic_name
from nga.utils.dt import get_current_datetime_utc


@Mediator.handler
class FinishBudgetBackgroundJobCommandHandler:
    @inject
    def __init__(self, event_stream: EventStream = Provide["event_stream"]) -> None:
        self._event_stream = event_stream

    def handle(self, cmd: FinishBudgetBackgroundJobCommand) -> None:
        background_job = BudgetBackgroundJob.objects.get(pk=cmd.budget_background_job_id)

        background_job.finished_at = get_current_datetime_utc()

        if cmd.error is not None:
            background_job.status = BudgetBackgroundJobStatusEnum.FAILED
            background_job.error_message = cmd.error.message
            background_job.error_traceback = cmd.error.traceback

        else:
            background_job.status = BudgetBackgroundJobStatusEnum.FINISHED

        background_job.save()

        self._log_finish(background_job)

        self._publish_integration_event(background_job)

    def _log_finish(self, background_job: BudgetBackgroundJob) -> None:
        job_id = background_job.id
        job_type = BudgetBackgroundJobTypeEnum(background_job.type)
        budget_id = background_job.budget_id

        logging.info(f"Budget [id={budget_id}] background job [id={job_id}][type={job_type.name}] finished")

    def _publish_integration_event(self, background_job: BudgetBackgroundJob) -> None:
        topic_name = get_budget_topic_name(background_job.budget_id)

        bbj_dto = background_job.to_dto()

        if background_job.status == BudgetBackgroundJobStatusEnum.FAILED:
            event: Event = BudgetBackgroundJobFailedEvent(budget_background_job=bbj_dto)
        else:
            event = BudgetBackgroundJobFinishedEvent(budget_background_job=bbj_dto)

        self._event_stream.publish(topic_name, event)
