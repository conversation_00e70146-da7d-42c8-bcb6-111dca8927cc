from dependency_injector.wiring import Provide, inject
from mediatr import Mediator

from nga.apps.budget_background_jobs import dto, orm_models
from nga.apps.budget_background_jobs.commands import ScheduleBudgetBackgroundJobCommand
from nga.apps.budget_background_jobs.domain_events import BudgetBackgroundJobScheduledEvent
from nga.apps.budget_background_jobs.enums import BudgetBackgroundJobStatusEnum
from nga.internal.messaging import EventStream
from nga.internal.messaging.topics import get_budget_topic_name


@Mediator.handler
class ScheduleBudgetBackgroundJobCommandHandler:
    @inject
    def __init__(self, event_stream: EventStream = Provide["event_stream"]) -> None:
        self._event_stream = event_stream

    def handle(self, cmd: ScheduleBudgetBackgroundJobCommand) -> dto.BudgetBackgroundJob:
        orm_background_job = orm_models.BudgetBackgroundJob.objects.create(
            budget_id=cmd.budget_id,
            type=cmd.type,
            status=BudgetBackgroundJobStatusEnum.SCHEDULED,
            params=cmd.task_params,
        )

        background_job = orm_background_job.to_dto()

        self._publish_integration_event(background_job)

        return background_job

    def _publish_integration_event(self, background_job: dto.BudgetBackgroundJob) -> None:
        topic_name = get_budget_topic_name(background_job.budget_id)

        background_job_started = BudgetBackgroundJobScheduledEvent(budget_background_job=background_job)

        self._event_stream.publish(topic_name, background_job_started)
