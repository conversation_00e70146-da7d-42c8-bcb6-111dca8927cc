import logging
import traceback

from dependency_injector.wiring import Provide, inject
from mediatr import Mediator

from nga.apps.budget_background_jobs import dto, orm_models
from nga.apps.budget_background_jobs.commands import FinishBudgetBackgroundJobCommand, StartBudgetBackgroundJobCommand
from nga.apps.budget_background_jobs.dto import BudgetBackgroundJobError
from nga.apps.budget_background_jobs.enums import BudgetBackgroundJobStatusEnum, BudgetBackgroundJobTypeEnum
from nga.apps.budget_background_jobs.tasks import get_task_by_job_type
from nga.internal.messaging import EventStream
from nga.utils.dt import get_current_datetime_utc


@Mediator.handler
class StartBudgetBackgroundJobCommandHandler:
    @inject
    def __init__(
        self,
        event_stream: EventStream = Provide["event_stream"],
        mediator: Mediator = Provide["mediator"],
    ) -> None:
        self._event_stream = event_stream
        self._mediator = mediator

    def handle(self, cmd: StartBudgetBackgroundJobCommand) -> dto.BudgetBackgroundJob:
        background_job = cmd.budget_background_job

        background_job.status = BudgetBackgroundJobStatusEnum.STARTED
        background_job.started_at = get_current_datetime_utc()
        background_job.save(update_fields=["status", "started_at"])

        try:
            task = get_task_by_job_type(BudgetBackgroundJobTypeEnum(background_job.type))

            result = task.delay(**background_job.get_params(), budget_background_job_id=str(background_job.pk))
        except Exception as e:
            error = BudgetBackgroundJobError(message=str(e), traceback=traceback.format_exc())

            finish_cmd = FinishBudgetBackgroundJobCommand(background_job.pk, error=error)

            return self._mediator.send(finish_cmd)

        self._log_start(background_job)

        background_job.task_id = result.id
        background_job.save(update_fields=["task_id"])

        background_job_dto = background_job.to_dto()

        return background_job_dto

    def _log_start(self, background_job: orm_models.BudgetBackgroundJob) -> None:
        budget_id = background_job.budget_id
        job_id = background_job.id
        job_type = BudgetBackgroundJobTypeEnum(background_job.type)

        logging.info(f"Budget [id={budget_id}] background job [id={job_id}][type={job_type.name}] started")
