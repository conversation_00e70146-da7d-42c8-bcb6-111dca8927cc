import traceback
from contextlib import contextmanager
from typing import Any, Generator, Optional

from celery import Task
from dependency_injector.wiring import Provide, inject
from mediatr import Mediator

from nga.apps.agreements.commands import (
    BulkActivateBudgetAgreementCommand,
    BulkCloneBudgetAgreementCommand,
    BulkCopyBudgetAgreementCommand,
    BulkDeactivateBudgetAgreementCommand,
)
from nga.apps.budget_background_jobs import orm_models
from nga.apps.budget_background_jobs.commands import FinishBudgetBackgroundJobCommand, StartBudgetBackgroundJobCommand
from nga.apps.budget_background_jobs.dto import BudgetBackgroundJobError
from nga.apps.budget_background_jobs.enums import BudgetBackgroundJobTypeEnum
from nga.apps.budgets.commands import FillBudgetWithAgreementsCommand
from nga.apps.forecasts.commands import (
    BulkCopyForecastRuleCommand,
    BulkCreateDefaultForecastRuleCommand,
    BulkDeleteForecastRuleCommand,
)
from nga.apps.forecasts.domain.dto import ForecastRuleSetup
from nga.apps.iotron.commands import BulkSubmitAgreementToIOTRONCommand
from nga.infra import celery_app


@celery_app.task
@inject
def start_scheduled_budget_background_jobs(mediator: Mediator = Provide["mediator"]) -> None:
    background_jobs = orm_models.BudgetBackgroundJob.objects.scheduled()

    for job in background_jobs:
        start_job_cmd = StartBudgetBackgroundJobCommand(budget_background_job=job)

        mediator.send(start_job_cmd)


@celery_app.task
@inject
def copy_budget_agreements_task(
    target_budget_id: int,
    budget_agreement_ids: Optional[list[int]],
    source_budget_id: Optional[int],
    only_active: Optional[bool],
    budget_background_job_id: str,
    mediator: Mediator = Provide["mediator"],
) -> None:
    copy_budget_agreements_cmd = BulkCopyBudgetAgreementCommand(
        target_budget_id=target_budget_id,
        budget_agreement_ids=budget_agreement_ids,
        source_budget_id=source_budget_id,
        only_active=only_active,
    )

    with budget_background_job_executor(budget_background_job_id, mediator):
        mediator.send(copy_budget_agreements_cmd)


@celery_app.task
@inject
def clone_budget_agreements_task(
    budget_id: int,
    budget_agreement_ids: list[int],
    budget_background_job_id: str,
    mediator: Mediator = Provide["mediator"],
) -> None:
    clone_budget_agreements_cmd = BulkCloneBudgetAgreementCommand(
        budget_id=budget_id,
        budget_agreement_ids=budget_agreement_ids,
        new_name=None,
    )

    with budget_background_job_executor(budget_background_job_id, mediator):
        mediator.send(clone_budget_agreements_cmd)


@celery_app.task
@inject
def activate_budget_agreements_task(
    budget_id: int,
    budget_background_job_id: str,
    mediator: Mediator = Provide["mediator"],
) -> None:
    activate_budget_agreements_cmd = BulkActivateBudgetAgreementCommand(
        budget_id=budget_id,
        budget_agreement_ids=None,
    )

    with budget_background_job_executor(budget_background_job_id, mediator):
        mediator.send(activate_budget_agreements_cmd)


@celery_app.task
@inject
def deactivate_budget_agreements_task(
    budget_id: int,
    budget_background_job_id: str,
    mediator: Mediator = Provide["mediator"],
) -> None:
    deactivate_budget_agreements_cmd = BulkDeactivateBudgetAgreementCommand(
        budget_id=budget_id,
        budget_agreement_ids=None,
    )

    with budget_background_job_executor(budget_background_job_id, mediator):
        mediator.send(deactivate_budget_agreements_cmd)


@celery_app.task
@inject
def fill_budget_with_agreements_and_activate_them_task(
    budget_id: int,
    budget_agreement_ids: list[int],
    budget_background_job_id: str,
    mediator: Mediator = Provide["mediator"],
) -> None:
    fill_with_agreements_cmd = FillBudgetWithAgreementsCommand(
        budget_id=budget_id,
        budget_agreement_ids=budget_agreement_ids,
    )

    with budget_background_job_executor(budget_background_job_id, mediator):
        mediator.send(fill_with_agreements_cmd)


@celery_app.task
@inject
def copy_forecast_rules_task(
    target_budget_id: int,
    source_budget_id: int,
    budget_background_job_id: str,
    mediator: Mediator = Provide["mediator"],
) -> None:
    copy_forecast_rules_cmd = BulkCopyForecastRuleCommand(
        target_budget_id=target_budget_id,
        source_budget_id=source_budget_id,
        forecast_rule_ids=None,
    )

    with budget_background_job_executor(budget_background_job_id, mediator):
        mediator.send(copy_forecast_rules_cmd)


@celery_app.task
@inject
def delete_forecast_rules_task(
    budget_id: int,
    budget_background_job_id: str,
    mediator: Mediator = Provide["mediator"],
) -> None:
    delete_forecast_rules_cmd = BulkDeleteForecastRuleCommand(budget_id=budget_id)

    with budget_background_job_executor(budget_background_job_id, mediator):
        mediator.send(delete_forecast_rules_cmd)


@celery_app.task
@inject
def submit_agreements_to_iotron_task(
    budget_agreement_ids: list[int],
    budget_background_job_id: str,
    mediator: Mediator = Provide["mediator"],
) -> None:

    submit_agreements_to_iotron_cmd = BulkSubmitAgreementToIOTRONCommand(budget_agreement_ids)

    with budget_background_job_executor(budget_background_job_id, mediator):
        mediator.send(submit_agreements_to_iotron_cmd)


@celery_app.task
@inject
def create_default_forecast_rules_task(
    budget_id: int,
    setups: list[dict[str, Any]],
    budget_background_job_id: str,
    mediator: Mediator = Provide["mediator"],
) -> None:

    create_default_forecast_rules_cmd = BulkCreateDefaultForecastRuleCommand(
        budget_id=int(budget_id),
        setups=[ForecastRuleSetup(**s) for s in setups],
    )

    with budget_background_job_executor(budget_background_job_id, mediator):
        mediator.send(create_default_forecast_rules_cmd)


_TASK_JOB_TYPE_MAP = {
    BudgetBackgroundJobTypeEnum.COPY_AGREEMENTS: copy_budget_agreements_task,
    BudgetBackgroundJobTypeEnum.CLONE_AGREEMENTS: clone_budget_agreements_task,
    BudgetBackgroundJobTypeEnum.ACTIVATE_AGREEMENTS: activate_budget_agreements_task,
    BudgetBackgroundJobTypeEnum.DEACTIVATE_AGREEMENTS: deactivate_budget_agreements_task,
    BudgetBackgroundJobTypeEnum.SUBMIT_AGREEMENTS_TO_IOTRON: submit_agreements_to_iotron_task,
    BudgetBackgroundJobTypeEnum.COPY_FORECAST_RULES: copy_forecast_rules_task,
    BudgetBackgroundJobTypeEnum.DELETE_FORECAST_RULES: delete_forecast_rules_task,
    BudgetBackgroundJobTypeEnum.CREATE_DEFAULT_FORECAST_RULES: create_default_forecast_rules_task,
    BudgetBackgroundJobTypeEnum.FILL_BUDGET_WITH_AGREEMENTS: fill_budget_with_agreements_and_activate_them_task,
}


def get_task_by_job_type(budget_background_job_type: BudgetBackgroundJobTypeEnum) -> Task:

    task = _TASK_JOB_TYPE_MAP.get(budget_background_job_type)

    if task is None:
        raise ValueError(f"There is no celery task for {budget_background_job_type.name} job type")

    return task


@contextmanager
def budget_background_job_executor(budget_background_job_id: str, mediator: Mediator) -> Generator[None, None, None]:
    error = None

    try:
        yield
    except Exception as e:
        # TODO: Truncate error
        error = BudgetBackgroundJobError(message=str(e), traceback=traceback.format_exc()[:3000])

    finish_cmd = FinishBudgetBackgroundJobCommand(budget_background_job_id=budget_background_job_id, error=error)

    mediator.send(finish_cmd)
