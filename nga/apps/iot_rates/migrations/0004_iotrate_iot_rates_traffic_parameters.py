# Generated by Django 5.1.7 on 2025-04-14 12:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('iot_rates', '0003_alter_iotrate_called_countries'),
        ('references', '0030_countryphonecode'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='iotrate',
            index=models.Index(fields=['traffic_direction', 'service_type', 'partner_operator_id'], name='iot_rates_traffic_parameters'),
        ),
    ]
