from rest_framework import serializers

from nga.apps.common.serializer_fields import EnumC<PERSON><PERSON><PERSON><PERSON>, YearMonthField
from nga.apps.iot_rates.enums import IOTRateTypeEnum
from nga.apps.references.api.serializers import (
    CountrySerializer,
    OperatorSerializer,
)
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum


class IOTRateFiltersSchemaSerializer(serializers.Serializer):
    home_operators = OperatorSerializer(many=True)
    partner_operators = OperatorSerializer(many=True)

    traffic_directions = serializers.ListField(child=EnumChoiceField(enum_class=TrafficDirectionEnum))
    service_types = serializers.ListField(child=EnumChoiceField(enum_class=ServiceTypeEnum))

    called_countries = CountrySerializer(many=True)

    is_premium = serializers.ListField(child=serializers.BooleanField(allow_null=True))

    start_date_min = YearMonthField()
    start_date_max = YearMonthField()

    end_date_min = YearMonthField()
    end_date_max = Year<PERSON>ont<PERSON><PERSON><PERSON>()

    types = serializers.ListField(child=EnumChoiceField(enum_class=IOTRateTypeEnum))
    currency_codes = serializers.ListField(child=serializers.CharField())
