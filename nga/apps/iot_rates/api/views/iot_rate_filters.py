from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Max, Min, Q
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.iot_rates import models
from nga.apps.iot_rates.api.serializers import IOTRateFiltersSchemaSerializer
from nga.apps.references.providers import AbstractCountryProvider, AbstractOperatorProvider


class IOTRateFiltersAPIView(GenericAPIView):
    serializer_class = IOTRateFiltersSchemaSerializer

    permission_classes = [IsAuthenticated]

    queryset = models.IOTRate.objects.prefetch_related("called_countries").order_by("-pk")

    @inject
    def __init__(
        self,
        operator_provider: AbstractOperatorProvider = Closing[Provide["operator_provider"]],
        country_provider: AbstractCountryProvider = Closing[Provide["country_provider"]],
        **kwargs: Any,
    ):
        super().__init__(**kwargs)

        self.operators_map = {o.id: o for o in operator_provider.get_many()}
        self.countries_map = {c.id: c for c in country_provider.get_many()}

    @swagger_auto_schema(
        responses={status.HTTP_200_OK: serializer_class()},
    )
    def get(self, request: Request, *args: tuple[Any, ...], **kwargs: dict[str, Any]) -> Response:
        queryset = self.filter_queryset(self.get_queryset())

        filter_parameters = queryset.aggregate(
            home_operators=ArrayAgg("home_operator", distinct=True, default=[]),
            partner_operators=ArrayAgg("partner_operator", distinct=True, default=[]),
            traffic_directions=ArrayAgg("traffic_direction", distinct=True, default=[]),
            service_types=ArrayAgg("service_type", distinct=True, default=[]),
            called_countries=ArrayAgg(
                "called_countries", distinct=True, filter=Q(called_countries__id__isnull=False), default=[]
            ),
            start_date_min=Min("start_date"),
            start_date_max=Max("start_date"),
            end_date_min=Min("end_date"),
            end_date_max=Max("end_date"),
            types=ArrayAgg("type", distinct=True, default=[]),
            currency_codes=ArrayAgg("currency_code", distinct=True, default=[]),
            is_premium=ArrayAgg("is_premium", distinct=True, default=[]),
        )

        filter_parameters.update(
            {
                "home_operators": [self.operators_map[h_id] for h_id in filter_parameters["home_operators"]],
                "partner_operators": [self.operators_map[p_id] for p_id in filter_parameters["partner_operators"]],
                "called_countries": [self.countries_map[c_id] for c_id in filter_parameters["called_countries"]],
            }
        )

        serializer = self.get_serializer(filter_parameters)

        return Response(serializer.data)
