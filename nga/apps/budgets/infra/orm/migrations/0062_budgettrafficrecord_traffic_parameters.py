# Generated by Django 4.2 on 2025-03-21 08:59

from django.db import migrations, models

_create_idx_query = """
CREATE INDEX IF NOT EXISTS traffic_parameters ON PUBLIC.budget_traffic_records USING btree (
    budget_snapshot_id,
    partner_operator_id,
    traffic_month,
    service_type,
    traffic_direction
)
"""

_drop_idx_query = """DROP INDEX IF EXISTS traffic_parameters"""


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0049_discount_commitment_distribution_parameters'),
        ('budgets', '0061_alter_budget_type_and_more'),
        ('forecasts', '0024_alter_forecastrule_volume_percentage'),
        ('references', '0030_countryphonecode'),
    ]

    operations = [

        migrations.SeparateDatabaseAndState(
            state_operations=[
                migrations.AddIndex(
                    model_name='budgettrafficrecord',
                    index=models.Index(
                        fields=[
                            'budget_snapshot_id',
                            'partner_operator_id',
                            'traffic_month',
                            'service_type',
                            'traffic_direction',
                        ],
                        name='traffic_parameters',
                    ),
                ),
            ],
            database_operations=[
                migrations.RunSQL(
                    sql=_create_idx_query,
                    reverse_sql=_drop_idx_query,
                )
            ],
        ),
    ]
