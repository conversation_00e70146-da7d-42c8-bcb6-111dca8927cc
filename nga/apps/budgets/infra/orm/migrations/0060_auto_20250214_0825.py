# Generated by Django 4.2 on 2025-02-14 08:25

from django.db import migrations

from nga.apps.budgets.enums import BudgetTypeEnum


def set_type_for_budgets(apps, schema_editor):
    budget_model = apps.get_model("budgets", "Budget")
    for budget_obj in budget_model.objects.all():
        if budget_obj.is_master:
            budget_obj.type = BudgetTypeEnum.MASTER
        else:
            budget_obj.type = BudgetTypeEnum.UPDATED

        budget_obj.save()


def backwards(apps, schema_editor):
    budget_model = apps.get_model("budgets", "Budget")
    budget_model.objects.all().update(type=None)


class Migration(migrations.Migration):

    dependencies = [
        ('budgets', '0059_budget_type'),
    ]

    operations = [
        migrations.RunPython(set_type_for_budgets, backwards)
    ]
