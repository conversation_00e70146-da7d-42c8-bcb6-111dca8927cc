# Generated by Django 4.2 on 2024-10-04 13:30

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('budgets', '0053_delete_externalcalculatedtrafficrecord'),
    ]

    operations = [
        migrations.CreateModel(
            name='BudgetBackgroundJob',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('job_id', models.CharField(blank=True, max_length=36, null=True)),
                ('type', models.PositiveSmallIntegerField(choices=[(1, 'COPY_AGREEMENTS'), (2, 'ACTIVATE_AGREEMENTS'), (3, 'DEACTIVATE_AGREEMENTS'), (4, 'COPY_FORECAST_RULES'), (5, 'DELETE_FORECAST_RULES')])),
                ('is_finished', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('budget', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='background_jobs', related_query_name='background_job', to='budgets.budget')),
            ],
            options={
                'db_table': 'budget_background_jobs',
            },
        ),
    ]
