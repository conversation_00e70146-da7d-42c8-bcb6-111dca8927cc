# Generated by Django 4.2 on 2023-10-16 08:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0010_discount_discountparameter'),
        ('budgets', '0037_budgetcalculation_agreements_applied_at'),
    ]

    operations = [
        migrations.AddField(
            model_name='budgetcalculation',
            name='agreement',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='budget_calculations', related_query_name='budget_calculation', to='agreements.budgetagreement'),
        ),
        migrations.AlterField(
            model_name='budgetcalculation',
            name='type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'FULL_WITH_TRAFFIC_UPDATE'), (2, 'FULL_WITHOUT_TRAFFIC_UPDATE'), (3, 'ONLY_MODIFIED_AGREEMENTS'), (4, 'SINGLE_AGREEMENT')], verbose_name='Budget Calculation Type'),
        ),
    ]
