# Generated by Django 4.2 on 2024-03-18 09:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('forecasts', '0018_alter_forecastrule_distribution_model'),
        ('references', '0008_exchangerate_unique_rate_per_month'),
        ('agreements', '0021_alter_agreement_status'),
        ('budgets', '0046_alter_budgettrafficrecord_traffic_month'),
    ]

    operations = [
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='discount',
            field=models.ForeignKey(blank=True, db_index=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='traffic_records', related_query_name='traffic_record', to='agreements.discount'),
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='forecast_rule',
            field=models.ForeignKey(blank=True, db_index=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='traffic_records', related_query_name='traffic_record', to='forecasts.forecastrule'),
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='traffic_segment',
            field=models.ForeignKey(blank=True, db_index=False, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='budget_traffic_records', related_query_name='budget_traffic_record', to='references.trafficsegment', verbose_name='Traffic Segment'),
        ),
    ]
