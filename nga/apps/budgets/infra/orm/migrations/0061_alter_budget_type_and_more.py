# Generated by Django 4.2 on 2025-02-14 08:32

from django.db import migrations, models
import nga.apps.budgets.enums


class Migration(migrations.Migration):

    dependencies = [
        ('budgets', '0060_auto_20250214_0825'),
    ]

    operations = [
        migrations.AlterField(
            model_name='budget',
            name='type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'MASTER'), (2, 'BASELINE'), (3, 'UPDATED')], verbose_name='Budget Type'),
        ),
        migrations.AddConstraint(
            model_name='budget',
            constraint=models.UniqueConstraint(condition=models.Q(('type', nga.apps.budgets.enums.BudgetTypeEnum['MASTER'])), fields=('type',), name='allowed_only_one_master_budget_type'),
        ),
    ]
