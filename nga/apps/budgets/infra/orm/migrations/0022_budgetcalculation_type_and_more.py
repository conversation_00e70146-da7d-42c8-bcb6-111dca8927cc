# Generated by Django 4.2 on 2023-06-01 20:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('budgets', '0021_budgetcalculation'),
    ]

    operations = [
        migrations.AddField(
            model_name='budgetcalculation',
            name='type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'FULL_WITH_TRAFFIC_UPDATE'), (2, 'FULL_WITHOUT_TRAFFIC_UPDATE')], verbose_name='Budget Calculation Type'),
        ),
        migrations.AlterField(
            model_name='budgetcalculation',
            name='status',
            field=models.PositiveSmallIntegerField(choices=[(1, 'NOT_CALCULATED'), (2, 'STARTED'), (3, 'HISTORICAL_TRAFFIC_SYNCHRONIZATION'), (4, 'FORECAST_RULES_APPLICATION'), (5, 'AGREEMENTS_APPLICATION'), (6, 'CALCULATED'), (7, 'FAILED')], verbose_name='Budget Calculation Status'),
        ),
    ]
