# Generated by Django 4.2 on 2023-07-07 07:54

from django.db import migrations, models
import django.db.models.deletion
import nga.apps.common.model_fields


class Migration(migrations.Migration):

    dependencies = [
        ('references', '0001_initial_squashed'),
        ('budgets', '0024_alter_budgetcalculation_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExternalCalculatedTrafficRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('traffic_month', models.DateField(verbose_name='Traffic Month')),
                ('traffic_type', models.PositiveSmallIntegerField(choices=[(1, 'HISTORICAL'), (2, 'FORECASTED'), (3, 'FORECASTED_LIVE')], db_index=True, verbose_name='Traffic Type')),
                ('traffic_direction', models.PositiveSmallIntegerField(choices=[(1, 'INBOUND'), (2, 'OUTBOUND')], verbose_name='Traffic Direction')),
                ('service_type', models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA')], db_index=True, verbose_name='Service Type')),
                ('call_destination', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'HOME'), (2, 'LOCAL'), (3, 'INTERNATIONAL')], null=True, verbose_name='Call Destination')),
                ('volume_actual', nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='Volume Actual')),
                ('volume_billed', nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='Volume Billed')),
                ('tap_charge_net', nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='TAP Charge Net')),
                ('tap_charge_gross', nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='TAP Charge Gross')),
                ('charge_net', nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='Charge Net')),
                ('charge_gross', nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='Charge Gross')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Creation date')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last updated date')),
                ('home_operator', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='calculation_home_results', related_query_name='calculation_home_result', to='references.operator', verbose_name='Home Operator')),
                ('partner_operator', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='calculation_partner_results', related_query_name='calculation_partner_results', to='references.operator', verbose_name='Partner Operator')),
            ],
            options={
                'db_table': 'external_calculated_traffic_records',
            },
        ),
    ]
