# Generated by Django 4.2 on 2023-08-25 19:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('budgets', '0032_budgettrafficrecord_traffic_segment'),
    ]

    operations = [
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='service_type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA'), (6, 'VOLTE'), (7, 'ACCESS_FEE')], db_index=True, verbose_name='Service Type'),
        ),
        migrations.AlterField(
            model_name='externalcalculatedtrafficrecord',
            name='service_type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA'), (6, 'VOLTE'), (7, 'ACCESS_FEE')], db_index=True, verbose_name='Service Type'),
        ),
    ]
