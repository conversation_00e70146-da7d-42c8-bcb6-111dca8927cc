# Generated by Django 4.2 on 2023-05-31 09:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('budgets', '0020_alter_budgettrafficrecord_budget_snapshot'),
    ]

    operations = [
        migrations.CreateModel(
            name='BudgetCalculation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.PositiveSmallIntegerField(choices=[(1, 'HISTORICAL_TRAFFIC_SYNCHRONIZATION'), (2, 'FORECAST_RULES_APPLICATION'), (3, 'AGREEMENTS_APPLICATION'), (4, 'CALCULATED'), (5, 'FAILED')], verbose_name='Budget Calculation Status')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Creation Date')),
                ('finished_at', models.DateTimeField(blank=True, null=True, verbose_name='Finish Date')),
                ('traffic_synchronized_at', models.DateTimeField(blank=True, null=True, verbose_name='Traffic Sync Date')),
                ('forecast_rules_applied_at', models.DateTimeField(blank=True, null=True, verbose_name='Forecast Rules Apply Date')),
                ('budget_snapshot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='budget_calculations', related_query_name='budget_calculation', to='budgets.budgetsnapshot')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='budget_calculations', related_query_name='budget_calculation', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'budget_calculations',
            },
        ),
    ]
