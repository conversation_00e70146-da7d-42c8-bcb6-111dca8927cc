# Generated by Django 4.2 on 2024-03-18 10:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('budgets', '0047_alter_budgettrafficrecord_discount_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='externalcalculatedtrafficrecord',
            name='service_type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA'), (6, 'VOLTE'), (7, 'ACCESS_FEE')], verbose_name='Service Type'),
        ),
        migrations.AlterField(
            model_name='externalcalculatedtrafficrecord',
            name='traffic_month',
            field=models.DateField(db_index=True, verbose_name='Traffic Month'),
        ),
        migrations.AlterField(
            model_name='externalcalculatedtrafficrecord',
            name='traffic_type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'HISTORICAL'), (2, 'FORECASTED'), (3, 'FORECASTED_LIVE')], verbose_name='Traffic Type'),
        ),
    ]
