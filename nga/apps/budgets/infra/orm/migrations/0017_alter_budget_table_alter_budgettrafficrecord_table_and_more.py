# Generated by Django 4.2 on 2023-05-23 08:55

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import nga.apps.budgets.enums


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('budgets', '0016_alter_budgettrafficrecord_volume_billed'),
    ]

    operations = [
        migrations.AlterModelTable(
            name='budget',
            table='budgets',
        ),
        migrations.AlterModelTable(
            name='budgettrafficrecord',
            table='budget_traffic_records',
        ),
        migrations.CreateModel(
            name='BudgetSnapshot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=248, verbose_name='Name')),
                ('type', models.PositiveSmallIntegerField(choices=[(1, 'ACTIVE'), (2, 'CALCULATION'), (3, 'HISTORICAL')], verbose_name='Budget Snapshot Type')),
                ('created_at', models.DateTimeField(editable=False, verbose_name='Creation Date')),
                ('updated_at', models.DateTimeField(verbose_name='Update Date')),
                ('budget', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='snapshots', related_query_name='snapshot', to='budgets.budget', verbose_name='Budget')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='budget_snapshots', related_query_name='budget_snapshot', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'budget_snapshots',
            },
        ),
        migrations.AddField(
            model_name='budget',
            name='active_snapshot',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='budgets.budgetsnapshot', verbose_name='Active Snapshot'),
        ),
        migrations.AddField(
            model_name='budgettrafficrecord',
            name='budget_snapshot',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='traffic_records', related_query_name='traffic_record', to='budgets.budgetsnapshot', verbose_name='Budget Traffic Snapshot'),
        ),
        migrations.AddConstraint(
            model_name='budgetsnapshot',
            constraint=models.UniqueConstraint(condition=models.Q(('type', nga.apps.budgets.enums.BudgetSnapshotTypeEnum['ACTIVE'])), fields=('budget_id', 'type'), name='allowed_only_one_active_snapshot_per_budget'),
        ),
        migrations.AddConstraint(
            model_name='budgetsnapshot',
            constraint=models.UniqueConstraint(condition=models.Q(('type', nga.apps.budgets.enums.BudgetSnapshotTypeEnum['CALCULATION'])), fields=('budget_id', 'type'), name='allowed_only_one_calculation_snapshot_per_budget'),
        ),
    ]
