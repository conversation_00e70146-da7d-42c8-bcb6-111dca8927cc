# Generated by Django 4.2 on 2024-01-09 15:54

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('budgets', '0041_alter_budget_active_snapshot_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BudgetCalculationLogRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.CharField(max_length=128)),
                ('traceback', models.CharField(blank=True, max_length=2048, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Creation Date')),
                ('calculation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='log_records', related_query_name='log_record', to='budgets.budgetcalculation')),
            ],
            options={
                'db_table': 'budget_calculation_log_records',
            },
        ),
    ]
