import dataclasses

from _decimal import Decimal

__all__ = [
    "BudgetKPIOptions",
    "BudgetKPITotalCharges",
    "BudgetKPIValues",
    "ServiceTypeKPIValues",
]

from typing import Optional, Sequence

from nga.apps.budgets.enums import ChargeTypeEnum
from nga.core.enums import ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import DatePeriod


@dataclasses.dataclass
class ServiceTypeKPIValues:
    service_type: ServiceTypeEnum

    inbound_volume: Decimal
    inbound_charge: Decimal
    inbound_rate: Decimal

    outbound_volume: Decimal
    outbound_charge: Decimal
    outbound_rate: Decimal

    net_position_charge: Decimal
    net_position_volume: Decimal


@dataclasses.dataclass
class BudgetKPITotalCharges:
    inbound: Decimal
    outbound: Decimal
    net_position: Decimal


@dataclasses.dataclass
class BudgetKPIValues:
    service_types: list[ServiceTypeKPIValues]
    total_charges: BudgetKPITotalCharges
    options: "BudgetKPIOptions"


@dataclasses.dataclass
class BudgetKPIOptions:
    """Budget KPI Values parameters for calculation."""

    budget_id: int
    charge_type: ChargeTypeEnum
    volume_type: VolumeTypeEnum

    home_operators: Optional[Sequence[int]] = None
    partner_operators: Optional[Sequence[int]] = None
    partner_countries: Optional[Sequence[int]] = None

    date_period: Optional[DatePeriod] = None
    currency_code: Optional[str] = None
