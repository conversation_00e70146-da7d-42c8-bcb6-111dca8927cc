import string
from abc import ABC, abstractmethod
from enum import Enum
from io import Bytes<PERSON>
from typing import IO, Any, Iterator

from django.utils import timezone
from openpyxl.workbook import Workbook
from openpyxl.worksheet.worksheet import Worksheet

from nga.apps.budgets.analytics.kpi import BudgetKPIValues
from nga.apps.budgets.domain.models import Budget
from nga.apps.common.renderers import BOLD_FONT, CENTER_ALIGNMENT, FONT_ATTR, XlsxStyleRenderer
from nga.apps.references.providers import AbstractOperatorProvider
from nga.utils.string import date_to_str


class AbstractBudgetKPIValuesRenderer(ABC):
    """Base class for writing BudgetKPIValues to IO."""

    def __init__(
        self,
        budget: Budget,
        kpi_values: BudgetKPIValues,
        operator_provider: AbstractOperatorProvider,
    ) -> None:
        self._budget = budget
        self._kpi_values = kpi_values

        self._operator_provider = operator_provider

    @abstractmethod
    def render(self) -> IO[bytes]:
        """Render BudgetKPIValues content into stream."""

    @abstractmethod
    def generate_filename(self) -> str:
        """Returns filename for processed budget."""


class BudgetKPIValuesXlsxRenderer(AbstractBudgetKPIValuesRenderer):
    """
    BudgetKPIValuesRenderer interface implementation for writing BudgetKPI values to xlsx file.
    """

    # Ordering in KPI_OPTIONS_COLUMNS, _NON_SECTION_FIELD_MAP, _INBOUND_FIELD_MAP and
    # _NET_POSITION_FIELD_MAP matters. There is an ability to define custom ordering.

    KPI_OPTIONS_HEADER = "Filters:"
    KPI_OPTIONS_COLUMNS = (
        "Budget",
        "Home Operators",
        "Partner Operators",
        "Start Date",
        "End Date",
        "Charges",
        "Currency",
    )

    _NON_SECTION_FIELD_MAP = {"Service Type": "service_type"}

    _INBOUND_FIELD_MAP = {
        "Volume": "inbound_volume",
        "Charge": "inbound_charge",
        "Rate": "inbound_rate",
    }
    _OUTBOUND_FIELD_MAP = {
        "Volume": "outbound_volume",
        "Charge": "outbound_charge",
        "Rate": "outbound_rate",
    }
    _NET_POSITION_FIELD_MAP = {"Volume": "net_position_volume", "Charge": "net_position_charge"}

    FILENAME_DATE_FORMAT = "%Y-%m-%d"

    class _TableSection(Enum):
        """Defines KPI values table sections."""

        INBOUND = "Inbound"
        OUTBOUND = "Outbound"
        NET_POSITION = "Net Position"

    KPI_TOTAL_CHARGES_HEADER = "Total"

    _KPI_TOTAL_CHARGES_SECTION_FIELD_MAP = {
        _TableSection.INBOUND: "inbound",
        _TableSection.OUTBOUND: "outbound",
        _TableSection.NET_POSITION: "net_position",
    }

    # There is option to define sections order
    KPI_SECTION_COLUMNS_MAP = {
        _TableSection.INBOUND: _INBOUND_FIELD_MAP,
        _TableSection.OUTBOUND: _OUTBOUND_FIELD_MAP,
        _TableSection.NET_POSITION: _NET_POSITION_FIELD_MAP,
    }
    _KPI_SECTION_LENGTH_MAP = {section: len(columns) for section, columns in KPI_SECTION_COLUMNS_MAP.items()}

    _alphabets_map = {i: let for i, let in enumerate(string.ascii_uppercase)}

    _COLUMN_WIDTH = 15

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

        self.xlsx_style_renderer = XlsxStyleRenderer()

    def render(self) -> IO[bytes]:
        """Entry point method that creates xlsx workbook and writes its content to memory."""

        wb = Workbook()
        ws: Worksheet = wb.active

        self._write_kpi_options(wb)
        self._write_kpi_values(wb)

        self.xlsx_style_renderer.apply_default_styles_to_ws(ws)

        memory_wb = BytesIO()
        wb.save(memory_wb)
        wb.close()

        return memory_wb

    def _write_kpi_options(self, wb: Workbook) -> None:
        """Writes budget KPI options."""

        ws: Worksheet = wb.active

        options_cell = f"A{self.xlsx_style_renderer.row_idx}"
        ws[options_cell] = self.KPI_OPTIONS_HEADER
        ws[options_cell].font = BOLD_FONT
        self.xlsx_style_renderer.commit_row()

        options = self._kpi_values.options

        period = options.date_period
        budget_period = self._budget.period
        start_date = period.start_date if period else budget_period.start_date
        end_date = period.end_date if period else budget_period.end_date

        home_operators_str = ""
        if options.home_operators:
            home_operators = self._operator_provider.get_many(operators_ids=options.home_operators)
            home_operators_str = ", ".join(hpmn.pmn_code for hpmn in home_operators)

        partner_operators_str = ""
        if options.partner_operators:
            partner_operators = self._operator_provider.get_many(operators_ids=options.partner_operators)
            partner_operators_str = ", ".join(ppmn.pmn_code for ppmn in partner_operators)

        row_values = (
            self._budget.name,
            home_operators_str,
            partner_operators_str,
            date_to_str(start_date),
            date_to_str(end_date),
            options.charge_type.name.capitalize(),
            options.currency_code,
        )
        for row in zip(self.KPI_OPTIONS_COLUMNS, row_values):
            ws.append(row)
            self.xlsx_style_renderer.commit_row()

        # Add empty line
        ws.append([])
        self.xlsx_style_renderer.commit_row()

    def _write_kpi_values(self, wb: Workbook) -> None:
        """Writes table whole with budget KPI values per each service type."""

        ws: Worksheet = wb.active

        self._write_kpi_table_sections(ws)
        self._write_kpi_table_header(ws)

        for service_kpi in self._kpi_values.service_types:
            row = [getattr(service_kpi, field) for (column, field) in self.iter_kpi_table_columns_map()]

            row = list(map(self._map_by_type, row))

            ws.append(row)

            self.xlsx_style_renderer.commit_row()

        self._write_kpi_totals_row(wb)

    @classmethod
    def iter_kpi_table_columns_map(cls) -> Iterator[tuple[str, str]]:
        """
        Yields KPI values table columns and its mapped fields on ServiceTypeKPIValues instance.
        Note that _NON_SECTION_FIELD_MAP columns are set before KPI_SECTION_COLUMNS_MAP
        by default.
        """

        for column, field in cls._NON_SECTION_FIELD_MAP.items():
            yield column, field

        for section, field_map in cls.KPI_SECTION_COLUMNS_MAP.items():
            for column, field in field_map.items():
                yield column, field

    def _write_kpi_table_sections(self, ws: Worksheet) -> None:
        """Writes table sections (_TableSection)."""

        section_cells = self._merge_section_cells(ws)

        for title, cell in zip(self.KPI_SECTION_COLUMNS_MAP.keys(), section_cells):
            ws[cell] = title.value
            ws[cell].font = BOLD_FONT
            ws[cell].alignment = CENTER_ALIGNMENT

        self.xlsx_style_renderer.commit_row()

    def _write_kpi_table_header(self, ws: Worksheet) -> None:
        """Writes budget KPI table header"""

        keys = [column for column, _ in self.iter_kpi_table_columns_map()]
        ws.append(keys)
        self.xlsx_style_renderer.apply_style_to_row(ws, FONT_ATTR, BOLD_FONT)
        self.xlsx_style_renderer.commit_row()

    def _merge_section_cells(self, ws: Worksheet) -> tuple[str, ...]:
        """Merges cells for table sections in order to write its columns under it."""

        cells = []

        start_idx = 1
        row_idx = self.xlsx_style_renderer.row_idx
        start_cell = "B" + str(row_idx)

        for section_name, length in self._KPI_SECTION_LENGTH_MAP.items():
            cells.append(start_cell)

            end_idx = length + start_idx - 1
            end_cell = str(self._alphabets_map[end_idx]) + str(row_idx)
            cells_range = f"{start_cell}:{end_cell}"

            ws.merge_cells(cells_range)

            start_idx = end_idx + 1
            start_cell = str(self._alphabets_map[start_idx]) + str(row_idx)

        return tuple(cells)

    def _write_kpi_totals_row(self, wb: Workbook) -> None:
        """Writes budget KPI total charges table row."""

        ws: Worksheet = wb.active

        total_cell = f"A{self.xlsx_style_renderer.row_idx}"
        ws[total_cell] = self.KPI_TOTAL_CHARGES_HEADER
        ws[total_cell].font = BOLD_FONT

        cells = self._merge_section_cells(ws)

        for title, cell in zip(self.KPI_SECTION_COLUMNS_MAP.keys(), cells):
            attr_name = self._KPI_TOTAL_CHARGES_SECTION_FIELD_MAP[title]
            ws[cell] = getattr(self._kpi_values.total_charges, attr_name)
            ws[cell].alignment = CENTER_ALIGNMENT
            ws[cell].font = BOLD_FONT

        self.xlsx_style_renderer.commit_row()

    @classmethod
    def _map_by_type(cls, _attr: Any) -> str:
        if isinstance(_attr, Enum):
            return _attr.name

        return _attr

    def generate_filename(self) -> str:
        current_date = timezone.now().date().strftime(self.FILENAME_DATE_FORMAT)
        budget_name = self._budget.name.replace(" ", "_")
        return f"{budget_name}_KPI_{current_date}.xlsx"
