import abc
import dataclasses
from decimal import Decimal
from typing import Collection

import numpy as np
import pandas as pd

from nga.apps.budgets.analytics.kpi import (
    BudgetKPIOptions,
    BudgetKPITotalCharges,
    BudgetKPIValues,
    ServiceTypeKPIValues,
)
from nga.apps.budgets.analytics.mappings import CHARGE_TYPE_FIELD_MAP, VOLUME_TYPE_FIELD_MAP
from nga.apps.budgets.domain.dto import BudgetTrafficParameters
from nga.apps.budgets.infra.orm import models
from nga.apps.references.types import TrafficRecordValuesDict
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum, get_choices_values

__all__ = [
    "AbstractBudgetKPIValuesProvider",
    "BudgetKPIValuesProvider",
]


class _TrafficRecord(TrafficRecordValuesDict):
    """Represents aggregated budget traffic record for KPI values provider."""

    service_type: ServiceTypeEnum

    traffic_direction: TrafficDirectionEnum


class AbstractBudgetKPIValuesProvider(abc.ABC):
    """
    Base class for KPI values provider. KPI (Key Performance Indicator)
    values are aggregation of budget values records by traffic direction and
    service type. In aggregation take part: volume charge net/gross and rate,
    where rate=charge/volume.
    """

    @abc.abstractmethod
    def get_values(self, options: BudgetKPIOptions) -> BudgetKPIValues:
        """Returns calculated KPI values based on provided options."""


class BudgetKPIValuesProvider(AbstractBudgetKPIValuesProvider):
    """Performs Budget KPI values calculation based on database storage."""

    _SERVICE_KPI_COLUMNS = [f.name for f in dataclasses.fields(ServiceTypeKPIValues)]

    def get_values(self, options: BudgetKPIOptions) -> BudgetKPIValues:
        """Performs KPI values calculation."""

        budget = models.Budget.objects.get(pk=options.budget_id)

        parameters = BudgetTrafficParameters(
            snapshot_id=budget.active_snapshot_id,
            home_operators=options.home_operators,
            partner_operators=options.partner_operators,
            partner_countries=options.partner_countries,
            period=options.date_period,
        )

        qs = (
            models.BudgetTrafficRecord.objects.filter_by_parameters(parameters)
            .apply_currency_exchange_rates(budget=budget, currency_code=options.currency_code)
            .values("service_type", "traffic_direction")
            .aggregate_volumes()
            .aggregate_charges(exchange_rates_applied=bool(options.currency_code is not None))
            .order_by("service_type")
        )

        records: list[_TrafficRecord] = list(qs)

        if records:
            service_kpi_values = self._get_service_kpi_values(records, options)
        else:
            service_kpi_values = self._create_empty_service_kpi_values()

        total_charges = self._get_total_charges(service_kpi_values)

        return BudgetKPIValues(service_types=service_kpi_values, total_charges=total_charges, options=options)

    @classmethod
    def _get_service_kpi_values(
        cls,
        records: list[_TrafficRecord],
        options: BudgetKPIOptions,
    ) -> list[ServiceTypeKPIValues]:
        """
        Calculates service KPI values. Performs calculation of rate for each direction and
        net position for each service type.
        """

        df = pd.DataFrame(records)

        inbound_mask = df["traffic_direction"] == TrafficDirectionEnum.INBOUND

        charge_field = CHARGE_TYPE_FIELD_MAP[options.charge_type]
        volume_field = VOLUME_TYPE_FIELD_MAP[options.volume_type]

        df["inbound_charge"] = df[inbound_mask][charge_field]
        df["inbound_volume"] = df[inbound_mask][volume_field]
        df["outbound_charge"] = df[~inbound_mask][charge_field]
        df["outbound_volume"] = df[~inbound_mask][volume_field]

        # aggregation by service_type, using apply call for maintaining decimal accuracy
        df = df.groupby(by=["service_type"], as_index=False)[
            ["inbound_charge", "inbound_volume", "outbound_charge", "outbound_volume"]
        ].apply(lambda x: x.sum())

        df["net_position_charge"] = df["inbound_charge"] - df["outbound_charge"]
        df["net_position_volume"] = df["inbound_volume"] - df["outbound_volume"]

        df.replace(0, np.nan, inplace=True)

        df["inbound_rate"] = df["inbound_charge"] / df["inbound_volume"]
        df["outbound_rate"] = df["outbound_charge"] / df["outbound_volume"]

        # After calculation replacing all numpy invalid values with zero
        df.replace([np.inf, -np.inf, np.nan], Decimal("0"), inplace=True)

        # fill values for empty services that are not present in input aggregated rows
        service_types = get_choices_values(ServiceTypeEnum)
        missing_services = list(set(service_types).difference(set(df["service_type"])))
        missing_services_df = cls._create_empty_service_kpi_df(missing_services)

        df = pd.concat([df, missing_services_df])

        df.sort_values("service_type", ascending=True, inplace=True)

        return cls._map_df_to_service_kpi_values(df)

    @classmethod
    def _create_empty_service_kpi_values(cls) -> list[ServiceTypeKPIValues]:
        """Creates list of ServiceTypeKPIValues for all service types with zeros."""

        rows_df = cls._create_empty_service_kpi_df(tuple(s.value for s in ServiceTypeEnum))

        return cls._map_df_to_service_kpi_values(rows_df)

    @classmethod
    def _create_empty_service_kpi_df(cls, values: Collection[str | int]) -> pd.DataFrame:
        """
        Creates pd.Dataframe instance with zero Service KPI values for each service type.
        """

        rows_df = pd.DataFrame(columns=cls._SERVICE_KPI_COLUMNS)
        rows_df["service_type"] = values
        rows_df.fillna(Decimal("0"), inplace=True)

        return rows_df

    @classmethod
    def _map_df_to_service_kpi_values(cls, _df: pd.DataFrame) -> list[ServiceTypeKPIValues]:
        """Maps pd.Dataframe with Service KPI values to list of ServiceTypeKPIValues."""

        return [
            ServiceTypeKPIValues(**{**r, "service_type": ServiceTypeEnum(r["service_type"])})
            for r in _df[cls._SERVICE_KPI_COLUMNS].to_dict("records")
        ]

    @classmethod
    def _get_total_charges(cls, service_type_kpi_values: list[ServiceTypeKPIValues]) -> BudgetKPITotalCharges:
        """Performs sum-up of charges for each direction and net position."""

        total_inbound, total_outbound, total_net_position = Decimal("0"), Decimal("0"), Decimal("0")

        for kpi_value in service_type_kpi_values:
            total_inbound += kpi_value.inbound_charge
            total_outbound += kpi_value.outbound_charge
            total_net_position += kpi_value.net_position_charge

        return BudgetKPITotalCharges(inbound=total_inbound, outbound=total_outbound, net_position=total_net_position)
