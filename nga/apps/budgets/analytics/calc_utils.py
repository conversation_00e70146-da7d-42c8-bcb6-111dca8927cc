from decimal import Decimal

import pandas as pd

from nga.core.enums import TrafficDirectionEnum


def calculate_net_position(df: pd.DataFrame, index_field: list[str]) -> pd.DataFrame:
    if df.empty:
        return df

    inbound_mask = df["traffic_direction"] == TrafficDirectionEnum.INBOUND

    inbound_df = df[inbound_mask]
    outbound_df = df[~inbound_mask]

    inbound_df.set_index(keys=index_field, inplace=True)
    outbound_df.set_index(keys=index_field, inplace=True)

    net_position_df = inbound_df.subtract(outbound_df, fill_value=Decimal("0"))
    net_position_df.reset_index(inplace=True)

    return net_position_df
