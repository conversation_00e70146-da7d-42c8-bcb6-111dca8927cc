from abc import ABC, abstractmethod
from datetime import date
from decimal import Decimal
from typing import Optional

import pandas as pd

from nga.apps.budgets.analytics.calc_utils import calculate_net_position
from nga.apps.budgets.analytics.mappings import UNIT_TRAFFIC_DIRECTION_MAP, get_unit_field_by_options
from nga.apps.budgets.analytics.traffic_evolution.dto import (
    TrafficEvolution,
    TrafficEvolutionOptions,
    TrafficEvolutionRecord,
)
from nga.apps.budgets.analytics.types import PreviousYearTrafficRecordValuesDict
from nga.apps.budgets.domain.dto import BudgetTrafficParameters
from nga.apps.budgets.enums import UnitDirectionTypeEnum
from nga.apps.budgets.infra.orm import models
from nga.apps.budgets.infra.repositories.orm_budget import from_orm_budget_to_domain
from nga.apps.references.types import TrafficRecordValuesDict
from nga.core.enums import TrafficTypeEnum
from nga.core.types import MONTHS_IN_A_YEAR, ONE_YEAR_IN_MONTHS, DatePeriod, Month


class AbstractTrafficEvolutionProvider(ABC):
    @abstractmethod
    def get_values(self, options: TrafficEvolutionOptions) -> TrafficEvolution:
        """
        Returns object that contains values representing budget's traffic monthly aggregation filtered by provided
        parameters.
        """


class TrafficEvolutionORMProvider(AbstractTrafficEvolutionProvider):
    _NET_POSITION_IDX = ["traffic_month", "traffic_type", "traffic_direction"]

    def get_values(self, options: TrafficEvolutionOptions) -> TrafficEvolution:
        """
        Returns object that contains values representing budget's traffic monthly aggregation filtered by provided
        parameters.
        """

        orm_budget = models.Budget.objects.get(pk=options.budget_id)

        traffic_months = self._period_to_traffic_months(options.period)

        traffic_direction = UNIT_TRAFFIC_DIRECTION_MAP.get(options.unit_direction_type)

        parameters = BudgetTrafficParameters(
            snapshot_id=orm_budget.active_snapshot_id,
            home_operators=options.home_operators,
            partner_operators=options.partner_operators,
            partner_countries=options.partner_countries,
            traffic_months=traffic_months,
            traffic_directions=(traffic_direction,) if traffic_direction else (),
            service_types=(options.service_type,) if options.service_type else (),
            include_forecasted=options.include_forecasted,
        )

        values = tuple(
            models.BudgetTrafficRecord.objects.filter_by_parameters(parameters)
            .apply_currency_exchange_rates(budget=orm_budget, currency_code=options.currency_code)
            .values("traffic_month", "traffic_direction", "traffic_type")
            .aggregate_volumes()
            .aggregate_charges(exchange_rates_applied=True)
        )

        traffic_evolution_values = self._calculate_traffic_evolution_values(values, options)

        domain_budget = from_orm_budget_to_domain(orm_budget)

        lhm = domain_budget.last_historical_month

        traffic_evolution_records = self._map_traffic_evolution_values_to_records(
            values=traffic_evolution_values,
            lhm=lhm,
            options=options,
        )

        missing_evolution_records = self._create_missing_records(traffic_evolution_records, lhm, options.period)

        traffic_evolution_records = tuple(
            sorted([*traffic_evolution_records, *missing_evolution_records], key=lambda r: r.traffic_month)
        )

        traffic_evolution = TrafficEvolution(options, traffic_evolution_records)

        return traffic_evolution

    @classmethod
    def _period_to_traffic_months(cls, period: Optional[DatePeriod]) -> Optional[tuple[Month, ...]]:
        if period is None:
            return None

        if period.total_months <= MONTHS_IN_A_YEAR:
            traffic_months = (
                *tuple(period),
                *tuple(period.previous_year),
            )
        else:
            period_with_previous_year = DatePeriod(period.previous_year.start_date, period.end_date)

            traffic_months = tuple(period_with_previous_year)

        return traffic_months

    def _calculate_traffic_evolution_values(
        self,
        values: tuple["_MonthlyTrafficRecord", ...],
        options: TrafficEvolutionOptions,
    ) -> list["_TrafficEvolutionValue"]:
        """
        Maps raw db values to collection of _TrafficEvolutionValue, where each object contains its value along with
        previous year value.
        """

        if not values:
            return []

        records_df = pd.DataFrame(values)

        result_records: list[_TrafficEvolutionValue] = []

        periods_by_12_months = reversed(tuple(options.period.chunk_by_n_months()))

        for sub_period in periods_by_12_months:
            requested_year_df = records_df[
                (records_df["traffic_month"] >= sub_period.start_date)
                & (records_df["traffic_month"] <= sub_period.end_date)
            ]

            previous_year_period = sub_period.previous_year

            previous_year_df = records_df.loc[records_df["traffic_month"].isin(previous_year_period)].copy()

            previous_year_df["traffic_month"] = previous_year_df["traffic_month"].apply(
                lambda m: m + ONE_YEAR_IN_MONTHS
            )

            if options.unit_direction_type == UnitDirectionTypeEnum.NET_POSITION:
                requested_year_df = calculate_net_position(requested_year_df, self._NET_POSITION_IDX)
                previous_year_df = calculate_net_position(previous_year_df, self._NET_POSITION_IDX)

            requested_year_df = self._aggregate_values(requested_year_df)
            previous_year_df = self._aggregate_values(previous_year_df)

            result_df = requested_year_df.merge(
                previous_year_df,
                how="outer",
                on=["traffic_month"],
                suffixes=("", "_previous_year"),
            )

            result_df = result_df.fillna(Decimal("0")).infer_objects(copy=False)

            result_records.extend(result_df.to_dict("records"))

        return result_records

    @classmethod
    def _aggregate_values(cls, values_df: pd.DataFrame) -> pd.DataFrame:
        values_df = (
            values_df.groupby(by=["traffic_month", "traffic_type"], group_keys=True)[
                [
                    "volume_actual",
                    "volume_billed",
                    "charge_net",
                    "charge_gross",
                    "tap_charge_net",
                    "tap_charge_gross",
                ]
            ]
            .apply(lambda x: x.sum())
            .reset_index()
        )

        return values_df

    @classmethod
    def _map_traffic_evolution_values_to_records(
        cls,
        values: list["_TrafficEvolutionValue"],
        lhm: Month,
        options: TrafficEvolutionOptions,
    ) -> tuple[TrafficEvolutionRecord, ...]:
        """Performs mapping of values to records including provided options. Maps appropriate value (volume/charge)."""

        value_field = get_unit_field_by_options(options)

        previous_year_value_field = f"{value_field}_previous_year"

        traffic_evolution_records = tuple(
            TrafficEvolutionRecord(
                traffic_type=TrafficTypeEnum.HISTORICAL if v["traffic_month"] <= lhm else TrafficTypeEnum.FORECASTED,
                traffic_month=v["traffic_month"],
                value=v[value_field],  # type: ignore[literal-required]
                previous_year_value=v[previous_year_value_field],  # type: ignore[literal-required]
            )
            for v in values
        )

        return traffic_evolution_records

    @classmethod
    def _create_missing_records(
        cls,
        records: tuple[TrafficEvolutionRecord, ...],
        lhm: Month,
        period: DatePeriod,
    ) -> list[TrafficEvolutionRecord]:
        covered_months = set(Month.create_from_date(r.traffic_month) for r in records)

        non_covered_months = set(period) - covered_months

        if len(covered_months) == period.total_months:
            return []

        missing_records = []

        for month in non_covered_months:
            traffic_type = TrafficTypeEnum.HISTORICAL if month <= lhm else TrafficTypeEnum.FORECASTED

            missing_records.append(
                TrafficEvolutionRecord(
                    traffic_type=traffic_type,
                    traffic_month=month.to_date(),
                    value=Decimal("0"),
                    previous_year_value=Decimal("0"),
                )
            )

        return missing_records


class _MonthlyTrafficRecord(TrafficRecordValuesDict):
    traffic_month: date


class _TrafficEvolutionValue(TrafficRecordValuesDict, PreviousYearTrafficRecordValuesDict):
    traffic_type: TrafficTypeEnum

    traffic_type_previous_year: TrafficTypeEnum

    traffic_month: date
