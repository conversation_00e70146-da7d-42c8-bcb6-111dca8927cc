import string
from abc import ABC, abstractmethod
from io import Bytes<PERSON>
from typing import I<PERSON>, Any, Optional

from django.utils import timezone
from openpyxl.styles import Alignment, Font
from openpyxl.workbook import Workbook
from openpyxl.worksheet.worksheet import Worksheet

from nga.apps.budgets.domain.models import Budget
from nga.apps.references.providers import (
    AbstractCountryProvider,
    AbstractOperatorProvider,
    AbstractTrafficSegmentProvider,
)
from nga.core.consts import XDR_CURRENCY_CODE
from nga.utils.string import date_to_str, stringify_enum_list

_BOLD_FONT = Font(bold=True)
_FONT_ATTR = "font"
_CENTER_ALIGNMENT = Alignment(horizontal="center")


class AbstractBudgetReportRenderer(ABC):
    """Base class for writing BudgetReport to IO."""

    @abstractmethod
    def render(
        self, budget: Budget, reporting_values: list[dict[str, Any]], reporting_options: dict[str, Any]
    ) -> IO[bytes]:
        """Render BudgetReport content into stream."""

    @abstractmethod
    def generate_filename(self) -> str:
        """Returns filename for processed budget."""


class BudgetReportXlsxRenderer(AbstractBudgetReportRenderer):
    """
    BudgetReportRenderer interface implementation for writing BudgetReport values to xlsx file.
    """

    REPORT_OPTIONS_HEADER = "Filters:"
    REPORT_OPTIONS_COLUMNS = (
        "Budget",
        "Home Operators",
        "Partner Operators",
        "Partner Countries",
        "Start Date",
        "End Date",
        "Traffic Types",
        "Traffic Directions",
        "Service Types",
        "Call Destinations",
        "Called Countries",
        "Premium",
        "Traffic Segments",
        "IMSI Count Types",
        "Currency",
    )
    _REPORT_FIELDS_MAP = {
        "home_operator_pmn": "Home Operator",
        "partner_operator_pmn": "Partner Operator",
        "partner_country": "Partner Country",
        "traffic_month": "Traffic Month",
        "traffic_type": "Traffic Type",
        "traffic_direction": "Traffic Direction",
        "service_type": "Service Type",
        "call_destination": "Call Destination",
        "called_country": "Called Country",
        "is_premium": "Premium",
        "traffic_segment_name": "Traffic Segment",
        "imsi_count_type": "IMSI Count Type",
        "volume_actual": "Volume Actual",
        "volume_billed": "Volume Billable",
        "tap_charge_net": "TAP Charge Net",
        "tap_charge_gross": "TAP Charge Gross",
        "charge_net": "Charge Net",
        "charge_gross": "Charge Gross",
        "discount_net": "Discount (Net)",
        "discount_gross": "Discount (Gross)",
        "tap_rate_net_volume_actual": "TAP Rate Net (Volume Actual)",
        "tap_rate_gross_volume_actual": "TAP Rate Gross (Volume Actual)",
        "discounted_rate_net_volume_actual": "Discounted Rate Net (Volume Actual) ",
        "discounted_rate_gross_volume_actual": "Discounted Rate Gross (Volume Actual)",
        "tap_rate_net_volume_billed": "TAP Rate Net (Volume Billed)",
        "tap_rate_gross_volume_billed": "TAP Rate Gross (Volume Billed)",
        "discounted_rate_net_volume_billed": "Discounted Rate Net (Volume Billed)",
        "discounted_rate_gross_volume_billed": "Discounted Rate Gross (Volume Billed)",
    }

    FILENAME_DATE_FORMAT = "%Y-%m-%d"

    _alphabets_map = {i: let for i, let in enumerate(string.ascii_uppercase)}

    _COLUMN_WIDTH = 15

    def __init__(
        self,
        country_provider: AbstractCountryProvider,
        operator_provider: AbstractOperatorProvider,
        traffic_segment_provider: AbstractTrafficSegmentProvider,
    ) -> None:
        self._row_idx = 1

        self._country_provider = country_provider
        self._operator_provider = operator_provider
        self._traffic_segment_provider = traffic_segment_provider

    def render(
        self,
        budget: Budget,
        reporting_values: list[dict[str, Any]],
        reporting_options: dict[str, Any],
    ) -> IO[bytes]:
        """Entry point method that creates xlsx workbook and writes its content to memory."""

        wb = Workbook()
        ws: Worksheet = wb.active

        self._write_reporting_options(wb, budget, reporting_options)
        self._write_reporting_values(wb, reporting_values, reporting_options)

        self._apply_default_styles_to_ws(ws)

        memory_wb = BytesIO()
        wb.save(memory_wb)
        wb.close()

        return memory_wb

    def _write_reporting_options(self, wb: Workbook, budget: Budget, options: dict[str, Any]) -> None:
        """Writes budget REPORT options."""

        ws: Worksheet = wb.active

        options_cell = f"A{self._row_idx}"
        ws[options_cell] = self.REPORT_OPTIONS_HEADER
        ws[options_cell].font = _BOLD_FONT
        self._commit_row()

        start_date, end_date = options.get("start_date"), options.get("end_date")

        start_date = start_date if start_date else budget.period.start_date
        end_date = end_date if end_date else budget.period.end_date

        home_operators_str = ""
        if options["home_operators"]:
            home_operators = self._operator_provider.get_many(operators_ids=options["home_operators"])
            home_operators_str = ", ".join(hpmn.pmn_code for hpmn in home_operators)

        partner_operators_str = ""
        if options["partner_operators"]:
            partner_operators = self._operator_provider.get_many(operators_ids=options["partner_operators"])
            partner_operators_str = ", ".join(ppmn.pmn_code for ppmn in partner_operators)

        countries_map = None

        partner_countries_str = ""
        if options["partner_countries"]:
            countries_map = {o.id: o for o in self._country_provider.get_many()}
            partner_countries_str = ", ".join(countries_map[c_id].name for c_id in options["partner_countries"])

        called_countries_str = ""
        if called_countries := options.get("called_countries", []):
            countries_map = countries_map or {o.id: o for o in self._country_provider.get_many()}
            called_countries_str = ", ".join(countries_map[c_id].name for c_id in called_countries)

        is_premium = options.get("is_premium")
        is_premium_str = str(is_premium) if isinstance(is_premium, bool) else "All"

        traffic_segments_str = ""
        if traffic_segment_ids := options.get("traffic_segments", []):
            traffic_segments = self._traffic_segment_provider.get_many(segments_ids=traffic_segment_ids)

            traffic_segment_operators_ids = list({ts.home_operator_id for ts in traffic_segments})
            traffic_segment_operators = self._operator_provider.get_many(operators_ids=traffic_segment_operators_ids)
            traffic_segment_operators_map = {op.id: op.pmn_code for op in traffic_segment_operators}

            traffic_segments_str = ", ".join(
                f"{traffic_segment_operators_map[ts.home_operator_id]}-{ts.name}" for ts in traffic_segments
            )

        row_values = (
            budget.name,
            home_operators_str,
            partner_operators_str,
            partner_countries_str,
            date_to_str(start_date),
            date_to_str(end_date),
            stringify_enum_list(options.get("traffic_types", [])),
            stringify_enum_list(options.get("traffic_directions", [])),
            stringify_enum_list(options.get("service_types", [])),
            stringify_enum_list(options.get("call_destinations", [])),
            called_countries_str,
            is_premium_str,
            traffic_segments_str,
            stringify_enum_list(options.get("imsi_count_types", [])),
            options.get("currency_code", XDR_CURRENCY_CODE),  # TODO Should be required field,
        )
        for row in zip(self.REPORT_OPTIONS_COLUMNS, row_values):
            ws.append(row)
            self._commit_row()

        # Add empty line
        ws.append([])
        self._commit_row()

    def _write_reporting_values(
        self,
        wb: Workbook,
        reporting_values: list[dict[str, Any]],
        reporting_options: dict[str, Any],
    ) -> None:
        """Writes table whole with budget REPORT values per each column."""

        ws: Worksheet = wb.active

        self._write_report_table_header(ws, reporting_options)

        for reporting_value in reporting_values:
            row = [reporting_value[column] for column in reporting_options["columns"]]
            ws.append(row)
            self._commit_row()

    def _write_report_table_header(self, ws: Worksheet, reporting_options: dict[str, Any]) -> None:
        """Writes budget REPORT table header"""

        keys = [self._REPORT_FIELDS_MAP[column] for column in reporting_options["columns"]]
        ws.append(keys)
        self._apply_style_to_row(ws, _FONT_ATTR, _BOLD_FONT)
        self._commit_row()

    def _commit_row(self) -> None:
        self._row_idx += 1

    def _apply_style_to_row(self, ws: Worksheet, style_attr: str, value: Any, row_idx: Optional[int] = None) -> None:
        """Applies provided style to provided or current row."""

        if row_idx is None:
            row_idx = self._row_idx

        for cell in ws[f"{row_idx}:{row_idx}"]:
            setattr(cell, style_attr, value)

    def _apply_default_styles_to_ws(self, ws: Worksheet) -> None:
        """Defines workbook general cell styles."""

        # set custom column width
        for let in self._alphabets_map.values():
            ws.column_dimensions[let].width = self._COLUMN_WIDTH

    def generate_filename(self) -> str:
        current_date = timezone.now().date().strftime(self.FILENAME_DATE_FORMAT)
        return f"Reporting_Dashboard_{current_date}.xlsx"
