from typing import Protocol

from nga.apps.budgets.enums import ChargeTypeEnum, UnitDirectionTypeEnum, UnitTypeEnum
from nga.core.enums import TrafficDirectionEnum, VolumeTypeEnum

__all__ = [
    "CHARGE_TYPE_FIELD_MAP",
    "VOLUME_TYPE_FIELD_MAP",
    "UNIT_TRAFFIC_DIRECTION_MAP",
    "UnitOptions",
    "get_unit_field_by_options",
]

CHARGE_TYPE_FIELD_MAP = {
    ChargeTypeEnum.NET: "charge_net",
    ChargeTypeEnum.GROSS: "charge_gross",
    ChargeTypeEnum.TAP_NET: "tap_charge_net",
    ChargeTypeEnum.TAP_GROSS: "tap_charge_gross",
}

VOLUME_TYPE_FIELD_MAP = {
    VolumeTypeEnum.ACTUAL: "volume_actual",
    VolumeTypeEnum.BILLED: "volume_billed",
}

UNIT_TRAFFIC_DIRECTION_MAP = {
    UnitDirectionTypeEnum.INBOUND: TrafficDirectionEnum.INBOUND,
    UnitDirectionTypeEnum.OUTBOUND: TrafficDirectionEnum.OUTBOUND,
}


class UnitOptions(Protocol):
    unit_type: UnitTypeEnum
    charge_type: ChargeTypeEnum
    volume_type: VolumeTypeEnum


def get_unit_field_by_options(options: UnitOptions) -> str:
    if options.unit_type == UnitTypeEnum.VOLUME:
        value_field = VOLUME_TYPE_FIELD_MAP[options.volume_type]
    else:
        value_field = CHARGE_TYPE_FIELD_MAP[options.charge_type]

    return value_field
