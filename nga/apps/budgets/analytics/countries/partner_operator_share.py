import typing
from decimal import Decimal

from django.db.models import Case, F, Sum, Value, When, Window

from nga.apps.budgets.analytics.countries.models import (
    PartnerCountryOperatorShareOptions,
    PartnerCountryOperatorShares,
    PartnerOperatorShare,
)
from nga.apps.budgets.domain.dto import BudgetTrafficParameters
from nga.apps.budgets.infra.orm import models


class PartnerCountryOperatorShareProviderProtocol(typing.Protocol):
    def get_shares(self, options: PartnerCountryOperatorShareOptions) -> PartnerCountryOperatorShares:
        """Returns collection with volume share distribution between partner operators inside specified country."""


class PartnerCountryOperatorShareORMProvider:
    def get_shares(self, options: PartnerCountryOperatorShareOptions) -> PartnerCountryOperatorShares:
        """Returns collection with volume share distribution between partner operators inside specified country."""

        budget = models.Budget.objects.filter(pk=options.budget_id).only("active_snapshot_id").first()

        budget_traffic_parameters = BudgetTrafficParameters(
            snapshot_id=budget.active_snapshot_id,
            home_operators=options.home_operators,
            partner_countries=(options.partner_country_id,),
            traffic_directions=(options.traffic_direction,),
            service_types=(options.service_type,),
            period=options.period,
        )

        qs = (
            models.BudgetTrafficRecord.objects.filter_by_parameters(budget_traffic_parameters)
            .annotate(total_volume_by_country=Window(expression=Sum("volume_actual")))
            .annotate(
                volume_by_operator=Window(
                    expression=Sum("volume_actual"),
                    partition_by=(F("partner_operator_id"),),
                )
            )
            .values("partner_operator_id")
            .annotate(
                partner_operator_share=Case(
                    When(total_volume_by_country=Value(Decimal("0")), then=Value(Decimal("0"))),
                    default=F("volume_by_operator") / F("total_volume_by_country"),
                )
            )
            .distinct("partner_operator_id")
        )

        share_values = tuple(
            PartnerOperatorShare(operator_id=r["partner_operator_id"], share=Decimal(r["partner_operator_share"]))
            for r in qs
        )

        shares = PartnerCountryOperatorShares(
            budget_id=options.budget_id,
            country_id=options.partner_country_id,
            shares=share_values,
        )

        return shares
