from dataclasses import dataclass
from decimal import Decimal
from typing import Optional, Sequence

from nga.apps.budgets.enums import ChargeTypeEnum, UnitDirectionTypeEnum, UnitTypeEnum
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum, VolumeTypeEnum
from nga.core.types import DatePeriod

__all__ = [
    "BudgetCountriesValuesOptions",
    "BudgetCountryValueRecord",
    "CountryOperatorValueRecord",
    "CountryOperatorsValuesOptions",
    "CountryOperatorsValues",
    "BudgetCountriesValues",
    "PartnerCountryOperatorShares",
    "PartnerCountryOperatorShareOptions",
    "PartnerOperatorShare",
]


@dataclass(kw_only=True)
class BudgetCountriesValuesOptions:
    budget_id: int

    agreement_id: Optional[int]

    volume_type: VolumeTypeEnum = VolumeTypeEnum.ACTUAL

    period: Optional[DatePeriod] = None
    service_type: Optional[ServiceTypeEnum] = None

    charge_type: ChargeTypeEnum = ChargeTypeEnum.GROSS
    currency_code: Optional[str] = None

    unit_type: UnitTypeEnum = UnitTypeEnum.CHARGE
    unit_direction_type: UnitDirectionTypeEnum = UnitDirectionTypeEnum.NET_POSITION

    include_forecasted: bool = True

    home_operators: Optional[Sequence[int]] = None
    partner_operators: Optional[Sequence[int]] = None
    partner_countries: Optional[Sequence[int]] = None

    @property
    def should_apply_exchange_rates(self) -> bool:
        return bool(self.currency_code is not None)


@dataclass
class BudgetCountryValueRecord:
    country_id: int
    value: Decimal


@dataclass
class CountryOperatorValueRecord:
    operator_id: int
    value: Decimal
    previous_year_value: Decimal


@dataclass(kw_only=True)
class CountryOperatorsValuesOptions(BudgetCountriesValuesOptions):
    country_id: int


@dataclass
class BudgetCountriesValues:
    records: tuple[BudgetCountryValueRecord, ...]

    tiers: tuple[float, ...]

    options: BudgetCountriesValuesOptions

    @property
    def total_records(self) -> int:
        return len(self.records)


@dataclass
class CountryOperatorsValues:
    records: tuple[CountryOperatorValueRecord, ...]
    options: CountryOperatorsValuesOptions

    @property
    def total_records(self) -> int:
        return len(self.records)


# Partner country operator share models


@dataclass
class PartnerCountryOperatorShareOptions:
    budget_id: int

    home_operators: tuple[int, ...]
    partner_country_id: int

    traffic_direction: TrafficDirectionEnum
    service_type: ServiceTypeEnum
    period: DatePeriod

    def __copy__(self) -> "PartnerCountryOperatorShareOptions":
        return PartnerCountryOperatorShareOptions(
            budget_id=self.budget_id,
            home_operators=self.home_operators,
            partner_country_id=self.partner_country_id,
            traffic_direction=self.traffic_direction,
            service_type=self.service_type,
            period=self.period,
        )


@dataclass
class PartnerOperatorShare:
    operator_id: int
    share: Decimal


@dataclass
class PartnerCountryOperatorShares:
    budget_id: int
    country_id: int
    shares: tuple[PartnerOperatorShare, ...]
