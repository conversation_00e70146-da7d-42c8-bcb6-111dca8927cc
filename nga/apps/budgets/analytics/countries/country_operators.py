from abc import ABC, abstractmethod
from datetime import date
from decimal import Decimal
from typing import Optional, Sequence

import pandas as pd
from django.db.models import F

from nga.apps.budgets.analytics.calc_utils import calculate_net_position
from nga.apps.budgets.analytics.countries.models import (
    CountryOperatorsValues,
    CountryOperatorsValuesOptions,
    CountryOperatorValueRecord,
)
from nga.apps.budgets.analytics.mappings import UNIT_TRAFFIC_DIRECTION_MAP, get_unit_field_by_options
from nga.apps.budgets.analytics.types import PreviousYearTrafficRecordValuesDict
from nga.apps.budgets.domain.dto import BudgetTrafficParameters
from nga.apps.budgets.enums import UnitDirectionTypeEnum
from nga.apps.budgets.infra.orm import models
from nga.apps.references.types import TrafficRecordValuesDict
from nga.core.enums import TrafficDirectionEnum
from nga.core.types import MONTHS_IN_A_YEAR, DatePeriod, Month


class _CountryOperatorTrafficRecord(TrafficRecordValuesDict):
    operator_id: int

    traffic_month: date

    traffic_direction: TrafficDirectionEnum


class _PartnerOperatorTrafficRecord(TrafficRecordValuesDict, PreviousYearTrafficRecordValuesDict):
    operator_id: int


class AbstractCountryOperatorsValuesProvider(ABC):
    @abstractmethod
    def get_values(self, options: CountryOperatorsValuesOptions) -> CountryOperatorsValues:
        """
        Aggregates budget traffic records by partner operators inside provided country.
        Applies exchange rates and CountryOperatorsValuesOptions (filters, charge settings).
        """


class CountryOperatorsValuesProvider(AbstractCountryOperatorsValuesProvider):
    def get_values(self, options: CountryOperatorsValuesOptions) -> CountryOperatorsValues:
        """
        Aggregates budget traffic records by partner operators inside provided country.
        Applies exchange rates and CountryOperatorsValuesOptions (filters, charge settings).
        """

        traffic_months = self._calculate_period(options.period)

        budget = models.Budget.objects.get(pk=options.budget_id)

        traffic_direction = UNIT_TRAFFIC_DIRECTION_MAP.get(options.unit_direction_type)

        parameters = BudgetTrafficParameters(
            snapshot_id=budget.active_snapshot_id,
            home_operators=options.home_operators,
            partner_operators=options.partner_operators,
            partner_countries=[options.country_id],
            service_types=[options.service_type] if options.service_type else [],
            traffic_months=traffic_months,
            traffic_directions=[traffic_direction] if traffic_direction else [],
            include_forecasted=options.include_forecasted,
        )

        values = list(
            models.BudgetTrafficRecord.objects.filter_by_parameters(parameters)
            .apply_currency_exchange_rates(budget=budget, currency_code=options.currency_code)
            .annotate(operator_id=F("partner_operator_id"))
            .values("operator_id", "traffic_direction", "traffic_month")
            .aggregate_volumes()
            .aggregate_charges(exchange_rates_applied=options.should_apply_exchange_rates)
            .order_by("operator_id")
        )

        raw_values = self._calculate_country_operators_values(values, options)

        operators_records = self._map_operators_values_to_records(raw_values, options)

        country_operators_values = CountryOperatorsValues(
            records=operators_records,
            options=options,
        )

        return country_operators_values

    @classmethod
    def _calculate_period(cls, period: Optional[DatePeriod]) -> Optional[tuple[Month, ...]]:
        if period is None:
            return None

        if period.total_months <= MONTHS_IN_A_YEAR:
            traffic_months = (
                *tuple(period.previous_year),
                *tuple(period),
            )
        else:
            traffic_months = tuple(period)

        return traffic_months

    @classmethod
    def _calculate_country_operators_values(
        cls,
        values: list[_CountryOperatorTrafficRecord],
        options: CountryOperatorsValuesOptions,
    ) -> Sequence[_PartnerOperatorTrafficRecord]:

        if not values:
            return []

        records_df = pd.DataFrame(values)

        period = options.period

        if period and period.total_months <= MONTHS_IN_A_YEAR:
            requested_year_df = records_df[
                (records_df["traffic_month"] >= period.start_date) & (records_df["traffic_month"] <= period.end_date)
            ]

            previous_year_df = records_df.loc[records_df["traffic_month"] < period.start_date]

            records_df = cls._aggregate_by_operator_id(requested_year_df, options)
            previous_year_records_df = cls._aggregate_by_operator_id(previous_year_df, options)

            records_df = records_df.merge(
                previous_year_records_df,
                how="outer",
                on=["operator_id"],
                suffixes=("", "_previous_year"),
            )
            records_df = records_df.fillna(Decimal("0")).infer_objects(copy=False)
        else:
            records_df = cls._aggregate_by_operator_id(records_df, options)

            records_df["volume_actual_previous_year"] = Decimal("0")
            records_df["volume_billed_previous_year"] = Decimal("0")
            records_df["charge_net_previous_year"] = Decimal("0")
            records_df["charge_gross_previous_year"] = Decimal("0")
            records_df["tap_charge_net_previous_year"] = Decimal("0")
            records_df["tap_charge_gross_previous_year"] = Decimal("0")

        records: Sequence[_PartnerOperatorTrafficRecord] = records_df.to_dict("records")

        return records

    @classmethod
    def _aggregate_by_operator_id(
        cls,
        records_df: pd.DataFrame,
        options: CountryOperatorsValuesOptions,
    ) -> pd.DataFrame:
        """Aggregates traffic record values by operator_id and traffic_direction."""

        records_df = (
            records_df.groupby(by=["operator_id", "traffic_direction"], group_keys=True)[
                [
                    "volume_actual",
                    "volume_billed",
                    "charge_net",
                    "charge_gross",
                    "tap_charge_net",
                    "tap_charge_gross",
                ]
            ]
            .apply(lambda x: x.sum())
            .reset_index()
        )

        if options.unit_direction_type == UnitDirectionTypeEnum.NET_POSITION:
            records_df = calculate_net_position(records_df, ["operator_id"])

        return records_df

    @classmethod
    def _map_operators_values_to_records(
        cls,
        values: Sequence[_PartnerOperatorTrafficRecord],
        options: CountryOperatorsValuesOptions,
    ) -> tuple[CountryOperatorValueRecord, ...]:
        """
        Maps values to CountryOperatorValueRecord, by choosing field for a result value,
        charge/volume.
        """

        value_field = get_unit_field_by_options(options)

        previous_year_value_field = f"{value_field}_previous_year"

        operators_values = (
            CountryOperatorValueRecord(
                operator_id=r["operator_id"],
                value=r[value_field],  # type: ignore[literal-required]
                previous_year_value=r[previous_year_value_field],  # type: ignore[literal-required]
            )
            for r in values
        )

        return tuple(operators_values)
