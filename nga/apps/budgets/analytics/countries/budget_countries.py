from abc import ABC, abstractmethod
from typing import Sequence

import jenkspy
import pandas as pd
from django.db.models import F

from nga.apps.budgets.analytics.calc_utils import calculate_net_position
from nga.apps.budgets.analytics.countries.models import (
    BudgetCountriesValues,
    BudgetCountriesValuesOptions,
    BudgetCountryValueRecord,
)
from nga.apps.budgets.analytics.mappings import UNIT_TRAFFIC_DIRECTION_MAP, get_unit_field_by_options
from nga.apps.budgets.domain.dto import BudgetTrafficParameters
from nga.apps.budgets.enums import UnitDirectionTypeEnum
from nga.apps.budgets.infra.orm import models
from nga.apps.references.types import TrafficRecordValuesDict

__all__ = [
    "AbstractBudgetCountriesValuesProvider",
    "BudgetCountriesValuesProvider",
]


class AbstractBudgetCountriesValuesProvider(ABC):
    @abstractmethod
    def get_values(self, options: BudgetCountriesValuesOptions) -> BudgetCountriesValues:
        """Aggregates budget traffic values by partner countries."""


class BudgetCountriesValuesProvider(AbstractBudgetCountriesValuesProvider):
    TOTAL_TIERS = 5

    def get_values(self, options: BudgetCountriesValuesOptions) -> BudgetCountriesValues:
        """Aggregates budget traffic values by partner countries."""

        traffic_direction = UNIT_TRAFFIC_DIRECTION_MAP.get(options.unit_direction_type)

        budget = models.Budget.objects.get(pk=options.budget_id)

        parameters = BudgetTrafficParameters(
            snapshot_id=budget.active_snapshot_id,
            home_operators=options.home_operators,
            partner_operators=options.partner_operators,
            partner_countries=options.partner_countries,
            period=options.period,
            service_types=[options.service_type] if options.service_type else [],
            traffic_directions=[traffic_direction] if traffic_direction else [],
            include_forecasted=options.include_forecasted,
        )

        values = list(
            models.BudgetTrafficRecord.objects.filter_by_parameters(parameters)
            .apply_currency_exchange_rates(budget=budget, currency_code=options.currency_code)
            .annotate(country_id=F("partner_operator__country_id"))
            .values("country_id", "traffic_direction")
            .aggregate_volumes()
            .aggregate_charges(exchange_rates_applied=options.should_apply_exchange_rates)
            .order_by("country_id")
        )

        if values and options.unit_direction_type == UnitDirectionTypeEnum.NET_POSITION:
            records_df = calculate_net_position(pd.DataFrame(values), ["country_id"])
            values = records_df.to_dict("records")

        countries_records = self._map_countries_values_to_records(values, options)

        tiers = self._calculate_tiers(countries_records)

        countries_values = BudgetCountriesValues(
            records=countries_records,
            options=options,
            tiers=tiers,
        )

        return countries_values

    @classmethod
    def _map_countries_values_to_records(
        cls,
        values: Sequence["_CountryTrafficRecord"],
        options: BudgetCountriesValuesOptions,
    ) -> tuple[BudgetCountryValueRecord, ...]:
        """
        Maps sequence of _CountryTrafficRecord to tuple of BudgetCountryValueRecord, by applying
        logic from BudgetCountriesValuesOptions:
            - selects values based on options.unit_type (volume or charge)
            - selects appropriate charge value based on options.charge_type
                if options.unit_type is "CHARGE"
        """

        value_field = get_unit_field_by_options(options)

        country_values = (
            BudgetCountryValueRecord(country_id=r["country_id"], value=r[value_field])  # type: ignore[literal-required]
            for r in values
        )

        return tuple(country_values)

    @classmethod
    def _calculate_tiers(cls, countries_records: Sequence[BudgetCountryValueRecord]) -> tuple[float, ...]:
        """
        For tiers calculation Jenks natural breaks optimization was used. Returns array of floats that represents breaks
        of input sequence.
        """

        values = tuple(float(r.value) for r in countries_records)

        unique_values = set(values)

        if len(unique_values) <= cls.TOTAL_TIERS:
            tiers = sorted(unique_values)
        else:
            tiers = jenkspy.jenks_breaks(values, n_classes=cls.TOTAL_TIERS)

        return tuple(tiers)


class _CountryTrafficRecord(TrafficRecordValuesDict):
    country_id: int
