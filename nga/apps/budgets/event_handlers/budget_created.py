import typing

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.budget_background_jobs.command_factories import ScheduleBudgetBackgroundJobCommandFactory
from nga.apps.budgets.domain import Budget
from nga.apps.budgets.domain.dto import BudgetParametersFilters
from nga.apps.budgets.domain.events import BudgetCreatedEvent
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.infra.tasks import create_mapped_exchange_rates_after_budget_created_task
from nga.apps.common.consts import DATE_FORMAT
from nga.internal.domain import Event


class FillBudgetWithAgreementsOnBudgetCreatedEvent:
    """After budget was created it must be filled with agreements that fit into that budget and activate them."""

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
    ) -> None:

        self._mediator = mediator

        self._budget_agreement_repository = budget_agreement_repository

    def handle(self, event: Event) -> None:
        budget_created = typing.cast(BudgetCreatedEvent, event)

        budget = budget_created.budget

        agreement_ids_to_copy = self._get_agreement_ids_to_copy(budget)

        if len(agreement_ids_to_copy) == 0:
            return None

        cmd = ScheduleBudgetBackgroundJobCommandFactory.fill_budget_with_agreements(
            budget_id=budget.id,
            budget_agreement_ids=agreement_ids_to_copy,
        )

        self._mediator.send(cmd)

    def _get_agreement_ids_to_copy(self, budget: Budget) -> list[int]:
        budget_parameters_filters = BudgetParametersFilters(home_operators=budget.home_operators)

        suitable_budget_agreements = self._budget_agreement_repository.get_many(
            budget_parameters=budget_parameters_filters,
            start_date=budget.period.start_date,
            end_date=budget.period.end_date,
            statuses=list(AgreementStatusEnum.get_confirmed_statuses()),
        )

        unique_agreement_list = list({ba.agreement_id: ba for ba in suitable_budget_agreements}.values())

        agreement_ids_to_copy = []

        for budget_agreement in unique_agreement_list:
            if budget_agreement.period not in budget.period:
                continue

            agreement_ids_to_copy.append(budget_agreement.id)

        return agreement_ids_to_copy


class ExpandMasterBudgetOnBudgetCreatedEvent:
    """
    After new simulation budget was created we need to update master budget. If simulation budget period exceeds
    master budget period, master budget period must be expanded to cover simulation period.
    """

    @inject
    def __init__(self, budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]]) -> None:
        self._budget_repository = budget_repository

    def handle(self, event: Event) -> None:
        budget_created = typing.cast(BudgetCreatedEvent, event)

        budget = budget_created.budget

        if budget.is_master:
            return None

        master_budget = self._budget_repository.get_master()

        period = budget.period

        if period in master_budget.period:
            return None

        if period.start_date < master_budget.period.start_date:
            master_budget.period.start_date = period.start_date

        if period.end_date > master_budget.period.end_date:
            master_budget.period.end_date = period.end_date

            str_date = master_budget.period.end_date.strftime(DATE_FORMAT)

            create_mapped_exchange_rates_after_budget_created_task.delay(str_date)

        self._budget_repository.save(master_budget)
