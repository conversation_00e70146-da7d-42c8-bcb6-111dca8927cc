import logging

from nga.apps.budgets.domain.events import BudgetCreatedEvent
from nga.apps.budgets.event_handlers.budget_created import (
    ExpandMasterBudgetOnBudgetCreatedEvent,
    FillBudgetWithAgreementsOnBudgetCreatedEvent,
)
from nga.internal.domain.event_dispatcher import EventSubscriber


def register_budget_domain_event_handlers(event_dispatcher: EventSubscriber) -> None:
    event_dispatcher.subscribe(BudgetCreatedEvent, FillBudgetWithAgreementsOnBudgetCreatedEvent)
    event_dispatcher.subscribe(BudgetCreatedEvent, ExpandMasterBudgetOnBudgetCreatedEvent)

    logging.info("budget domain event handlers are registered")
