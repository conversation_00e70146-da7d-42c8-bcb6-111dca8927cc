from drf_yasg.utils import swagger_serializer_method
from rest_framework import serializers
from rest_framework.utils.serializer_helpers import ReturnDict
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.budgets.dto import (
    BudgetPartnerCountryOperatorShares,
    PartnerCountryOperatorOverallShares,
    PartnerOperatorOverallShareValues,
)
from nga.apps.common.serializer_fields import EnumChoiceField, MoneyField, YearMonthField
from nga.apps.common.serializer_mixins import CountryMapperSerializerMixin, OperatorMapperSerializerMixin
from nga.apps.references.api.serializers import CountrySerializer, OperatorSerializer
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum


class PartnerOperatorOverallShareValuesSerializer(
    OperatorMapperSerializerMixin,
    DataclassSerializer[PartnerOperatorOverallShareValues],
):
    operator = serializers.SerializerMethodField()

    lhm_share = MoneyField()
    actual_share = MoneyField()
    retrospective_share = MoneyField()

    class Meta:
        dataclass = PartnerOperatorOverallShareValues
        fields = (
            "operator",
            "lhm_share",
            "actual_share",
            "retrospective_share",
        )

    @swagger_serializer_method(OperatorSerializer)
    def get_operator(self, instance: PartnerOperatorOverallShareValues) -> ReturnDict:
        operator = self.operators_map[instance.operator_id]

        return OperatorSerializer(operator).data


class PartnerCountryOperatorOverallSharesSerializer(
    CountryMapperSerializerMixin,
    DataclassSerializer[PartnerCountryOperatorOverallShares],
):
    country = serializers.SerializerMethodField()

    operator_shares = PartnerOperatorOverallShareValuesSerializer(many=True)

    class Meta:
        dataclass = PartnerCountryOperatorOverallShares
        fields = (
            "country",
            "operator_shares",
        )

    @swagger_serializer_method(CountrySerializer)
    def get_country(self, instance: PartnerCountryOperatorOverallShares) -> ReturnDict:
        country = self.countries_map[instance.country_id]

        return CountrySerializer(country).data


class BudgetPartnerCountryOperatorSharesSerializer(DataclassSerializer[BudgetPartnerCountryOperatorShares]):
    country_operator_shares = PartnerCountryOperatorOverallSharesSerializer(many=True)

    class Meta:
        dataclass = BudgetPartnerCountryOperatorShares
        fields = (
            "budget_id",
            "country_operator_shares",
        )


class BudgetPartnerCountryOperatorSharesQuerySerializer(serializers.Serializer):
    home_operators = serializers.ListField(child=serializers.IntegerField(), required=True)
    partner_countries = serializers.ListField(child=serializers.IntegerField(), required=True)

    start_date = YearMonthField(required=True)
    end_date = YearMonthField(required=True)

    service_type = EnumChoiceField(enum_class=ServiceTypeEnum)

    traffic_direction = EnumChoiceField(enum_class=TrafficDirectionEnum)

    class Meta:
        fields = (
            "home_operators",
            "partner_countries",
            "start_date",
            "end_date",
            "service_type",
            "traffic_direction",
        )
