from typing import Any, Optional

from dependency_injector.wiring import Closing, Provide, inject
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.fields import Serializer<PERSON>ethodField

from nga.apps.budgets.enums import ReportingAllowedColumnsEnum
from nga.apps.common.serializer_fields import <PERSON><PERSON>rencyCodeField, EnumChoiceField, MoneyField, YearMonthField
from nga.apps.common.serializers import BudgetQuerySerializer
from nga.apps.references.domain.models import Country, Operator, TrafficSegment
from nga.apps.references.providers import (
    AbstractCountryProvider,
    AbstractOperatorProvider,
    AbstractTrafficSegmentProvider,
)
from nga.core.enums import (
    CallDestinationEnum,
    IMSICountTypeEnum,
    ServiceTypeEnum,
    TrafficDirectionEnum,
    TrafficTypeEnum,
    get_choices_values,
)

__all__ = [
    "BudgetReportSerializer",
    "BudgetReportQuerySerializer",
]


class BudgetReportQuerySerializer(BudgetQuerySerializer):
    default_error_messages = {
        **serializers.Serializer.default_error_messages,
        "dates_not_set": _("both start_date and end_date must be provided."),
    }
    ASC = "asc"
    DESC = "desc"
    ASC_SIGN = "+"
    DESC_SIGN = "-"
    SORT_DIRECTION = (ASC, DESC)

    columns = serializers.ListField(
        child=serializers.ChoiceField(choices=get_choices_values(ReportingAllowedColumnsEnum)), required=True
    )
    sort_field = serializers.ChoiceField(choices=get_choices_values(ReportingAllowedColumnsEnum), required=False)
    sort = serializers.ChoiceField(choices=SORT_DIRECTION, required=False)
    sort_sign = serializers.SerializerMethodField(
        source="get_sort_sign"
    )  # TODO let's think about ordering field from paginator class  # noqa

    start_date = YearMonthField(required=False)
    end_date = YearMonthField(required=False)

    traffic_types = serializers.ListField(
        child=EnumChoiceField(enum_class=TrafficTypeEnum),
        required=False,
        allow_empty=False,
    )
    traffic_directions = serializers.ListField(
        child=EnumChoiceField(enum_class=TrafficDirectionEnum),
        required=False,
        allow_empty=False,
    )
    service_types = serializers.ListField(
        child=EnumChoiceField(enum_class=ServiceTypeEnum),
        required=False,
        allow_empty=False,
    )

    imsi_count_types = serializers.ListField(
        child=EnumChoiceField(enum_class=IMSICountTypeEnum),
        required=False,
        allow_empty=False,
    )

    call_destinations = serializers.ListField(
        child=EnumChoiceField(enum_class=CallDestinationEnum),
        required=False,
        allow_empty=False,
    )
    called_countries = serializers.ListField(
        child=serializers.IntegerField(min_value=1),
        required=False,
        allow_empty=False,
    )

    is_premium = serializers.BooleanField(required=False, default=None, allow_null=True)

    traffic_segments = serializers.ListField(
        child=serializers.IntegerField(min_value=1),
        required=False,
        allow_empty=False,
    )

    currency_code = CurrencyCodeField(min_length=True, required=False)

    class Meta:
        fields = (
            "columns",
            "sort_field",
            "sort",
            "sort_sign",
            "home_operators",
            "partner_operators",
            "partner_countries",
            "start_date",
            "end_date",
            "traffic_types",
            "traffic_directions",
            "service_types",
            "call_destinations",
            "called_countries",
            "is_premium",
            "traffic_segments",
            "currency_code",
        )

    def validate(self, data: dict[str, Any]) -> dict[str, Any]:
        """Check does currency exist and that start_date and end_date are provided both."""

        dates = [data.get("start_date"), data.get("end_date")]

        if any(dates) and not all(dates):
            raise serializers.ValidationError(self.default_error_messages["dates_not_set"])

        data["sort_sign"] = self.get_sort_sign(data)
        data["sort_field"] = self.get_sort_field(data)

        return data

    def get_sort_sign(self, obj: dict[str, Any]) -> Optional[str]:
        sort = obj.get("sort")

        if sort is None:
            return None

        return self.ASC_SIGN if sort == self.ASC else self.DESC_SIGN

    def get_sort_field(self, obj: dict[str, Any]) -> Optional[str]:
        sort_field = obj.get("sort_field")
        if sort_field is None:
            return None

        columns = obj["columns"]

        if sort_field not in columns:
            sort_field = None

        return self.fields["sort_field"].to_representation(value=sort_field)


class BudgetReportSerializer(serializers.Serializer):
    home_operator_pmn = SerializerMethodField()
    partner_operator_pmn = SerializerMethodField()
    partner_country = SerializerMethodField()

    traffic_type = EnumChoiceField(enum_class=TrafficTypeEnum, required=False, allow_null=True)
    traffic_month = YearMonthField(required=False, allow_null=True)
    traffic_direction = EnumChoiceField(enum_class=TrafficDirectionEnum)
    traffic_segment_name = SerializerMethodField()

    imsi_count_type = EnumChoiceField(enum_class=IMSICountTypeEnum, required=False, allow_null=True)

    service_type = EnumChoiceField(enum_class=ServiceTypeEnum)

    call_destination = EnumChoiceField(enum_class=CallDestinationEnum, required=False, allow_null=True)
    called_country = SerializerMethodField()
    is_premium = serializers.BooleanField(required=False, allow_null=True)

    volume_actual = MoneyField(required=False, allow_null=True)
    volume_billed = MoneyField(required=False, allow_null=True)

    tap_charge_net = MoneyField(required=False, allow_null=True)
    tap_charge_gross = MoneyField(required=False, allow_null=True)

    charge_net = MoneyField(required=False, allow_null=True)
    charge_gross = MoneyField()

    discount_net = MoneyField(required=False, allow_null=True)
    discount_gross = MoneyField(required=False, allow_null=True)

    tap_rate_net_volume_actual = MoneyField(required=False, allow_null=True)
    tap_rate_gross_volume_actual = MoneyField(required=False, allow_null=True)

    discounted_rate_net_volume_actual = MoneyField(required=False, allow_null=True)
    discounted_rate_gross_volume_actual = MoneyField(required=False, allow_null=True)

    tap_rate_net_volume_billed = MoneyField(required=False, allow_null=True)
    tap_rate_gross_volume_billed = MoneyField(required=False, allow_null=True)

    discounted_rate_net_volume_billed = MoneyField(required=False, allow_null=True)
    discounted_rate_gross_volume_billed = MoneyField(required=False, allow_null=True)

    class Meta:
        fields = (
            "home_operator_pmn",
            "partner_operator_pmn",
            "partner_country",
            "traffic_month",
            "traffic_type",
            "traffic_direction",
            "service_type",
            "call_destination",
            "called_country",
            "is_premium",
            "traffic_segment_name",
            "volume_actual",
            "volume_billed",
            "tap_charge_net",
            "tap_charge_gross",
            "charge_net",
            "charge_gross",
            "discount_net",
            "discount_gross",
            "tap_rate_net_volume_actual",
            "tap_rate_gross_volume_actual",
            "discounted_rate_net_volume_actual",
            "discounted_rate_gross_volume_actual",
            "tap_rate_net_volume_billed",
            "tap_rate_gross_volume_billed",
            "discounted_rate_net_volume_billed",
            "discounted_rate_gross_volume_billed",
        )

    @inject
    def __init__(
        self,
        *args: Any,
        operator_provider: AbstractOperatorProvider = Closing[Provide["operator_provider"]],
        country_provider: AbstractCountryProvider = Closing[Provide["country_provider"]],
        traffic_segment_provider: AbstractTrafficSegmentProvider = Closing[Provide["traffic_segment_provider"]],
        **kwargs: Any,
    ):
        """Creates instance only from fields that is in configured list."""

        fields = kwargs.pop("fields", None)

        super().__init__(*args, **kwargs)

        self._operator_provider = operator_provider
        self._operators_map: dict[int, Operator] = {}

        self._country_provider = country_provider
        self._countries_map: dict[int, Country] = {}

        self._traffic_segments_map: dict[int, TrafficSegment] = {}
        self._traffic_segment_provider = traffic_segment_provider

        if fields:
            allowed_fields = set(fields)
            existing_fields = set(self.fields.keys())

            for field_name in existing_fields - allowed_fields:
                self.fields.pop(field_name)

    @property
    def operators_map(self) -> dict[int, Operator]:
        if not self._operators_map:
            self._operators_map = {o.id: o for o in self._operator_provider.get_many()}

        return self._operators_map

    @property
    def countries_map(self) -> dict[int, Country]:
        if not self._countries_map:
            self._countries_map = {o.id: o for o in self._country_provider.get_many()}

        return self._countries_map

    @property
    def traffic_segments_map(self) -> dict[int, TrafficSegment]:
        if not self._traffic_segments_map:
            self._traffic_segments_map = {o.id: o for o in self._traffic_segment_provider.get_many()}

        return self._traffic_segments_map

    def get_home_operator_pmn(self, obj: dict[str, Any]) -> Optional[str]:
        hpmn_id = obj.get("home_operator_id")

        return self.operators_map[hpmn_id].pmn_code if hpmn_id else None

    def get_partner_operator_pmn(self, obj: dict[str, Any]) -> Optional[str]:
        ppmn_id = obj.get("partner_operator_id")

        return self.operators_map[ppmn_id].pmn_code if ppmn_id else None

    def get_partner_country(self, obj: dict[str, Any]) -> Optional[str]:
        ppmn_id = obj.get("partner_operator_id")

        if not ppmn_id:
            return None

        country_id = self.operators_map[ppmn_id].country_id if ppmn_id else None

        return self.countries_map[country_id].name if country_id else None

    def get_called_country(self, obj: dict[str, Any]) -> Optional[str]:
        country_id = obj.get("called_country_id")

        return self.countries_map[country_id].name if country_id else None

    def get_traffic_segment_name(self, obj: dict[str, Any]) -> Optional[str]:
        traffic_segment_id = obj.get("traffic_segment_id")

        return self.traffic_segments_map[traffic_segment_id].name if traffic_segment_id else None
