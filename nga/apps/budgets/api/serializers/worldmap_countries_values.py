from typing import Any

from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.budgets.analytics.countries import BudgetCountriesValues, BudgetCountryValueRecord
from nga.apps.budgets.enums import ChargeTypeEnum, UnitDirectionTypeEnum, UnitTypeEnum
from nga.apps.common.serializer_fields import CurrencyCodeField, EnumChoiceField, MoneyField, YearMonthField
from nga.apps.common.serializers import BudgetQuerySerializer
from nga.core.enums import ServiceTypeEnum, VolumeTypeEnum


class BudgetCountriesValuesQuerySerializer(BudgetQuerySerializer):
    default_error_messages = {
        **serializers.Serializer.default_error_messages,
        "dates_not_set": _("both start_date and end_date must be provided."),
    }

    budgets_ids = serializers.ListField(child=serializers.IntegerField(), required=False)

    agreement_ids = serializers.ListField(child=serializers.IntegerField(), required=False)

    start_date = YearMonthField(required=True)
    end_date = YearMonthField(required=True)

    service_type = EnumChoiceField(enum_class=ServiceTypeEnum)

    charge_type = EnumChoiceField(
        enum_class=ChargeTypeEnum,
        required=False,
        default=ChargeTypeEnum.GROSS,
    )

    volume_type = EnumChoiceField(
        enum_class=VolumeTypeEnum,
        required=False,
        default=VolumeTypeEnum.ACTUAL,
    )

    currency_code = CurrencyCodeField(required=False, allow_null=True)

    unit_type = EnumChoiceField(enum_class=UnitTypeEnum)
    unit_direction_type = EnumChoiceField(
        enum_class=UnitDirectionTypeEnum,
        default=UnitDirectionTypeEnum.NET_POSITION,
    )

    include_forecasted = serializers.BooleanField(default=True)

    class Meta:
        fields = (
            "budgets_ids",
            "agreement_ids",
            "start_date",
            "end_date",
            "service_type",
            "charge_type",
            "currency_code",
            "unit_type",
            "unit_direction_type",
            "include_forecasted",
            "home_operators",
            "partner_operators",
            "partner_countries",
        )

    def validate(self, data: dict[str, Any]) -> dict[str, Any]:
        budget_ids = data.get("budgets_ids")
        agreement_ids = data.get("agreement_ids")

        if not (budget_ids or agreement_ids):
            raise ValidationError("budgets_ids or agreement_ids must be specified")

        return data


class BudgetCountryValueRecordSerializer(DataclassSerializer[BudgetCountryValueRecord]):
    country_id = serializers.IntegerField()
    value = MoneyField()

    class Meta:
        dataclass = BudgetCountryValueRecord
        fields = ("country_id", "value")


class BudgetCountriesValuesSerializer(DataclassSerializer[BudgetCountriesValues]):
    budget_id = serializers.IntegerField(source="options.budget_id")
    agreement_id = serializers.IntegerField(source="options.agreement_id")

    records = BudgetCountryValueRecordSerializer(many=True)

    tiers = serializers.ListField(child=MoneyField())

    class Meta:
        fields = (
            "budget_id",
            "agreement_id",
            "records",
            "tiers",
        )
        dataclass = BudgetCountriesValues


class BudgetCountriesValuesResponseSerializer(serializers.Serializer):
    data = BudgetCountriesValuesSerializer(many=True)

    class Meta:
        fields = ("data",)
