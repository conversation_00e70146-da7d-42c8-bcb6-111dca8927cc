from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.budgets.api.schemas import BudgetParametersSchema
from nga.apps.budgets.enums import BudgetTypeEnum
from nga.apps.common.serializer_fields import EnumC<PERSON>iceField, MonthField, YearM<PERSON>h<PERSON>ield
from nga.apps.references.api.serializers import OperatorSerializer

__all__ = [
    "BudgetParametersSchemaSerializer",
]


class BudgetParametersSchemaSerializer(DataclassSerializer[BudgetParametersSchema]):
    start_date = YearMonthField()
    end_date = YearMonthField()

    last_historical_month = MonthField()

    home_operators = OperatorSerializer(many=True)

    type = EnumChoiceField(enum_class=BudgetTypeEnum)

    class Meta:
        dataclass = BudgetParametersSchema
        fields = (
            "id",
            "name",
            "description",
            "home_operators",
            "start_date",
            "end_date",
            "last_historical_month",
            "is_master",
            "type",
            "created_at",
            "updated_at",
        )
