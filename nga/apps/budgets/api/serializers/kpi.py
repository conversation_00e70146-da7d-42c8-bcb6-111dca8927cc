from typing import Any

from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.budgets.analytics import kpi
from nga.apps.budgets.enums import ChargeTypeEnum
from nga.apps.common.serializer_fields import <PERSON>urrencyCodeField, EnumChoiceField, MoneyField, YearMonthField
from nga.apps.common.serializers import BudgetQuerySerializer
from nga.core.enums import ServiceTypeEnum, VolumeTypeEnum, get_choices_values

__all__ = [
    "BudgetKPITotalsSerializer",
    "BudgetKPITotalChargesSerializer",
    "BudgetKPIValuesQuerySerializer",
    "BudgetKPIValuesSerializer",
    "ServiceTypeKPIValuesSerializer",
]


class BudgetKPIValuesQuerySerializer(BudgetQuerySerializer):
    default_error_messages = {
        **serializers.Serializer.default_error_messages,
        "dates_not_set": _("both start_date and end_date must be provided."),
    }

    charge_type = serializers.ChoiceField(
        choices=get_choices_values(ChargeTypeEnum),
        default=ChargeTypeEnum.NET.value,
    )

    volume_type = EnumChoiceField(
        enum_class=VolumeTypeEnum,
        default=VolumeTypeEnum.ACTUAL,
    )

    start_date = YearMonthField(required=False)
    end_date = YearMonthField(required=False)

    currency_code = CurrencyCodeField(required=False)

    class Meta:
        fields = (
            "home_operators",
            "partner_operators",
            "partner_countries",
            "charge_type",
            "start_date",
            "end_date",
            "currency_code",
        )

    def validate(self, data: dict[str, Any]) -> dict[str, Any]:
        """Check that start_date and end_date are provided both."""

        dates = [data.get("start_date"), data.get("end_date")]

        if any(dates) and not all(dates):
            raise serializers.ValidationError(self.default_error_messages["dates_not_set"])

        return data


class ServiceTypeKPIValuesSerializer(DataclassSerializer[kpi.ServiceTypeKPIValues]):
    service_type = EnumChoiceField(enum_class=ServiceTypeEnum)

    inbound_charge = MoneyField()
    inbound_volume = MoneyField()
    inbound_rate = MoneyField()

    outbound_charge = MoneyField()
    outbound_volume = MoneyField()
    outbound_rate = MoneyField()

    net_position_charge = MoneyField()
    net_position_volume = MoneyField()

    class Meta:
        dataclass = kpi.ServiceTypeKPIValues
        fields = (
            "service_type",
            "inbound_charge",
            "inbound_volume",
            "inbound_rate",
            "outbound_charge",
            "outbound_volume",
            "outbound_rate",
            "net_position_charge",
            "net_position_volume",
        )


class BudgetKPITotalChargesSerializer(DataclassSerializer[kpi.BudgetKPITotalCharges]):
    inbound = MoneyField()
    outbound = MoneyField()
    net_position = MoneyField()

    class Meta:
        dataclass = kpi.BudgetKPITotalCharges
        fields = ("inbound", "outbound", "net_position")


class BudgetKPIValuesSerializer(DataclassSerializer[kpi.BudgetKPIValues]):
    service_types = ServiceTypeKPIValuesSerializer(many=True)
    total_charges = BudgetKPITotalChargesSerializer()

    class Meta:
        dataclass = kpi.BudgetKPIValues
        fields = ("service_types", "total_charges")


class BudgetKPITotalsSerializer(DataclassSerializer[kpi.BudgetKPIValues]):
    budget_id = serializers.IntegerField(source="options.budget_id")
    total_charges = BudgetKPITotalChargesSerializer()

    class Meta:
        dataclass = kpi.BudgetKPIValues
        fields = ("budget_id", "total_charges")
