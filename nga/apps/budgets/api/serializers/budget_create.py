from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.budgets.api.schemas import BudgetCreateSchema
from nga.apps.budgets.commands import CreateBudgetCommand
from nga.apps.budgets.enums import BudgetTypeEnum
from nga.apps.common.serializer_fields import CurrentUserP<PERSON>Default, EnumChoiceField, MonthField, Year<PERSON>onthField
from nga.apps.common.validation import check_operators_for_existence
from nga.apps.references.exceptions import OperatorDoesNotExist
from nga.apps.references.providers import AbstractOperatorProvider

__all__ = [
    "BudgetCreateSchemaSerializer",
]


class BudgetCreateSchemaSerializer(DataclassSerializer[BudgetCreateSchema]):
    start_date = YearMonthField()
    end_date = YearMonthField()

    description = serializers.CharField(required=False, allow_null=True, default=None)

    type = EnumChoiceField(
        enum_class=BudgetTypeEnum,
        choices=BudgetTypeEnum.get_editable_types(),
    )

    last_historical_month = MonthField()

    user_id = serializers.HiddenField(default=CurrentUserPKDefault())

    run_calculation = serializers.BooleanField(default=False)

    @inject
    def __init__(
        self,
        operator_provider: AbstractOperatorProvider = Closing[Provide["operator_provider"]],
        **kwargs: Any,
    ):
        super().__init__(**kwargs)

        self._operator_provider = operator_provider

    class Meta:
        dataclass = CreateBudgetCommand
        fields = (
            "name",
            "description",
            "type",
            "home_operators",
            "start_date",
            "end_date",
            "last_historical_month",
            "user_id",
            "run_calculation",
        )

    def validate_home_operators(self, home_operators: list[int]) -> list[int]:
        try:
            check_operators_for_existence(self._operator_provider, home_operators)
        except OperatorDoesNotExist as e:
            raise ValidationError(e.message)

        return home_operators
