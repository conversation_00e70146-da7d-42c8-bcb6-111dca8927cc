from rest_framework import serializers
from rest_framework.serializers import ModelSerializer

from nga.apps.budgets.api.filters import BudgetFilterSet
from nga.apps.budgets.enums import BudgetTypeEnum
from nga.apps.budgets.infra.orm import models
from nga.apps.common.serializer_fields import <PERSON>umC<PERSON>iceField, MonthField, Year<PERSON>onth<PERSON>ield
from nga.apps.common.serializer_utils import get_choices_for_sort_field
from nga.apps.references.api.serializers import OperatorORMSerializer


class BudgetListSerializer(ModelSerializer[models.Budget]):
    start_date = YearMonthField()
    end_date = YearMonthField()

    last_historical_month = MonthField(required=True)

    home_operators = OperatorORMSerializer(many=True)

    type = EnumChoiceField(enum_class=BudgetTypeEnum)

    class Meta:
        model = models.Budget
        fields = (
            "id",
            "name",
            "description",
            "home_operators",
            "start_date",
            "end_date",
            "last_historical_month",
            "type",
            "is_master",
            "created_at",
            "updated_at",
        )


class BudgetListQuerySerializer(serializers.Serializer):
    sort_field = serializers.ChoiceField(
        choices=get_choices_for_sort_field(BudgetFilterSet),
        required=False,
        default=None,
        allow_null=True,
    )

    class Meta:
        fields = ("sort_field",)
