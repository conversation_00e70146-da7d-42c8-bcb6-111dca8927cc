from typing import Any

from asgiref.sync import async_to_sync
from channels.generic.websocket import JsonWebsocketConsumer
from dependency_injector.wiring import Closing, Provide

from nga.apps.budgets.commands import BudgetMessageTypeEnum
from nga.apps.budgets.domain.exceptions import BudgetDoesNotExist
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.internal.messaging.topics import get_budget_topic_name


class BudgetConsumer(JsonWebsocketConsumer):
    def __init__(
        self,
        *args: Any,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(*args, **kwargs)

        self._budget_repository = budget_repository

        self._budget_group_channel_name: str = ""

        # adds handler for needed message type that is sent to channel group

        message_type: BudgetMessageTypeEnum
        for message_type in BudgetMessageTypeEnum:
            self.__dict__[message_type.value] = self.send_event

    def connect(self) -> None:
        if self.scope["user"].is_anonymous is True:
            self.close()
            return None

        budget_id = int(self.scope["url_route"]["kwargs"]["budget_id"])

        try:
            budget = self._budget_repository.get_by_id(budget_id)
        except BudgetDoesNotExist:
            self.close()
            return None

        self._budget_group_channel_name = get_budget_topic_name(budget.id)

        async_to_sync(self.channel_layer.group_add)(self._budget_group_channel_name, self.channel_name)

        self.accept()

    def disconnect(self, close_code: int) -> None:
        if self._budget_group_channel_name != "":
            async_to_sync(self.channel_layer.group_discard)(self._budget_group_channel_name, self.channel_name)

    def django_channel_message(self, message: dict[str, Any]) -> None:
        msg = {**message}
        msg.pop("type")

        self.send_json(msg)

    def send_event(self, event: dict[str, Any]) -> None:
        self.send_json(event)
