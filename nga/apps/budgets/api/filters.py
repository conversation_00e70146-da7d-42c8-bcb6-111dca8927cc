import django_filters

from nga.apps.budgets.infra.orm.models import Budget


class BudgetFilterSet(django_filters.FilterSet):
    sort_field = django_filters.OrderingFilter(
        fields=(
            ("name", "name"),
            ("last_historical_month", "last_historical_month"),
            ("created_at", "created_at"),
            ("updated_at", "updated_at"),
        )
    )

    class Meta:
        model = Budget
        fields = ["sort_field"]
