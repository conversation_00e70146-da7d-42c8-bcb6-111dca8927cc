from typing import Any

from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.common.views import BaseManageView

from .budget_delete import BudgetDeleteAPIView
from .budget_parameters import BudgetParametersAPIView
from .budget_update import BudgetUpdateAPIView


class BudgetDetailAPIView(BaseManageView):
    """View that provides operations under Budget instance."""

    VIEWS_BY_METHOD = {
        "GET": BudgetParametersAPIView,
        "PATCH": BudgetUpdateAPIView,
        "DELETE": BudgetDeleteAPIView,
    }

    @swagger_auto_schema(
        responses={status.HTTP_200_OK: VIEWS_BY_METHOD["GET"].serializer_class()},
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Returns budget parameters."""

    @swagger_auto_schema(
        request_body=VIEWS_BY_METHOD["PATCH"].serializer_class(partial=True),
        responses={status.HTTP_200_OK: VIEWS_BY_METHOD["PATCH"].response_serializer_class()},
    )
    def patch(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Partially updates budget."""

    @swagger_auto_schema()
    def delete(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Removes budget."""
