import logging
import traceback
from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from rest_framework.exceptions import APIException
from rest_framework.generics import ListAPIView, RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.api import serializers
from nga.apps.budgets.api.views.utils import get_budget_or_404
from nga.apps.budgets.domain.dto import BudgetComponentsQuantity, BudgetParametersFilters
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.infra.orm import models
from nga.apps.budgets.providers import AbstractBudgetComponentsProvider
from nga.apps.common.serializers import BudgetQuerySerializer
from nga.apps.common.view_mixins import QueryParametersMixin
from nga.core.types import DatePeriod
from nga.utils.collections import to_id_list


class BudgetComponentsQuantityAPIView(
    QueryParametersMixin,
    RetrieveAPIView,
):
    queryset = models.Budget.objects.all()
    serializer_class = serializers.BudgetComponentsQuantitySerializer
    query_serializer_class = BudgetQuerySerializer
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_components_provider: AbstractBudgetComponentsProvider = Closing[Provide["budget_components_provider"]],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._budget_repository = budget_repository
        self._budget_components_provider = budget_components_provider

    @swagger_auto_schema(query_serializer=query_serializer_class())
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        budget = get_budget_or_404(self._budget_repository, kwargs["pk"])

        quantity = self.get_quantity(budget.id)

        serializer = self.get_serializer(quantity)

        return Response(serializer.data)

    def get_filters(self) -> BudgetParametersFilters:
        query = self.get_query_params()

        period = None

        start_date, end_date = query.get("start_date"), query.get("end_date")

        if start_date and end_date:
            period = DatePeriod(start_date, end_date)

        filters = BudgetParametersFilters(
            home_operators=query["home_operators"],
            partner_operators=query["partner_operators"],
            partner_countries=query["partner_countries"],
            period=period,
        )

        return filters

    def get_quantity(self, budget_id: int) -> BudgetComponentsQuantity:
        filters = self.get_filters()

        try:
            quantity = self._budget_components_provider.get_quantity(budget_id, filters)
        except Exception as e:
            traceback.print_exc()
            logging.error(str(e))
            raise APIException(str(e))

        return quantity


class BudgetComponentsQuantityListAPIView(ListAPIView):
    """Returns total amount of each budget component for all budgets."""

    queryset = models.Budget.objects.all()
    serializer_class = serializers.BudgetComponentsQuantitySerializer
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_components_provider: AbstractBudgetComponentsProvider = Closing[Provide["budget_components_provider"]],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._budget_repository = budget_repository
        self._budget_components_provider = budget_components_provider

    def list(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        budgets = self._budget_repository.get_many()
        budget_ids = to_id_list(budgets)

        quantities = [self._budget_components_provider.get_quantity(budget_id) for budget_id in budget_ids]
        serializer = self.get_serializer(quantities, many=True)

        return Response(serializer.data)
