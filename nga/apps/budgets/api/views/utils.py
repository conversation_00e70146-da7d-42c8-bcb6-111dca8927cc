import typing
from dataclasses import asdict
from typing import Optional, Sequence, TypeVar

from rest_framework.exceptions import NotFound

from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.budgets.domain import Budget
from nga.apps.budgets.domain.exceptions import BudgetDoesNotExist
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.core.types import DatePeriod


def get_budget_or_404(budget_repository: AbstractBudgetRepository, budget_id: int) -> Budget:
    """Returns budget instance if it exists otherwise Django's 404 exception is raised."""

    try:
        budget = budget_repository.get_by_id(budget_id)
    except BudgetDoesNotExist as e:
        raise NotFound(e.message)

    return budget


class _Options(typing.Protocol):
    period: Optional[DatePeriod] = None

    home_operators: Optional[Sequence[int]] = None

    partner_operators: Optional[Sequence[int]] = None


Options = TypeVar("Options", bound=_Options)


def filter_options_by_agreement(options: Options, agreement: BudgetAgreement) -> Options:
    """
    :param options: python pure dataclass
    :param agreement: BudgetAgreement
    """

    raw_agreement_options = asdict(options) | dict(  # type: ignore[call-overload]
        period=options.period,
        home_operators=agreement.home_operators,
        partner_operators=agreement.partner_operators,
    )
    agreement_options = options.__class__(**raw_agreement_options)

    if options.period:
        start_date = max(options.period.start_date, agreement.period.start_date)
        end_date = min(options.period.end_date, agreement.period.end_date)

        if start_date <= end_date:
            agreement_options.period = DatePeriod(start_date, end_date)

    if options.home_operators:
        agreement_options.home_operators = [
            operator for operator in options.home_operators if operator in agreement.home_operators
        ]

    if options.partner_operators:
        agreement_options.partner_operators = [
            operator for operator in options.partner_operators if operator in agreement.partner_operators
        ]

    return agreement_options
