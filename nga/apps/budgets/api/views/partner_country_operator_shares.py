import logging
import traceback
from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from django.db.models import QuerySet
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework.exceptions import APIException
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.api.serializers import (
    BudgetPartnerCountryOperatorSharesQuerySerializer,
    BudgetPartnerCountryOperatorSharesSerializer,
)
from nga.apps.budgets.api.swagger_tags import BUDGET_ANALYTICS
from nga.apps.budgets.api.views.utils import get_budget_or_404
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.requests import GetBudgetPartnerCountryOperatorSharesRequest
from nga.apps.common.view_mixins import QueryParametersMixin
from nga.core.types import DatePeriod


class BudgetPartnerCountryOperatorSharesAPIView(QueryParametersMixin, RetrieveAPIView):
    queryset: QuerySet = []

    permission_classes = [IsAuthenticated]

    serializer_class = BudgetPartnerCountryOperatorSharesSerializer
    query_serializer_class = BudgetPartnerCountryOperatorSharesQuerySerializer

    @inject
    def __init__(
        self,
        *args: Any,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(*args, **kwargs)

        self._mediator = mediator
        self._budget_repository = budget_repository

    @swagger_auto_schema(query_serializer=query_serializer_class(), tags=[BUDGET_ANALYTICS])
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        budget = get_budget_or_404(self._budget_repository, budget_id=self.kwargs["pk"])

        query_params = self.get_query_params()

        get_shares_request = GetBudgetPartnerCountryOperatorSharesRequest(
            budget_id=budget.id,
            home_operators=query_params["home_operators"],
            partner_countries=query_params["partner_countries"],
            service_type=query_params["service_type"],
            traffic_direction=query_params["traffic_direction"],
            period=DatePeriod(query_params["start_date"], query_params["end_date"]),
        )

        try:
            shares = self._mediator.send(get_shares_request)
        except Exception as e:
            traceback.print_exc()
            logging.error(str(e))
            raise APIException(detail=str(e))

        response_data = self.get_serializer(shares).data

        return Response(response_data)
