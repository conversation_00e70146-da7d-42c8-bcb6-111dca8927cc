from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.api.serializers import BudgetComponentsStateSerializer
from nga.apps.budgets.api.views.utils import get_budget_or_404
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.infra.orm import models
from nga.apps.budgets.providers import AbstractBudgetComponentsProvider


class BudgetComponentsStateAPIView(RetrieveAPIView):
    queryset = models.Budget.objects.none()
    serializer_class = BudgetComponentsStateSerializer
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_components_provider: AbstractBudgetComponentsProvider = Closing[Provide["budget_components_provider"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._budget_components_provider = budget_components_provider
        self._budget_repository = budget_repository

    def retrieve(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        budget = get_budget_or_404(self._budget_repository, budget_id=kwargs["pk"])

        budget_components_state = self._budget_components_provider.get_state(budget)

        serializer = self.get_serializer(budget_components_state)

        return Response(serializer.data)
