from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.infra.tasks import delete_budget_task
from nga.apps.common.view_utils import get_budget_or_404


class BudgetDeleteAPIView(GenericAPIView):
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._budget_repository = budget_repository

    def delete(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        budget = get_budget_or_404(self._budget_repository, self.kwargs["pk"], raise_403_if_master=True)

        delete_budget_task.delay(budget_id=budget.id)

        return Response(status=status.HTTP_204_NO_CONTENT)
