import logging
import traceback
from typing import Any, Optional

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework.exceptions import APIException
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.budgets.analytics.traffic_evolution import TrafficEvolution
from nga.apps.budgets.api.serializers import TrafficEvolutionAPIResponseSerializer, TrafficEvolutionQuerySerializer
from nga.apps.budgets.api.swagger_tags import BUDGET_ANALYTICS
from nga.apps.budgets.api.views.utils import filter_options_by_agreement
from nga.apps.budgets.enums import ChargeTypeEnum, UnitDirectionTypeEnum, UnitTypeEnum
from nga.apps.budgets.providers.budget_agreement import AbstractBudgetAgreementProvider
from nga.apps.budgets.requests import GetTrafficEvolutionRequest
from nga.apps.common.view_mixins import QueryParametersMixin
from nga.core.enums import ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import DatePeriod


class TrafficEvolutionListAPIView(QueryParametersMixin, ListAPIView):
    permission_classes = [IsAuthenticated]

    serializer_class = TrafficEvolutionAPIResponseSerializer
    query_serializer_class = TrafficEvolutionQuerySerializer

    @inject
    def __init__(
        self,
        *args: Any,
        mediator: Mediator = Provide["mediator"],
        budget_agreement_provider: AbstractBudgetAgreementProvider = Closing[Provide["budget_agreement_provider"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(*args, **kwargs)

        self._mediator = mediator

        self._budget_agreement_provider = budget_agreement_provider

    @swagger_auto_schema(query_serializer=query_serializer_class(), tags=[BUDGET_ANALYTICS])
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        query_params = self.get_query_params()

        evolution_requests = self._create_requests(query_params)

        traffic_evolution_values = []

        for evolution_request in evolution_requests:
            values = self._get_traffic_evolution(evolution_request)

            traffic_evolution_values.append(values)

        data = self.serialize(traffic_evolution_values)

        return Response(data)

    def _create_requests(self, query_params: dict[str, Any]) -> list[GetTrafficEvolutionRequest]:
        budget_ids: list[int] = query_params["budgets_ids"]

        agreement_ids = query_params.get("agreement_ids")

        requests = []

        if agreement_ids is not None:
            agreements = self._budget_agreement_provider.get_many(agreement_ids=agreement_ids)

            for agreement in agreements:
                r = self._create_request(agreement.budget_id, query_params, agreement=agreement)

                requests.append(r)

        else:
            for budget_id in budget_ids:
                r = self._create_request(budget_id, query_params, agreement=None)

                requests.append(r)

        return requests

    @classmethod
    def _create_request(
        cls,
        budget_id: int,
        query_params: dict[str, Any],
        agreement: Optional[BudgetAgreement],
    ) -> GetTrafficEvolutionRequest:
        request = GetTrafficEvolutionRequest(
            budget_id=budget_id,
            agreement_id=None,
            period=DatePeriod(query_params["start_date"], query_params["end_date"]),
            service_type=ServiceTypeEnum(query_params["service_type"]),
            charge_type=ChargeTypeEnum(query_params["charge_type"]),
            volume_type=VolumeTypeEnum(query_params["volume_type"]),
            currency_code=query_params.get("currency_code"),
            unit_type=UnitTypeEnum(query_params["unit_type"]),
            unit_direction_type=UnitDirectionTypeEnum(query_params["unit_direction_type"]),
            include_forecasted=query_params["include_forecasted"],
            home_operators=query_params["home_operators"],
            partner_operators=query_params["partner_operators"],
            partner_countries=query_params["partner_countries"],
        )

        if agreement:
            request.agreement_id = agreement.id
            request = filter_options_by_agreement(request, agreement)

        return request

    def _get_traffic_evolution(self, request: GetTrafficEvolutionRequest) -> TrafficEvolution:
        try:
            traffic_evolution = self._mediator.send(request)
        except Exception as e:
            traceback.print_exc()
            logging.error(str(e))
            raise APIException(detail=str(e))

        return traffic_evolution

    def serialize(self, traffic_evolution_values: list[TrafficEvolution]) -> dict[str, Any]:
        data = {"data": traffic_evolution_values}

        serialized_data: dict[str, Any] = self.serializer_class(data).data

        return serialized_data
