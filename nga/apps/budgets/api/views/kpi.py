import logging
import traceback
from typing import Any, Type

from dependency_injector.wiring import Closing, Provide, inject
from django.conf import settings
from django.http import HttpResponse
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.exceptions import APIException
from rest_framework.generics import ListAPIView, RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from nga.apps.budgets.analytics.kpi import (
    AbstractBudgetKPIValuesProvider,
    AbstractBudgetKPIValuesRenderer,
    BudgetKPIOptions,
    BudgetKPIValues,
    BudgetKPIValuesXlsxRenderer,
)
from nga.apps.budgets.api import serializers
from nga.apps.budgets.api.swagger_tags import BUDGET_ANALYTICS
from nga.apps.budgets.api.views.utils import get_budget_or_404
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.enums import ChargeTypeEnum
from nga.apps.budgets.infra.orm import models
from nga.apps.common.view_mixins import QueryParametersMixin
from nga.apps.references.providers import AbstractOperatorProvider
from nga.core.enums import VolumeTypeEnum
from nga.core.types import DatePeriod
from nga.utils.collections import to_id_list


class BaseBudgetKPIValuesView(QueryParametersMixin, APIView):
    serializer_class = serializers.BudgetKPIValuesSerializer
    query_serializer_class = serializers.BudgetKPIValuesQuerySerializer

    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_kpi_values_provider: AbstractBudgetKPIValuesProvider = Closing[Provide["budget_kpi_values_provider"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._budget_repository = budget_repository
        self._budget_kpi_values_provider = budget_kpi_values_provider

    def get_kpi_values(self, budget_id: int) -> BudgetKPIValues:
        options = self.create_kpi_options(budget_id)
        try:
            values = self._budget_kpi_values_provider.get_values(options)
        except Exception as e:
            traceback.print_exc()
            logging.error(str(e))
            raise APIException(detail=str(e))
        return values

    def create_kpi_options(self, budget_id: int) -> BudgetKPIOptions:
        """Creates BudgetKPIOptions instance based on user request parameters."""

        query_data = self.get_query_params()

        start_date, end_date = query_data.get("start_date"), query_data.get("end_date")

        if start_date and end_date:
            date_period = DatePeriod(start_date=query_data["start_date"], end_date=query_data["end_date"])
        else:
            date_period = None

        options = BudgetKPIOptions(
            home_operators=query_data.get("home_operators"),
            partner_operators=query_data["partner_operators"],
            partner_countries=query_data["partner_countries"],
            budget_id=budget_id,
            charge_type=ChargeTypeEnum(query_data["charge_type"]),
            volume_type=VolumeTypeEnum(query_data["volume_type"]),
            date_period=date_period,
            currency_code=query_data.get("currency_code"),
        )

        return options


class BudgetKPIValuesAPIView(BaseBudgetKPIValuesView, RetrieveAPIView):
    queryset = models.Budget.objects.all()
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(query_serializer=BaseBudgetKPIValuesView.query_serializer_class(), tags=[BUDGET_ANALYTICS])
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Returns Budget KPI Values."""

        budget = get_budget_or_404(self._budget_repository, kwargs["pk"])

        kpi_values = self.get_kpi_values(budget.id)

        serializer = self.get_serializer(kpi_values)

        return Response(serializer.data)


class BudgetKPIValuesXlsxView(BudgetKPIValuesAPIView):
    kpi_xlsx_renderer: Type[AbstractBudgetKPIValuesRenderer] = BudgetKPIValuesXlsxRenderer

    @inject
    def __init__(
        self,
        operator_provider: AbstractOperatorProvider = Closing[Provide["operator_provider"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._operator_provider = operator_provider

    @swagger_auto_schema(
        query_serializer=BudgetKPIValuesAPIView.query_serializer_class(),
        responses={
            "200": openapi.Response(
                "File Attachment",
                schema=openapi.Schema(type=openapi.TYPE_FILE, format=settings.XLSX_CONTENT_TYPE),
            )
        },
        tags=[BUDGET_ANALYTICS],
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> HttpResponse:
        """Returns Budget KPI Values xlsx file."""

        budget = get_budget_or_404(self._budget_repository, kwargs["pk"])

        kpi_values = self.get_kpi_values(budget.id)

        renderer = self.kpi_xlsx_renderer(
            budget=budget,
            kpi_values=kpi_values,
            operator_provider=self._operator_provider,
        )

        memo_wb = renderer.render()
        memo_wb.seek(0)

        response = HttpResponse(memo_wb.read(), content_type=settings.XLSX_CONTENT_TYPE)
        filename = renderer.generate_filename()
        response["Content-Disposition"] = f"attachment; filename={filename}"

        return response


class BudgetKPITotalsAPIView(BaseBudgetKPIValuesView, ListAPIView):
    queryset = models.Budget.objects.all()
    serializer_class = serializers.BudgetKPITotalsSerializer
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(query_serializer=BaseBudgetKPIValuesView.query_serializer_class(), tags=[BUDGET_ANALYTICS])
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Returns budget KPI total charges for all budgets."""

        budgets = self._budget_repository.get_many()

        budget_ids = to_id_list(budgets)

        kpi_values = [self.get_kpi_values(budget_id) for budget_id in budget_ids]

        serializer = self.get_serializer(kpi_values, many=True)

        return Response(serializer.data)
