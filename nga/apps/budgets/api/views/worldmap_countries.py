import logging
import traceback
from abc import abstractmethod
from typing import Any, Generic, Optional, Sequence, TypeVar

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from rest_framework.exceptions import APIException
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.budgets.analytics.countries import (
    AbstractBudgetCountriesValuesProvider,
    AbstractCountryOperatorsValuesProvider,
    BudgetCountriesValues,
    BudgetCountriesValuesOptions,
    CountryOperatorsValues,
    CountryOperatorsValuesOptions,
)
from nga.apps.budgets.api.serializers import (
    BudgetCountriesValuesQuerySerializer,
    BudgetCountriesValuesResponseSerializer,
    BudgetCountryOperatorsValuesQuerySerializer,
    CountryOperatorsValuesResponseSerializer,
)
from nga.apps.budgets.api.swagger_tags import BUDGET_ANALYTICS
from nga.apps.budgets.api.views.utils import filter_options_by_agreement
from nga.apps.budgets.enums import ChargeTypeEnum, UnitDirectionTypeEnum, UnitTypeEnum
from nga.apps.budgets.providers.budget_components import AbstractBudgetAgreementProvider
from nga.apps.common.view_mixins import QueryParametersMixin
from nga.core.enums import ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import DatePeriod

T_Values = TypeVar("T_Values", BudgetCountriesValues, CountryOperatorsValues)
T_Options = TypeVar("T_Options", BudgetCountriesValuesOptions, CountryOperatorsValuesOptions)


class BaseCountriesValuesListAPIView(Generic[T_Values, T_Options], QueryParametersMixin, RetrieveAPIView):
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        budget_agreement_provider: AbstractBudgetAgreementProvider = Closing[Provide["budget_agreement_provider"]],
        **kwargs: Any
    ):
        super().__init__(**kwargs)

        self._budget_agreement_provider = budget_agreement_provider

    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Returns Budget Countries Values."""

        query_params = self.get_query_params()
        options_list = self.create_all_options(query_params)
        countries_values = []

        for options in options_list:
            values = self.get_values(options)
            countries_values.append(values)

        data = self.serialize(countries_values)

        return Response(data)

    def create_all_options(self, query_params: dict[str, Any]) -> list[T_Options]:
        """Create options instances for each combination for budget or agreement."""

        budget_ids = query_params.get("budgets_ids")
        agreement_ids = query_params.get("agreement_ids")

        options_list = []

        if budget_ids is not None:
            for budget_id in budget_ids:
                options = self.create_options(budget_id, query_params, agreement=None)
                options_list.append(options)

        elif agreement_ids is not None:
            agreements = self._budget_agreement_provider.get_many(agreement_ids=agreement_ids)

            for agreement in agreements:
                options = self.create_options(agreement.budget_id, query_params, agreement=agreement)
                options_list.append(options)

        return options_list

    @abstractmethod
    def create_options(
        self, budget_id: int, query_params: dict[str, Any], *, agreement: Optional[BudgetAgreement]
    ) -> T_Options:
        """Create options instance based on query params."""

    @abstractmethod
    def serialize(self, values: Sequence[T_Values]) -> dict[str, Any]:
        """Sorts, maps and serializes response data."""

    @abstractmethod
    def get_values(self, options: T_Options) -> T_Values:
        """Returns analytics values."""


class WorldmapCountryValuesAPIView(BaseCountriesValuesListAPIView[BudgetCountriesValues, BudgetCountriesValuesOptions]):
    serializer_class = BudgetCountriesValuesResponseSerializer
    query_serializer_class = BudgetCountriesValuesQuerySerializer

    @inject
    def __init__(
        self,
        budget_agreement_provider: AbstractBudgetAgreementProvider = Closing[Provide["budget_agreement_provider"]],
        countries_values_provider: AbstractBudgetCountriesValuesProvider = Closing[
            Provide["budget_countries_values_provider"]
        ],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs, budget_agreement_provider=budget_agreement_provider)

        self._countries_values_provider = countries_values_provider

    @swagger_auto_schema(query_serializer=query_serializer_class(), tags=[BUDGET_ANALYTICS])
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        return super().get(request, *args, **kwargs)

    def create_options(
        self, budget_id: int, query_params: dict[str, Any], *, agreement: Optional[BudgetAgreement]
    ) -> BudgetCountriesValuesOptions:
        """Creates BudgetCountriesValuesOptions from provided query parameters."""

        options = BudgetCountriesValuesOptions(
            budget_id=budget_id,
            agreement_id=None,
            period=DatePeriod(query_params["start_date"], query_params["end_date"]),
            service_type=ServiceTypeEnum(query_params["service_type"]),
            charge_type=ChargeTypeEnum(query_params["charge_type"]),
            volume_type=VolumeTypeEnum(query_params["volume_type"]),
            currency_code=query_params.get("currency_code"),
            unit_type=UnitTypeEnum(query_params["unit_type"]),
            unit_direction_type=UnitDirectionTypeEnum(query_params["unit_direction_type"]),
            include_forecasted=query_params["include_forecasted"],
            home_operators=query_params["home_operators"],
            partner_operators=query_params["partner_operators"],
            partner_countries=query_params["partner_countries"],
        )

        if agreement:
            options.agreement_id = agreement.id
            options = filter_options_by_agreement(options, agreement)

        return options

    def get_values(self, options: BudgetCountriesValuesOptions) -> BudgetCountriesValues:
        try:
            countries_values = self._countries_values_provider.get_values(options)
        except Exception as e:
            traceback.print_exc()
            logging.error(str(e))
            raise APIException(detail=str(e))

        return countries_values

    def serialize(self, countries_values: Sequence[BudgetCountriesValues]) -> dict[str, Any]:
        """Sorts, maps and serializes response data."""

        for values in countries_values:
            values.records = tuple(sorted(values.records, key=lambda r: r.value, reverse=True))

        data = {"data": countries_values}
        serialized_data: dict[str, Any] = self.serializer_class(data).data

        return serialized_data


class BudgetCountryOperatorsValuesAPIView(
    BaseCountriesValuesListAPIView[CountryOperatorsValues, CountryOperatorsValuesOptions]
):
    serializer_class = CountryOperatorsValuesResponseSerializer
    query_serializer_class = BudgetCountryOperatorsValuesQuerySerializer

    @inject
    def __init__(
        self,
        budget_agreement_provider: AbstractBudgetAgreementProvider = Closing[Provide["budget_agreement_provider"]],
        operators_values_provider: AbstractCountryOperatorsValuesProvider = Closing[
            Provide["budget_country_operators_values_provider"]
        ],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs, budget_agreement_provider=budget_agreement_provider)

        self._operators_values_provider = operators_values_provider

    @swagger_auto_schema(query_serializer=query_serializer_class(), tags=[BUDGET_ANALYTICS])
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        return super().get(request, *args, **kwargs)

    @classmethod
    def create_options(
        cls, budget_id: int, query_params: dict[str, Any], *, agreement: Optional[BudgetAgreement]
    ) -> CountryOperatorsValuesOptions:
        """Creates CountryOperatorsValuesOptions from provided query parameters."""

        options = CountryOperatorsValuesOptions(
            budget_id=budget_id,
            agreement_id=None,
            country_id=query_params["country_id"],
            period=DatePeriod(query_params["start_date"], query_params["end_date"]),
            service_type=ServiceTypeEnum(query_params["service_type"]),
            charge_type=ChargeTypeEnum(query_params["charge_type"]),
            volume_type=VolumeTypeEnum(query_params["volume_type"]),
            currency_code=query_params.get("currency_code"),
            unit_type=UnitTypeEnum(query_params["unit_type"]),
            unit_direction_type=UnitDirectionTypeEnum(query_params["unit_direction_type"]),
            include_forecasted=query_params["include_forecasted"],
            home_operators=query_params["home_operators"],
            partner_operators=query_params["partner_operators"],
            partner_countries=query_params["partner_countries"],
        )

        if agreement:
            options.agreement_id = agreement.id
            options = filter_options_by_agreement(options, agreement)

        return options

    def get_values(
        self,
        options: CountryOperatorsValuesOptions,
    ) -> CountryOperatorsValues:
        try:
            countries_values = self._operators_values_provider.get_values(options)
        except Exception as e:
            traceback.print_exc()
            logging.error(str(e))
            raise APIException(detail=str(e))

        return countries_values

    def serialize(
        self,
        operators_values: Sequence[CountryOperatorsValues],
    ) -> dict[str, Any]:
        """Sorts, maps and serializes response data."""

        for values in operators_values:
            values.records = tuple(sorted(values.records, key=lambda r: (r.value, r.previous_year_value), reverse=True))

        data = {"data": operators_values}
        serialized_data: dict[str, Any] = self.serializer_class(data).data

        return serialized_data
