from typing import Any, cast

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.api.mappers import AbstractBudgetParametersSchemaMapper
from nga.apps.budgets.api.serializers import BudgetCreateSchemaSerializer, BudgetParametersSchemaSerializer
from nga.apps.budgets.commands import CreateBudgetCommand
from nga.apps.budgets.domain.exceptions import BudgetValidationError
from nga.internal.uow import AbstractUnitOfWork


class BudgetCreateAPIView(GenericAPIView):
    queryset = None
    response_serializer_class = BudgetParametersSchemaSerializer
    serializer_class = BudgetCreateSchemaSerializer
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Closing[Provide["mediator"]],
        budget_parameters_mapper: AbstractBudgetParametersSchemaMapper = Closing[
            Provide["budget_parameters_schema_mapper"]
        ],
        **kwargs: Any,
    ) -> None:
        """Init view and dependencies."""

        super().__init__(**kwargs)

        self._uow = uow
        self._mediator = mediator
        self._budget_parameters_mapper = budget_parameters_mapper

    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        create_budget_cmd = self.get_create_budget_cmd(request)

        try:
            with self._uow:
                budget = self._mediator.send(create_budget_cmd)
        except BudgetValidationError as e:
            raise ValidationError(e.message)

        response_schema = self._budget_parameters_mapper.map(budget)

        response_data = self.response_serializer_class(response_schema).data

        return Response(response_data, status=status.HTTP_201_CREATED)

    def get_create_budget_cmd(self, request: Request) -> CreateBudgetCommand:
        """Returns BudgetCreateSchema instance created from request body parameters."""

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        schema = serializer.save()

        return cast(CreateBudgetCommand, schema)
