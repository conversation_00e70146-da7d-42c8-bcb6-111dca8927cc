from django_filters.rest_framework import Django<PERSON>ilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated

from nga.apps.budgets.api.filters import BudgetFilterSet
from nga.apps.budgets.api.serializers import BudgetListSerializer
from nga.apps.budgets.infra.orm import models


class BudgetListAPIView(ListAPIView):
    serializer_class = BudgetListSerializer
    permission_classes = [IsAuthenticated]

    queryset = (
        models.Budget.objects.prefetch_related("home_operators")
        .exclude(_is_deleting=True)
        .order_by("-is_master", "-created_at")
    )

    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = BudgetFilterSet

    search_fields = ("name",)
