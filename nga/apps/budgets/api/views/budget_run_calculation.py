from typing import Any, cast

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.api.mappers import BudgetCalculationMapper
from nga.apps.budgets.api.serializers import BudgetCalculationRetrieveSerializer, BudgetRunCalculationSerializer
from nga.apps.budgets.api.serializers.budget_calculation import BudgetCalculationParameters
from nga.apps.budgets.api.views.utils import get_budget_or_404
from nga.apps.budgets.commands import RunBudgetCalculationCommand
from nga.apps.budgets.domain import BudgetCalculation
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.infra.orm import models
from nga.apps.common.consts import SWAGGER_BUDGET_CALCULATIONS_TAG
from nga.apps.users.provider import AbstractUserProvider
from nga.core.exceptions import BaseNGAException


class BudgetRunCalculationAPIView(GenericAPIView):
    queryset = models.BudgetCalculation.objects.none()
    permission_classes = [IsAuthenticated]

    serializer_class = BudgetCalculationRetrieveSerializer
    serializer_body_class = BudgetRunCalculationSerializer

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        user_provider: AbstractUserProvider = Closing[Provide["user_provider"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._budget_repository = budget_repository
        self._user_provider = user_provider

    @swagger_auto_schema(
        request_body=serializer_body_class(),
        responses={status.HTTP_201_CREATED: serializer_class()},
        tags=[SWAGGER_BUDGET_CALCULATIONS_TAG],
    )
    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Runs budget calculation."""

        budget = get_budget_or_404(self._budget_repository, kwargs["pk"])

        if budget.is_master is True:
            raise APIException("Master Budget is not allowed to be calculated.")

        calc_parameters = self.get_calc_parameters()
        user_id: int = cast(int, self.request.user.id)

        run_calculation_cmd = RunBudgetCalculationCommand(
            budget_id=budget.id,
            calculation_type=calc_parameters.type,
            user_id=user_id,
            budget_agreement_id=calc_parameters.budget_agreement_id,
        )

        try:
            calculation = self._mediator.send(run_calculation_cmd)
        except BaseNGAException as e:
            raise APIException(e.message)

        return self.map_calculation_to_response(calculation)

    def get_calc_parameters(self) -> BudgetCalculationParameters:
        serializer = self.serializer_body_class(data=self.request.data)
        serializer.is_valid(raise_exception=True)

        parameters = serializer.save()

        return cast(BudgetCalculationParameters, parameters)

    def map_calculation_to_response(self, calculation: BudgetCalculation) -> Response:
        schema = BudgetCalculationMapper(self._user_provider).map_to_schema(calculation)

        serializer = self.get_serializer(schema)

        return Response(serializer.data, status=status.HTTP_201_CREATED)
