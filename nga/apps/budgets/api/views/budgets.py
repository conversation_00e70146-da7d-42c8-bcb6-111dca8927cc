from typing import Any

from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.filters import <PERSON><PERSON>ilter
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.api.serializers import (
    BudgetCreateSchemaSerializer,
    BudgetListQuerySerializer,
    BudgetParametersSchemaSerializer,
)
from nga.apps.common.views import BaseManageView

from .budget_create import BudgetCreateAPIView
from .budget_list import BudgetListAPIView


class BudgetsAPIView(BaseManageView):
    """API View that provides actions to Budget collection."""

    VIEWS_BY_METHOD = {
        "GET": BudgetListAPIView,
        "POST": BudgetCreateAPIView,
    }

    # HTTP method handler are defined in order to provider Swagger schema.

    @swagger_auto_schema(
        responses={status.HTTP_200_OK: VIEWS_BY_METHOD["GET"].serializer_class(many=True)},
        query_serializer=BudgetListQuerySerializer(),
        filters=[SearchFilter()],
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Returns list of Budgets."""

    @swagger_auto_schema(
        responses={status.HTTP_201_CREATED: BudgetParametersSchemaSerializer()},
        request_body=BudgetCreateSchemaSerializer,
    )
    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Creates Budget."""
