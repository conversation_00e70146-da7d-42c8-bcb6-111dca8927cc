import typing
from decimal import Decimal
from typing import Any, Optional

from dependency_injector.wiring import Closing, Provide, inject
from django.conf import settings
from django.db.models import F, QuerySet
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.analytics.reporting import AbstractBudgetReportRenderer, BudgetReportXlsxRenderer
from nga.apps.budgets.api import serializers
from nga.apps.budgets.api.swagger_tags import BUDGET_ANALYTICS
from nga.apps.budgets.api.views.utils import get_budget_or_404
from nga.apps.budgets.domain.dto import BudgetTrafficParameters
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.enums import ReportingAllowedColumnsEnum
from nga.apps.budgets.infra.orm import models
from nga.apps.budgets.infra.orm.querysets import BudgetTrafficRecordQuerySet
from nga.apps.budgets.infra.orm.utils import zero_division_validate
from nga.apps.common.pagination import DefaultNGAPageNumberPagination
from nga.apps.common.view_mixins import QueryParametersMixin
from nga.apps.references.providers import (
    AbstractCountryProvider,
    AbstractOperatorProvider,
    AbstractTrafficSegmentProvider,
)
from nga.core.types import DatePeriod


class BudgetReportAPIView(ListAPIView, QueryParametersMixin):
    serializer_class = serializers.BudgetReportSerializer
    query_serializer_class = serializers.BudgetReportQuerySerializer
    queryset = models.BudgetTrafficRecord.objects.order_by("budget_snapshot_id")
    permission_classes = [IsAuthenticated]
    pagination_class = DefaultNGAPageNumberPagination

    MAPPING_FIELDS = {
        "home_operator_pmn": "home_operator_id",
        "partner_operator_pmn": "partner_operator_id",
        "called_country": "called_country_id",
        "traffic_segment_name": "traffic_segment_id",
    }
    AGGREGATED_COLUMNS = (
        "volume_actual",
        "volume_billed",
        "charge_net",
        "charge_gross",
        "tap_charge_net",
        "tap_charge_gross",
        "discount_net",
        "discount_gross",
        "tap_rate_net_volume_actual",
        "tap_rate_gross_volume_actual",
        "discounted_rate_net_volume_actual",
        "discounted_rate_gross_volume_actual",
        "tap_rate_net_volume_billed",
        "tap_rate_gross_volume_billed",
        "discounted_rate_net_volume_billed",
        "discounted_rate_gross_volume_billed",
    )
    SORTING_FIELD_MAP = {
        ReportingAllowedColumnsEnum.HOME_OPERATOR_PMN: "home_operator__pmn_code",
        ReportingAllowedColumnsEnum.PARTNER_OPERATOR_PMN: "partner_operator__pmn_code",
        ReportingAllowedColumnsEnum.PARTNER_COUNTRY: "partner_operator__country__name",
        ReportingAllowedColumnsEnum.TRAFFIC_SEGMENT_NAME: "traffic_segment__name",
        ReportingAllowedColumnsEnum.CALLED_COUNTRY: "called_country__name",
    }

    @swagger_auto_schema(
        responses={status.HTTP_200_OK: serializer_class(many=True)},
        query_serializer=query_serializer_class(),
        paginator=DefaultNGAPageNumberPagination(),
        tags=[BUDGET_ANALYTICS],
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Returns Budget Report records."""

        report_qs = self.get_report_values()

        query_data = self.get_query_params()

        columns = query_data["columns"]

        page = self.paginate_queryset(report_qs) or tuple()

        serializer = self.get_serializer(page, many=True, fields=columns)

        return self.get_paginated_response(serializer.data)

    def get_report_values(self) -> BudgetTrafficRecordQuerySet:
        query_data = self.get_query_params()

        mapped_columns = self.get_mapped_columns()

        group_by_fields = set([i for i in mapped_columns if i not in self.AGGREGATED_COLUMNS])

        currency_code: Optional[str] = query_data.get("currency_code")

        budget = get_object_or_404(models.Budget, pk=self.kwargs["pk"])

        qs = (
            self.filter_queryset(self.get_queryset())
            .apply_currency_exchange_rates(budget=budget, currency_code=currency_code)
            .values(*group_by_fields)
            .aggregate_charges(exchange_rates_applied=bool(currency_code is not None))
            .aggregate_volumes()
        )

        # Annotate new fields as calculations of existing ones.
        qs = qs.annotate(
            discount_net=F("charge_net") - F("tap_charge_net"),
            discount_gross=F("charge_gross") - F("tap_charge_gross"),
            tap_rate_net_volume_actual=zero_division_validate("tap_charge_net", "volume_actual"),
            tap_rate_gross_volume_actual=zero_division_validate("tap_charge_gross", "volume_actual"),
            discounted_rate_net_volume_actual=zero_division_validate("charge_net", "volume_actual"),
            discounted_rate_gross_volume_actual=zero_division_validate("charge_gross", "volume_actual"),
            tap_rate_net_volume_billed=zero_division_validate("tap_charge_net", "volume_billed"),
            tap_rate_gross_volume_billed=zero_division_validate("tap_charge_gross", "volume_billed"),
            discounted_rate_net_volume_billed=zero_division_validate("charge_net", "volume_billed"),
            discounted_rate_gross_volume_billed=zero_division_validate("charge_gross", "volume_billed"),
        )

        sort_field, sort_sign = query_data.get("sort_field"), query_data.get("sort_sign")

        if sort_field is not None and sort_sign is not None:
            mapped_sort_field = self.SORTING_FIELD_MAP.get(sort_field, sort_field)
            mapped_sort_field = (
                mapped_sort_field if sort_sign == self.query_serializer_class.ASC_SIGN else f"-{mapped_sort_field}"
            )

            qs = qs.order_by(mapped_sort_field)

        return qs

    def filter_queryset(self, qs: QuerySet) -> BudgetTrafficRecordQuerySet:
        budget = get_object_or_404(models.Budget, pk=self.kwargs["pk"])

        qs = qs.filter(budget_snapshot_id=budget.active_snapshot_id)

        query_data = self.get_query_params()

        start_date, end_date = query_data.get("start_date"), query_data.get("end_date")

        is_premium = query_data.get("is_premium")

        if isinstance(is_premium, bool):
            is_premium = [is_premium]

        parameters = BudgetTrafficParameters(
            snapshot_id=budget.active_snapshot_id,
            home_operators=query_data.get("home_operators"),
            partner_operators=query_data.get("partner_operators"),
            partner_countries=query_data.get("partner_countries"),
            period=DatePeriod(start_date, end_date) if start_date and end_date else None,
            traffic_types=query_data.get("traffic_types"),
            traffic_directions=query_data.get("traffic_directions"),
            service_types=query_data.get("service_types"),
            call_destinations=query_data.get("call_destinations"),
            called_countries=query_data.get("called_countries"),
            premium=is_premium,
            traffic_segments=query_data.get("traffic_segments"),
            imsi_count_types=query_data.get("imsi_count_types"),
        )

        qs = qs.filter_by_parameters(parameters)

        return typing.cast(BudgetTrafficRecordQuerySet, qs)

    def get_mapped_columns(self) -> set[str]:
        query_data = self.get_query_params()

        columns = list(query_data["columns"])

        mapped_columns = set([self.MAPPING_FIELDS.get(column, column) for column in columns])

        if ReportingAllowedColumnsEnum.PARTNER_COUNTRY in mapped_columns:
            # PARTNER_COUNTRY is getting value from PARTNER_OPERATOR in mapper
            mapped_columns.remove(ReportingAllowedColumnsEnum.PARTNER_COUNTRY)

        return mapped_columns


class BudgetReportXlsxView(BudgetReportAPIView):
    @inject
    def __init__(
        self,
        *args: Any,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        operator_provider: AbstractOperatorProvider = Closing[Provide["operator_provider"]],
        country_provider: AbstractCountryProvider = Closing[Provide["country_provider"]],
        traffic_segment_provider: AbstractTrafficSegmentProvider = Closing[Provide["traffic_segment_provider"]],
        **kwargs: Any,
    ):
        super().__init__(*args, **kwargs)

        self._budget_repository = budget_repository
        self._operator_provider = operator_provider
        self._country_provider = country_provider
        self._traffic_segment_provider = traffic_segment_provider

    @swagger_auto_schema(
        query_serializer=BudgetReportAPIView.query_serializer_class(),
        responses={
            "200": openapi.Response(
                "File Attachment",
                schema=openapi.Schema(type=openapi.TYPE_FILE, format=settings.XLSX_CONTENT_TYPE),
            )
        },
        tags=[BUDGET_ANALYTICS],
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> HttpResponse:
        """Returns Budget KPI Values xlsx file."""

        budget = get_budget_or_404(self._budget_repository, kwargs["pk"])

        query_data = self.get_query_params()

        columns = query_data["columns"]

        report_values = self.get_report_values()

        serializer = self.get_serializer(report_values, many=True, fields=columns)

        reporting_values = serializer.data

        self.format_numeric_fields(reporting_values)

        options = dict(**query_data)
        options["home_operators"] = query_data["home_operators"]
        options["partner_operators"] = query_data["partner_operators"]
        options["partner_countries"] = query_data["partner_countries"]

        renderer = self.get_file_renderer()

        memo_wb = renderer.render(budget, reporting_values, options)
        memo_wb.seek(0)

        response = HttpResponse(memo_wb.read(), content_type=settings.XLSX_CONTENT_TYPE)
        filename = renderer.generate_filename()
        response["Content-Disposition"] = f"attachment; filename={filename}"

        return response

    def get_file_renderer(self) -> AbstractBudgetReportRenderer:
        renderer = BudgetReportXlsxRenderer(
            country_provider=self._country_provider,
            operator_provider=self._operator_provider,
            traffic_segment_provider=self._traffic_segment_provider,
        )

        return renderer

    def format_numeric_fields(self, reporting_values: list[dict[str, Any]]) -> None:
        """Function for converting numeric fields from string to Decimal after using serializer."""

        for reporting_value in reporting_values:
            for key, value in reporting_value.items():
                if key in self.AGGREGATED_COLUMNS:
                    reporting_value[key] = Decimal(value) if value is not None else None
