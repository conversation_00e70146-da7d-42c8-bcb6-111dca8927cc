from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from rest_framework.exceptions import NotFound
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.api.mappers import BudgetCalculationMapper
from nga.apps.budgets.api.serializers import BudgetCalculationRetrieveSerializer, BudgetLastCalculationQuerySerializer
from nga.apps.budgets.domain import BudgetCalculation, BudgetCalculationDoesNotExist
from nga.apps.budgets.domain.exceptions import BudgetDoesNotExist
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.infra.orm import models
from nga.apps.common.consts import SWAGGER_BUDGET_CALCULATIONS_TAG
from nga.apps.common.view_mixins import QueryParametersMixin
from nga.apps.users.provider import AbstractUserProvider


class BudgetLastCalculationAPIView(QueryParametersMixin, RetrieveAPIView):
    queryset = models.BudgetCalculation.objects.none()  # required be defined by swagger schema generator
    permission_classes = [IsAuthenticated]
    query_serializer_class = BudgetLastCalculationQuerySerializer
    serializer_class = BudgetCalculationRetrieveSerializer

    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        user_provider: AbstractUserProvider = Closing[Provide["user_provider"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._budget_repository = budget_repository
        self._user_provider = user_provider

    @swagger_auto_schema(
        tags=[SWAGGER_BUDGET_CALCULATIONS_TAG],
        query_serializer=query_serializer_class(),
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        query_params = self.get_query_params()

        if budget_agreement_id := query_params.get("budget_agreement_id"):
            calculation = self.get_last_agreement_calculation(kwargs["pk"], budget_agreement_id)
        else:
            calculation = self.get_last_calculation(kwargs["pk"])

        schema = BudgetCalculationMapper(self._user_provider).map_to_schema(calculation)

        serializer = self.get_serializer(schema)

        return Response(serializer.data)

    def get_last_calculation(self, budget_id: int) -> BudgetCalculation:
        try:
            calculation = self._budget_repository.get_last_calculation(budget_id=budget_id)
        except (BudgetDoesNotExist, BudgetCalculationDoesNotExist) as e:
            raise NotFound(e.message)

        return calculation

    def get_last_agreement_calculation(self, budget_id: int, budget_agreement_id: int) -> BudgetCalculation:
        try:
            calculation = self._budget_repository.get_last_agreement_calculation(budget_id, budget_agreement_id)
        except (BudgetDoesNotExist, BudgetCalculationDoesNotExist) as e:
            raise NotFound(e.message)

        return calculation
