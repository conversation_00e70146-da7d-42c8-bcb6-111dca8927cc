from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.api.mappers import AbstractBudgetParametersSchemaMapper
from nga.apps.budgets.api.serializers import BudgetParametersSchemaSerializer
from nga.apps.budgets.api.views.utils import get_budget_or_404
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.infra.orm import models


class BudgetParametersAPIView(RetrieveAPIView):
    queryset = models.Budget.objects.all()
    serializer_class = BudgetParametersSchemaSerializer
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_parameters_mapper: AbstractBudgetParametersSchemaMapper = Closing[
            Provide["budget_parameters_schema_mapper"]
        ],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._budget_repository = budget_repository
        self._budget_parameters_mapper = budget_parameters_mapper

    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Budget Parameters API."""

        budget = get_budget_or_404(budget_repository=self._budget_repository, budget_id=self.kwargs["pk"])

        response_schema = self._budget_parameters_mapper.map(budget)

        budget_response = self.serializer_class(response_schema).data

        return Response(budget_response)
