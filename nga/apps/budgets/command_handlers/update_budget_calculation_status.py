from dependency_injector.wiring import Closing, Provide, inject
from mediatr.mediator import Mediator

from nga.apps.budgets.commands import (
    BudgetMessageTypeEnum,
    NotifyBudgetChannelCommand,
    UpdateBudgetCalculationStatusCommand,
)
from nga.apps.budgets.domain import BudgetCalculation
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.enums import BudgetCalculationStatusEnum


@Mediator.handler
class UpdateBudgetCalculationStatusCommandHandler:
    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
    ) -> None:
        """Dependencies initialization."""

        self._mediator = mediator

        self._budget_repository = budget_repository

    def handle(self, command: UpdateBudgetCalculationStatusCommand) -> BudgetCalculation:
        calculation = self._update_calculation(command)

        notify_budget_channel_command = NotifyBudgetChannelCommand(
            budget_id=calculation.budget_id,
            message_type=command.message_type,
            dt=command.dt,
        )
        self._mediator.send(notify_budget_channel_command)

        return calculation

    def _update_calculation(self, command: UpdateBudgetCalculationStatusCommand) -> BudgetCalculation:
        calculation = self._budget_repository.get_calculation_by_id(command.calculation_id)

        match command.message_type:
            case BudgetMessageTypeEnum.BUDGET_CALCULATION_STARTED:
                calculation.status = BudgetCalculationStatusEnum.STARTED

            case BudgetMessageTypeEnum.BUDGET_CALCULATION_IS_FINISHING:
                calculation.status = BudgetCalculationStatusEnum.IS_FINISHING

            case BudgetMessageTypeEnum.BUDGET_CALCULATION_FINISHED:
                calculation.status = BudgetCalculationStatusEnum.CALCULATED
                calculation.finished_at = command.dt

            case BudgetMessageTypeEnum.BUDGET_CALCULATION_FAILED:
                calculation.status = BudgetCalculationStatusEnum.FAILED
                calculation.finished_at = command.dt

            case BudgetMessageTypeEnum.HISTORICAL_TRAFFIC_SYNC_STARTED:
                calculation.status = BudgetCalculationStatusEnum.HISTORICAL_TRAFFIC_SYNCHRONIZATION

            case BudgetMessageTypeEnum.HISTORICAL_TRAFFIC_SYNC_FINISHED:
                calculation.traffic_synchronized_at = command.dt

            case BudgetMessageTypeEnum.FORECAST_RULES_APPLICATION_STARTED:
                calculation.status = BudgetCalculationStatusEnum.FORECAST_RULES_APPLICATION

            case BudgetMessageTypeEnum.FORECAST_RULES_APPLICATION_FINISHED:
                calculation.forecast_rules_applied_at = command.dt

            case BudgetMessageTypeEnum.AGREEMENTS_APPLICATION_STARTED:
                calculation.status = BudgetCalculationStatusEnum.AGREEMENTS_APPLICATION

            case BudgetMessageTypeEnum.AGREEMENTS_APPLICATION_FINISHED:
                calculation.agreements_applied_at = command.dt

            case BudgetMessageTypeEnum.MASTER_BUDGET_COMPONENTS_CALCULATION_STARTED:
                calculation.status = BudgetCalculationStatusEnum.EXTERNAL_CALCULATION_RESULTS_APPLICATION

        calculation = self._budget_repository.save_calculation(calculation)

        return calculation
