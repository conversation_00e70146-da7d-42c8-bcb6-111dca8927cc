from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.commands import DeleteBudgetCommitmentTrafficCommand
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository, AbstractBudgetTrafficRepository


@Mediator.handler
class DeleteBudgetCommitmentTrafficCommandHandler:
    """
    Removes budget traffic records that were created by discounts. Traffic records created by discounts are used in
    discount models to reach commitment.
    """

    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_traffic_repository: AbstractBudgetTrafficRepository = Closing[Provide["budget_traffic_repository"]],
    ) -> None:
        """Init deps."""

        self._budget_repository = budget_repository
        self._budget_traffic_repository = budget_traffic_repository

    def handle(self, cmd: DeleteBudgetCommitmentTrafficCommand) -> None:
        budget = self._budget_repository.get_by_id(cmd.budget_id)

        for snapshot_id in (budget.calculation_snapshot_id, budget.active_snapshot_id):
            self._budget_traffic_repository.delete_many(snapshot_id, discount_ids=cmd.discount_ids)
