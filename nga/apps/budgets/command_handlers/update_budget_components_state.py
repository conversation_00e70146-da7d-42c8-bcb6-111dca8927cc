from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.budgets.commands import (
    UpdateBudgetAfterAgreementsModifiedCommand,
    UpdateBudgetAfterForecastRulesModifiedCommand,
    UpdateBudgetAfterImportMonthlyTrafficRecordsCommand,
)
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository

__all__ = [
    "UpdateBudgetAfterForecastRulesModifiedCommandHandler",
    "UpdateBudgetAfterImportMonthlyTrafficRecordsCommandHandler",
    "UpdateBudgetAfterAgreementsModifiedCommandHandler",
]


@Mediator.handler
class UpdateBudgetAfterForecastRulesModifiedCommandHandler:
    # TODO: Rename to BudgetForecastRulesModified<EventHandler|NotificationHandler> after forecast rules API
    #  refactoring, when logic will move to domain layer.
    #  Flow:
    #   1. Send CreateForecastRuleCommand
    #   2. CreateForecastRule<PERSON>om<PERSON><PERSON><PERSON><PERSON> creates forecast rules and sends event|notification (
    #           BudgetForecastRulesModified<Event|Notification>
    #      )

    def __init__(self, budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]]) -> None:
        self._budget_repository = budget_repository

    def handle(self, command: UpdateBudgetAfterForecastRulesModifiedCommand) -> None:
        """Sets datetime when budget forecast rules has been updated"""

        budget = self._budget_repository.get_by_id(command.budget_id)

        budget.forecast_rules_modified_at = command.updated_at

        self._budget_repository.save(budget)


@Mediator.handler
class UpdateBudgetAfterImportMonthlyTrafficRecordsCommandHandler:
    def __init__(self, budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]]) -> None:
        self._budget_repository = budget_repository

    def handle(self, command: UpdateBudgetAfterImportMonthlyTrafficRecordsCommand) -> None:
        """Sets datetime when budget forecast rules has been updated"""

        budget = self._budget_repository.get_by_id(command.budget_id)

        budget.historical_traffic_modified_at = command.updated_at

        self._budget_repository.save(budget)


@Mediator.handler
class UpdateBudgetAfterAgreementsModifiedCommandHandler:
    def __init__(self, budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]]) -> None:
        self._budget_repository = budget_repository

    def handle(self, command: UpdateBudgetAfterAgreementsModifiedCommand) -> None:
        """Sets datetime when budget agreements has been updated"""

        budget = self._budget_repository.get_by_id(command.budget_id)

        budget.agreements_last_modified_at = command.updated_at

        self._budget_repository.save(budget)
