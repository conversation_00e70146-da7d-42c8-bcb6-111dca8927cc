from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.budgets.commands import RunBudgetCalculationCommand
from nga.apps.budgets.domain import BudgetCalculation
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.calculation.budgets import (
    AbstractBudgetCalculationRunner,
    BudgetCalculationFactory,
    BudgetCalculationOptions,
)


@Mediator.handler
class RunBudgetCalculationCommandHandler:
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_calculation_runner: AbstractBudgetCalculationRunner = Closing[Provide["budget_calculation_runner"]],
    ) -> None:
        self._budget_repository = budget_repository
        self._budget_calculation_runner = budget_calculation_runner

    def handle(self, cmd: RunBudgetCalculationCommand) -> BudgetCalculation:
        budget = self._budget_repository.get_by_id(cmd.budget_id)

        calculation_factory = BudgetCalculationFactory(self._budget_repository)

        calculation_options = BudgetCalculationOptions(cmd.calculation_type, cmd.budget_agreement_id, cmd.user_id)

        calculation = calculation_factory.create(budget, calculation_options)

        calculation = self._budget_calculation_runner.run(calculation)

        return calculation
