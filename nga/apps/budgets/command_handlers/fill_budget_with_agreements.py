from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.commands import BulkActivateBudgetAgreementCommand, BulkCopyBudgetAgreementCommand
from nga.apps.budgets.commands import FillBudgetWithAgreementsCommand


@Mediator.handler
class FillBudgetWithAgreementsCommandHandler:
    @inject
    def __init__(
        self,
        mediator: Mediator = Closing[Provide["mediator"]],
    ) -> None:

        self._mediator = mediator

    def handle(self, cmd: FillBudgetWithAgreementsCommand) -> None:

        copy_cmd = BulkCopyBudgetAgreementCommand(
            target_budget_id=cmd.budget_id,
            budget_agreement_ids=cmd.budget_agreement_ids,
            source_budget_id=None,
            only_active=None,
        )

        self._mediator.send(copy_cmd)

        activate_cmd = BulkActivateBudgetAgreementCommand(
            budget_id=cmd.budget_id,
            budget_agreement_ids=None,
        )

        self._mediator.send(activate_cmd)
