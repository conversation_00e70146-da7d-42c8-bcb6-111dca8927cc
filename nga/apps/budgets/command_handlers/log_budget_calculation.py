from mediatr import Mediator

from nga.apps.budgets.commands import LogBudgetCalculationCommand
from nga.apps.budgets.infra.orm import models


@Mediator.handler
class LogBudgetCalculationCommandHandler:
    def handle(self, cmd: LogBudgetCalculationCommand) -> None:
        """Creates log record for failed calculation."""

        models.BudgetCalculationLogRecord.objects.create(
            calculation_id=cmd.calculation_id,
            message=cmd.message,
            traceback=cmd.traceback,
        )
