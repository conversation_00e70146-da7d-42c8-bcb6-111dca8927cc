import json
from dataclasses import asdict

from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.core.serializers.json import DjangoJSONEncoder
from mediatr.mediator import Mediator

from nga.apps.budgets.api.consumers.messages import BudgetNotification
from nga.apps.budgets.commands import NotifyBudgetChannelCommand
from nga.internal.messaging.topics import get_budget_topic_name


@Mediator.handler
class NotifyBudgetChannelCommandHandler:
    def handle(self, command: NotifyBudgetChannelCommand) -> None:
        channel_layer = get_channel_layer()

        channel_name = get_budget_topic_name(command.budget_id)

        notification = BudgetNotification(budget_id=command.budget_id, type=command.message_type, dt=command.dt)

        serialized_dict = json.loads(json.dumps(asdict(notification), cls=DjangoJSONEncoder))

        async_to_sync(channel_layer.group_send)(channel_name, serialized_dict)
