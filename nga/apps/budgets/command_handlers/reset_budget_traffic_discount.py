from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.commands import ResetBudgetTrafficDiscountCommand
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository, AbstractBudgetTrafficRepository


@Mediator.handler
class ResetBudgetTrafficDiscountCommandHandler:
    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_traffic_repository: AbstractBudgetTrafficRepository = Closing[Provide["budget_traffic_repository"]],
    ) -> None:
        """Init deps."""

        self._budget_repository = budget_repository
        self._budget_traffic_repository = budget_traffic_repository

    def handle(self, cmd: ResetBudgetTrafficDiscountCommand) -> None:
        budget = self._budget_repository.get_by_id(cmd.budget_id)

        self._budget_traffic_repository.reset_discount(
            snapshot_id=budget.active_snapshot_id,
            home_operators=cmd.home_operators,
            partner_operators=cmd.partner_operators,
            period=cmd.period,
        )

        self._budget_traffic_repository.reset_discount(
            snapshot_id=budget.calculation_snapshot_id,
            home_operators=cmd.home_operators,
            partner_operators=cmd.partner_operators,
            period=cmd.period,
        )
