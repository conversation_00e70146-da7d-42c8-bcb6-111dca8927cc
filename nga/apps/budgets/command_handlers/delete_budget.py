import logging

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.budgets.commands import DeleteBudgetCommand
from nga.apps.budgets.domain.exceptions import MasterBudgetForbiddenActionError
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.internal.uow import AbstractUnitOfWork


@Mediator.handler
class DeleteBudgetCommandHandler:
    @inject
    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
    ) -> None:

        self._uow = uow

        self._budget_repository = budget_repository
        self._budget_agreement_repository = budget_agreement_repository

    def handle(self, cmd: DeleteBudgetCommand) -> None:
        if cmd.budget.is_master:
            raise MasterBudgetForbiddenActionError("Master budget removal is forbidden")

        with self._uow:
            self._budget_repository.set_for_delete(cmd.budget.id)

        try:
            self._delete_budget(cmd.budget.id)
        except Exception as e:
            logging.error(f"Error occurred during budget[id={cmd.budget.id}] delete process - {str(e)}")

    def _delete_budget(self, budget_id: int) -> None:
        with self._uow:
            budget_agreements = self._budget_agreement_repository.get_many(budget_id=budget_id)

            for budget_agreement in budget_agreements:
                self._budget_agreement_repository.delete_by_id(budget_agreement.id)

            self._budget_repository.delete_by_id(budget_id)
