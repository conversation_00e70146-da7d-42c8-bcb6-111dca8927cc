from rest_framework.exceptions import PermissionDenied

from nga.core.exceptions import BaseNGAException

__all__ = [
    "BudgetCalculationError",
    "BudgetCalculationDoesNotExist",
    "BudgetCalculationIsRunning",
    "BudgetValidationError",
    "BudgetLastHistoricalMonthDoesNotExist",
    "BudgetDoesNotExist",
    "MasterBudgetForbiddenActionError",
    "MasterBudgetPermissionDenied",
]


class BudgetCalculationDoesNotExist(BaseNGAException):
    id_msg_template = "Budget id={budget_id} has not been calculated yet."

    @classmethod
    def from_budget_id(cls, budget_id: int) -> "BudgetCalculationDoesNotExist":
        msg = cls.id_msg_template.format(budget_id=budget_id)
        return BudgetCalculationDoesNotExist(msg)


class BudgetCalculationIsRunning(BaseNGAException):
    """Raised during creating calculation record while another calculation is running."""

    default_message = "Budget Calculation is already running."


class BudgetCalculationError(BaseNGAException):
    """Raised during budget calculation."""


class BudgetValidationError(BaseNGAException):
    """Raised when Budget breaks validation rules."""


class BudgetLastHistoricalMonthDoesNotExist(BaseNGAException):
    default_message = "Last historical month does not exist."


class MasterBudgetForbiddenActionError(BaseNGAException):
    """Raises when error is occurred during doing prohibited actions on the Master Budget."""


class MasterBudgetPermissionDenied(PermissionDenied):
    """REST Exception when working with forbidden operations on the Master Budget."""

    default_detail = "Forbidden to perform operations on master budget!"


class BudgetDoesNotExist(BaseNGAException):
    id_msg_template = "Budget with id={budget_id} does not exist."

    @classmethod
    def from_budget_id(cls, budget_id: int) -> "BudgetDoesNotExist":
        msg = cls.id_msg_template.format(budget_id=budget_id)
        return BudgetDoesNotExist(msg)
