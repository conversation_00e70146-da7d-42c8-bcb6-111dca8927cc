from abc import ABC, abstractmethod
from typing import Iterable

from nga.apps.budgets.domain import Budget
from nga.apps.budgets.domain.exceptions import BudgetValidationError

__all__ = [
    "AbstractBudgetSpecification",
    "BudgetLHMSpecification",
    "BudgetSpecification",
]


class AbstractBudgetSpecification(ABC):
    @abstractmethod
    def verify(self, budget: Budget) -> None:
        """Put here model verification rule otherwise raise an error."""


class BudgetLHMSpecification(AbstractBudgetSpecification):
    def verify(self, budget: Budget) -> None:
        if budget.last_historical_month:
            if not (budget.period.start_date <= budget.last_historical_month <= budget.period.end_date):
                raise BudgetValidationError("LHM must be in budget period.")


class BudgetSpecification(AbstractBudgetSpecification):
    specifications: Iterable[AbstractBudgetSpecification]

    def __init__(self, specifications: Iterable[AbstractBudgetSpecification]) -> None:
        self.specifications = specifications

    def verify(self, budget: Budget) -> None:
        for spec in self.specifications:
            spec.verify(budget)
