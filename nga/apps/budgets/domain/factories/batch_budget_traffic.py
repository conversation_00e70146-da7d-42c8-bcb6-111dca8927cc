from typing import Collection

from nga.apps.budgets.domain.dto import BudgetTrafficRecordDTO
from nga.apps.budgets.domain.repositories import AbstractBudgetTrafficRepository


class BatchBudgetTrafficFactory:
    def __init__(self, repository: AbstractBudgetTrafficRepository, batch_size: int = 5000) -> None:
        self._repository = repository

        self._batch_size = batch_size

        self._records: list[BudgetTrafficRecordDTO] = []
        self._total_records = 0

    @property
    def records(self) -> list[BudgetTrafficRecordDTO]:
        return self._records

    @property
    def total_records(self) -> int:
        return self._total_records

    def add_records(self, records: Collection[BudgetTrafficRecordDTO]) -> None:
        self._records.extend(records)

        self._total_records += len(records)

        if self._total_records >= self._batch_size:
            self.commit()

    def commit(self) -> None:
        if not self._records:
            return None

        self._repository.create_many(self._records)

        self._total_records = 0
        self._records.clear()
