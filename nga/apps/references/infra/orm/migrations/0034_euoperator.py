# Generated by Django 5.1.7 on 2025-05-13 11:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('references', '0033_predefinedfilter'),
    ]

    operations = [
        migrations.CreateModel(
            name='EUOperator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operator', models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='eu_operator', to='references.operator')),
            ],
            options={
                'verbose_name': 'EU Operator',
                'verbose_name_plural': 'EU Operators',
                'db_table': 'eu_operators',
            },
        ),
    ]
