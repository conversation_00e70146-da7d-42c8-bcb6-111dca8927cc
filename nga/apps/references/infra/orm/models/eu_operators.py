from django.db import models


class EUOperator(models.Model):
    """Model only for EU operators."""

    operator = models.OneToOneField(
        "Operator",
        on_delete=models.PROTECT,
        related_name="eu_operator",
    )

    class Meta:
        db_table = "eu_operators"

        verbose_name = "EU Operator"
        verbose_name_plural = "EU Operators"

    def __str__(self) -> str:
        if self.pk:
            return f"{self.operator.pmn_code} - {self.operator.name} ({self.operator.country})"
        return self.operator.pmn_code

    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id}, code={self.operator.pmn_code}>"
