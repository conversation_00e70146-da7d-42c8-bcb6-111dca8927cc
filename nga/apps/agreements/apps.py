from django.apps import AppConfig


class AgreementsConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "nga.apps.agreements"

    def ready(self) -> None:
        # wire package for injecting dependencies

        from nga.infra.di import di_container

        di_container.wire(
            packages=[
                "nga.apps.agreements.api.views",
                "nga.apps.agreements.command_handlers",
                "nga.apps.agreements.request_handlers",
                "nga.apps.agreements.event_handlers",
            ],
            modules=[
                "nga.apps.agreements.api.serializers.agreement_filter_parameters",
            ],
        )

        from nga.apps.agreements.event_handlers import register_agreement_domain_event_handlers

        register_agreement_domain_event_handlers(di_container.event_dispatcher())
