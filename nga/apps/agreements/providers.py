from typing import Optional, Sequence

from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.budgets.domain.dto import BudgetParametersFilters
from nga.apps.budgets.providers.budget_agreement import AbstractBudgetAgreementProvider


class BudgetAgreementProvider(AbstractBudgetAgreementProvider):
    def __init__(self, repository: AbstractBudgetAgreementRepository) -> None:
        self._repository = repository

    def get_by_id(self, budget_agreement_id: int) -> BudgetAgreement:
        """Returns agreement by ID."""

        return self._repository.get_by_id(budget_agreement_id)

    def get_many(
        self,
        *,
        budget_id: Optional[int] = None,
        agreement_ids: Optional[list[int]] = None,
        only_active: Optional[bool] = None,
        only_modified: Optional[bool] = None,
    ) -> tuple[BudgetAgreement, ...]:
        """Returns agreements for specified budget."""

        agreements = self._repository.get_many(
            budget_id=budget_id,
            budget_agreement_ids=agreement_ids,
            only_active=only_active,
            only_modified=only_modified,
        )

        return agreements

    def update_many(self, budget_agreements: Sequence[BudgetAgreement]) -> None:
        self._repository.update_many(budget_agreements)

    def get_total_agreements(
        self,
        budget_id: int,
        *,
        budget_parameters: Optional[BudgetParametersFilters] = None,
        is_active: Optional[bool] = None,
    ) -> int:
        """Returns number of agreements by provided options."""

        return self._repository.count(budget_id, budget_parameters=budget_parameters, is_active=is_active)
