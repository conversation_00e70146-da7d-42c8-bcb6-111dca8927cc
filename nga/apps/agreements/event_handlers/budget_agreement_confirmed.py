from typing import cast

from dependency_injector.wiring import Provide, inject
from mediatr import Mediator

from nga.apps.agreements.commands import ConnectAgreementWithBudgetsCommand
from nga.apps.agreements.domain.events import BudgetAgreementConfirmedEvent
from nga.internal.domain import Event


class ConnectWithBudgetsOnBudgetAgreementConfirmedEvent:
    @inject
    def __init__(self, mediator: Mediator = Provide["mediator"]) -> None:
        self._mediator = mediator

    def handle(self, event: Event) -> None:
        budget_agreement_confirmed = cast(BudgetAgreementConfirmedEvent, event)

        self._mediator.send(ConnectAgreementWithBudgetsCommand(budget_agreement_confirmed.budget_agreement.id))
