from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from io import BytesIO
from typing import IO, Any

from django.utils import timezone
from openpyxl.workbook import Workbook
from openpyxl.worksheet.worksheet import Worksheet

from nga.apps.agreements.domain.models import BudgetAgreementQueryOptions
from nga.apps.agreements.domain.repositories import AbstractAgreementNegotiatorRepository
from nga.apps.agreements.enums import AgreementCalculationStatusEnum, AgreementStatusEnum
from nga.apps.common.renderers import BOLD_FONT, FONT_ATTR, XlsxStyleRenderer
from nga.apps.references.providers import AbstractCountryProvider, AbstractOperatorProvider
from nga.utils.string import date_to_str, datetime_to_str, stringify_enum_list


class AbstractBudgetAgreementRenderer(ABC):
    """Base class for writing Budget Agreements to IO."""

    @abstractmethod
    def render(
        self,
        budget_agreement_values: list[dict[str, Any]],
        budget_agreement_options: BudgetAgreementQueryOptions,
    ) -> IO[bytes]:
        """Render Budget Agreements content into stream."""

    @abstractmethod
    def generate_filename(self, budget_id: int) -> str:
        """Returns filename for file with Budget Agreements."""


class BudgetAgreementXlsxRenderer(AbstractBudgetAgreementRenderer):
    """
    BudgetAgreementRenderer interface implementation for writing Budget Agreement values to xlsx file.
    """

    AGREEMENTS_OPTIONS_HEADER = "Filters:"
    AGREEMENTS_OPTIONS_COLUMNS = (
        "Sort Field",
        "Budget",
        "Search",
        "Home Operators",
        "Partner Operators",
        "Partner Countries",
        "Start Date Min",
        "Start Date Max",
        "End Date Min",
        "End Date Max",
        "Is Active",
        "Include Satellite",
        "Include Premium",
        "Include Premium in Commitment",
        "Is Rolling",
        "Calculation Statuses",
        "Statuses",
        "Negotiators",
        "Updated At Min",
        "Updated At Max",
        "Applied At Min",
        "Applied At Max",
    )

    _AGREEMENTS_FIELDS_MAP = {
        "pk": "ID",
        "reference": "Agreement reference",
        "home_pmn_codes": "Home Operators",
        "partner_pmn_codes": "Partner Operators",
        "partner_country_names": "Partner Countries",
        "period": "Period",
        "status": "Status",
        "negotiator": "Negotiator",
        "is_active": "Active",
        "updated_at": "Added/Changed",
        "applied_at": "Applied",
        "calculation_status": "Calculation Status",
        "include_premium": "Premium",
        "include_premium_in_commitment": "Premium in Commitments",
        "include_satellite": "Satellites",
        "is_rolling": "Rolling",
    }

    FILENAME_DATE_FORMAT = "%Y-%m-%d"

    def __init__(
        self,
        country_provider: AbstractCountryProvider,
        operator_provider: AbstractOperatorProvider,
        agreement_negotiator_repository: AbstractAgreementNegotiatorRepository,
    ) -> None:
        self.xlsx_style_renderer = XlsxStyleRenderer()

        self._country_provider = country_provider
        self._operator_provider = operator_provider
        self._agreement_negotiator_repository = agreement_negotiator_repository

    def render(
        self,
        budget_agreement_values: list[dict[str, Any]],
        budget_agreement_options: BudgetAgreementQueryOptions,
    ) -> IO[bytes]:
        wb = Workbook()
        ws: Worksheet = wb.active

        self._write_agreement_options(wb, budget_agreement_options)
        self._write_agreement_values(wb, budget_agreement_values)

        self.xlsx_style_renderer.apply_default_styles_to_ws(ws)

        memory_wb = BytesIO()
        wb.save(memory_wb)
        wb.close()

        return memory_wb

    def _write_agreement_options(self, wb: Workbook, options: BudgetAgreementQueryOptions) -> None:
        ws: Worksheet = wb.active

        options_cell = f"A{self.xlsx_style_renderer.row_idx}"
        ws[options_cell] = self.AGREEMENTS_OPTIONS_HEADER
        ws[options_cell].font = BOLD_FONT
        self.xlsx_style_renderer.commit_row()

        home_operators_str = ""
        if options.home_operators:
            home_operators = self._operator_provider.get_many(operators_ids=options.home_operators)
            home_operators_str = ", ".join(hpmn.pmn_code for hpmn in home_operators)

        partner_operators_str = ""
        if options.partner_operators:
            partner_operators = self._operator_provider.get_many(operators_ids=options.partner_operators)
            partner_operators_str = ", ".join(ppmn.pmn_code for ppmn in partner_operators)

        partner_countries_str = ""
        if options.partner_countries:
            countries_map = {o.id: o for o in self._country_provider.get_many()}
            partner_countries_str = ", ".join(countries_map[c_id].name for c_id in options.partner_countries)

        negotiators_str = ""
        if options.negotiators:
            traffic_segments = self._agreement_negotiator_repository.get_many(negotiator_ids=options.negotiators)
            negotiators_str = ", ".join(n.name for n in traffic_segments)

        row_values = (
            options.sort_field or "",
            options.budget_name,
            options.search or "",
            home_operators_str,
            partner_operators_str,
            partner_countries_str,
            date_to_str(options.start_date_min) if options.start_date_min is not None else "",
            date_to_str(options.start_date_max) if options.start_date_max is not None else "",
            date_to_str(options.end_date_min) if options.end_date_min is not None else "",
            date_to_str(options.end_date_max) if options.end_date_max is not None else "",
            str(options.is_active) if isinstance(options.is_active, bool) else "All",
            str(options.include_satellite) if isinstance(options.include_satellite, bool) else "",
            str(options.include_premium) if isinstance(options.include_premium, bool) else "",
            (
                str(options.include_premium_in_commitment)
                if isinstance(options.include_premium_in_commitment, bool)
                else ""
            ),
            str(options.is_rolling) if isinstance(options.is_rolling, bool) else "All",
            stringify_enum_list(options.calculation_statuses or []),
            stringify_enum_list(options.statuses or []),
            negotiators_str,
            datetime_to_str(options.updated_at_min) if options.updated_at_min is not None else "",
            datetime_to_str(options.updated_at_max) if options.updated_at_max is not None else "",
            datetime_to_str(options.applied_at_min) if options.applied_at_min is not None else "",
            datetime_to_str(options.applied_at_max) if options.applied_at_max is not None else "",
        )
        for row in zip(self.AGREEMENTS_OPTIONS_COLUMNS, row_values):
            ws.append(row)
            self.xlsx_style_renderer.commit_row()

        ws.append([])
        self.xlsx_style_renderer.commit_row()

    def _write_agreement_values(
        self,
        wb: Workbook,
        budget_agreement_values: list[dict[str, Any]],
    ) -> None:

        ws: Worksheet = wb.active

        self._write_agreements_table_header(ws)

        for ba_value in budget_agreement_values:
            ba_value.update(
                {
                    "period": f"{date_to_str(ba_value['start_date'])} - {date_to_str(ba_value['end_date'])}",
                    "status": AgreementStatusEnum(ba_value["status"]),
                    "calculation_status": AgreementCalculationStatusEnum(ba_value["calculation_status"]),
                    "include_premium": self._map_include_field(ba_value["include_premium"]),
                    "include_premium_in_commitment": self._map_include_field(ba_value["include_premium_in_commitment"]),
                    "include_satellite": self._map_include_field(ba_value["include_satellite"]),
                }
            )

            row = [ba_value[column] for column in self._AGREEMENTS_FIELDS_MAP.keys()]

            row = list(map(self._map_by_type, row))

            ws.append(row)
            self.xlsx_style_renderer.commit_row()

    def _write_agreements_table_header(
        self,
        ws: Worksheet,
    ) -> None:

        ws.append(list(self._AGREEMENTS_FIELDS_MAP.values()))

        self.xlsx_style_renderer.apply_style_to_row(ws, FONT_ATTR, BOLD_FONT)
        self.xlsx_style_renderer.commit_row()

    @staticmethod
    def _map_include_field(_attr: bool) -> str:
        return "Include" if _attr else "Exclude"

    @classmethod
    def _map_by_type(cls, _attr: Any) -> str:
        if isinstance(_attr, Enum):
            return _attr.name

        elif isinstance(_attr, list):
            return ", ".join(_attr)

        elif isinstance(_attr, datetime):
            return datetime_to_str(_attr)

        elif isinstance(_attr, bool):
            return "Yes" if _attr else "No"

        return _attr

    def generate_filename(self, budget_id: int) -> str:
        current_date = timezone.now().date().strftime(self.FILENAME_DATE_FORMAT)
        return f"Budget_{budget_id}_Agreements_{current_date}.xlsx"
