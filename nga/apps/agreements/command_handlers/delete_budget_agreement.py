from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.commands import BulkDeleteBudgetAgreementCommand
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.apps.budgets.commands import (
    DeleteBudgetCommitmentTrafficCommand,
    ResetBudgetTrafficDiscountCommand,
    UpdateBudgetAfterAgreementsModifiedCommand,
)
from nga.internal.uow import AbstractUnitOfWork
from nga.utils.collections import to_id_list


@Mediator.handler
class BulkDeleteBudgetAgreementCommandHandler:
    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
    ) -> None:
        """Init deps."""

        self._mediator = mediator
        self._uow = uow

        self._budget_agreement_repository = budget_agreement_repository
        self._discount_repository = discount_repository

    def handle(self, cmd: BulkDeleteBudgetAgreementCommand) -> None:
        budget_agreements = self._budget_agreement_repository.get_many(
            budget_id=cmd.budget_id,
            budget_agreement_ids=cmd.budget_agreement_ids,
        )

        for budget_agreement in budget_agreements:
            with self._uow:
                self._delete_budget_agreement(budget_agreement)

        self._send_update_budget_cmd(cmd.budget_id)

    def _delete_budget_agreement(self, budget_agreement: BudgetAgreement) -> None:
        self._send_delete_budget_commitment_traffic_cmd(budget_agreement)

        self._send_reset_budget_traffic_discounts_cmd(budget_agreement)

        self._budget_agreement_repository.delete_by_id(budget_agreement.id)

    def _send_delete_budget_commitment_traffic_cmd(self, budget_agreement: BudgetAgreement) -> None:
        discounts = self._discount_repository.get_many(budget_agreement.agreement_id)

        delete_budget_traffic_cmd = DeleteBudgetCommitmentTrafficCommand(
            budget_id=budget_agreement.budget_id,
            discount_ids=to_id_list(discounts),
        )

        self._mediator.send(delete_budget_traffic_cmd)

    def _send_reset_budget_traffic_discounts_cmd(self, budget_agreement: BudgetAgreement) -> None:
        reset_traffic_discount_cmd = ResetBudgetTrafficDiscountCommand(
            budget_id=budget_agreement.budget_id,
            home_operators=budget_agreement.home_operators,
            partner_operators=budget_agreement.partner_operators,
            period=budget_agreement.period,
        )

        self._mediator.send(reset_traffic_discount_cmd)

    def _send_update_budget_cmd(self, budget_id: int) -> None:
        # can be reworked by listening to BudgetAgreementDeletedEvent
        update_budget_command = UpdateBudgetAfterAgreementsModifiedCommand(budget_id=budget_id)

        self._mediator.send(update_budget_command)
