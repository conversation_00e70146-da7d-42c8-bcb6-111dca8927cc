from typing import Optional

from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.agreements.commands import CreateBudgetAgreementCommand
from nga.apps.agreements.domain.exceptions import (
    AgreementNegotiatorDoesNotExist,
    AgreementValidationError,
    WrongIncludePremiumInCommitment,
)
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import (
    AbstractAgreementNegotiatorRepository,
    AbstractBudgetAgreementRepository,
)
from nga.apps.agreements.domain.validation import check_include_premium_in_commitment
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from nga.apps.common.validation import check_operators_for_existence
from nga.apps.references.exceptions import OperatorDoesNotExist
from nga.apps.references.providers import AbstractOperatorProvider


@Mediator.handler
class CreateBudgetAgreementCommandHandler:
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        agreement_negotiator_repository: AbstractAgreementNegotiatorRepository = Closing[
            Provide["agreement_negotiator_repository"]
        ],
        operator_provider: AbstractOperatorProvider = Closing[Provide["operator_provider"]],
    ) -> None:
        """Init deps."""

        self._mediator = mediator

        self._budget_agreement_repository = budget_agreement_repository
        self._agreement_negotiator_repository = agreement_negotiator_repository
        self._operator_provider = operator_provider

    def handle(self, cmd: CreateBudgetAgreementCommand) -> BudgetAgreement:
        try:
            check_operators_for_existence(self._operator_provider, cmd.budget_agreement_dto.home_operators)
            check_operators_for_existence(self._operator_provider, cmd.budget_agreement_dto.partner_operators)

            self.check_negotiator_for_existence(cmd.budget_agreement_dto.negotiator_id)

            check_include_premium_in_commitment(
                cmd.budget_agreement_dto.include_premium,
                cmd.budget_agreement_dto.include_premium_in_commitment,
            )

        except (OperatorDoesNotExist, AgreementNegotiatorDoesNotExist, WrongIncludePremiumInCommitment) as e:
            raise AgreementValidationError(e.message)

        budget_agreement = self._budget_agreement_repository.create(cmd.budget_agreement_dto, cmd.budget_id)

        # can be reworked by listening to BudgetAgreementCreatedEvent
        update_budget_command = UpdateBudgetAfterAgreementsModifiedCommand(budget_id=budget_agreement.budget_id)

        self._mediator.send(update_budget_command)

        return budget_agreement

    def check_negotiator_for_existence(self, negotiator_id: Optional[int]) -> None:
        if negotiator_id is not None:
            self._agreement_negotiator_repository.get_by_id(negotiator_id)

        return None
