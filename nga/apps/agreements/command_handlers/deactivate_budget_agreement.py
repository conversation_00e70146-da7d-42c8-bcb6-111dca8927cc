from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.commands import BulkDeactivateBudgetAgreementCommand
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.apps.agreements.dto import BudgetAgreementError
from nga.apps.budgets.commands import (
    DeleteBudgetCommitmentTrafficCommand,
    ResetBudgetTrafficDiscountCommand,
    UpdateBudgetAfterAgreementsModifiedCommand,
)
from nga.internal.uow import AbstractUnitOfWork
from nga.utils.collections import to_id_list


@Mediator.handler
class BulkDeactivateBudgetAgreementCommandHandler:
    @inject
    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Provide["mediator"],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
    ) -> None:
        """Init deps."""

        self._uow = uow
        self._mediator = mediator

        self._budget_agreement_repository = budget_agreement_repository
        self._discount_repository = discount_repository

    def handle(
        self, cmd: BulkDeactivateBudgetAgreementCommand
    ) -> tuple[list[BudgetAgreement], list[BudgetAgreementError]]:
        budget_agreements = self._budget_agreement_repository.get_many(
            budget_id=cmd.budget_id, budget_agreement_ids=cmd.budget_agreement_ids
        )

        deactivated_budget_agreements = []

        errors = []

        for budget_agreement in budget_agreements:

            if not budget_agreement.is_active:
                continue

            try:
                with self._uow:
                    budget_agreement.deactivate()

                    self._budget_agreement_repository.save(budget_agreement)

                    self.send_reset_budget_traffic_discount_command(budget_agreement)

                    self.send_delete_budget_commitment_traffic(budget_agreement)

                    deactivated_budget_agreements.append(budget_agreement)

            except Exception as e:
                errors.append(BudgetAgreementError(agreement_id=budget_agreement.id, detail=str(e)))

        update_budget_command = UpdateBudgetAfterAgreementsModifiedCommand(budget_id=cmd.budget_id)

        self._mediator.send(update_budget_command)

        return deactivated_budget_agreements, errors

    def send_reset_budget_traffic_discount_command(self, budget_agreement: BudgetAgreement) -> None:
        reset_traffic_discount_cmd = ResetBudgetTrafficDiscountCommand(
            budget_id=budget_agreement.budget_id,
            home_operators=budget_agreement.home_operators,
            partner_operators=budget_agreement.partner_operators,
            period=budget_agreement.period,
        )

        self._mediator.send(reset_traffic_discount_cmd)

    def send_delete_budget_commitment_traffic(self, budget_agreement: BudgetAgreement) -> None:
        discounts = self._discount_repository.get_many(budget_agreement.agreement_id)

        discount_ids = to_id_list(discounts)

        delete_budget_traffic_cmd = DeleteBudgetCommitmentTrafficCommand(budget_agreement.budget_id, discount_ids)

        self._mediator.send(delete_budget_traffic_cmd)
