from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.agreements.commands import AutoRenewBudgetAgreementCommand
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.agreements.domain.services import AbstractBudgetAgreementRenewService
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.internal.domain import EventPublisher
from nga.internal.uow import AbstractUnitOfWork


@Mediator.handler
class AutoRenewBudgetAgreementCommandHandler:
    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Provide["mediator"],
        event_dispatcher: EventPublisher = Provide["event_dispatcher"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        budget_agreement_renew_service: AbstractBudgetAgreementRenewService = Closing[
            Provide["budget_agreement_renew_service"]
        ],
    ) -> None:
        """Init deps."""

        self._uow = uow
        self._mediator = mediator
        self._event_dispatcher = event_dispatcher

        self._budget_repository = budget_repository
        self._budget_agreement_repository = budget_agreement_repository

        self._budget_agreement_renew_service = budget_agreement_renew_service

    def handle(self, cmd: AutoRenewBudgetAgreementCommand) -> list[BudgetAgreement]:
        """Performs Agreement auto-renew action."""

        auto_renewed_budget_agreements = []

        base_budget_agreement = self._budget_agreement_repository.get_by_id(cmd.budget_agreement_id)

        budget = self._budget_repository.get_by_id(base_budget_agreement.budget_id)

        while base_budget_agreement.renew_period() in budget.period:
            with self._uow:
                nested_budget_agreement = self._budget_agreement_repository.get_by_parent_id(
                    base_budget_agreement.agreement_id,
                    budget_id=budget.id,
                )

                if nested_budget_agreement is None:
                    nested_budget_agreement = self._budget_agreement_renew_service.renew(
                        budget,
                        base_budget_agreement,
                    )

                    nested_budget_agreement.set_status(AgreementStatusEnum.AUTO_RENEWED)

                    nested_budget_agreement.parent_id = base_budget_agreement.agreement_id

                    self._budget_agreement_repository.save(nested_budget_agreement)

                    events = nested_budget_agreement.pop_events()

                    self._event_dispatcher.publish(*events)

                auto_renewed_budget_agreements.append(nested_budget_agreement)

                base_budget_agreement = nested_budget_agreement

        if auto_renewed_budget_agreements:

            update_budget_command = UpdateBudgetAfterAgreementsModifiedCommand(budget_id=budget.id)

            self._mediator.send(update_budget_command)

        return auto_renewed_budget_agreements
