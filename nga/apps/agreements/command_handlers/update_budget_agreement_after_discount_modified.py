from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.commands import UpdateBudgetAgreementAfterDiscountModifiedCommand
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from nga.utils.dt import get_current_datetime_utc


@Mediator.handler
class UpdateBudgetAgreementAfterDiscountModifiedCommandHandler:
    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
    ) -> None:
        self._mediator = mediator
        self._budget_agreement_repository = budget_agreement_repository

    def handle(self, cmd: UpdateBudgetAgreementAfterDiscountModifiedCommand) -> None:
        budget_agreement = self._budget_agreement_repository.get_by_id(cmd.budget_agreement_id)

        budget_agreement.updated_at = get_current_datetime_utc()

        budget_agreement.reset_calculation_status_after_modification()

        budget_agreement = self._budget_agreement_repository.save(budget_agreement)

        update_budget_cmd = UpdateBudgetAfterAgreementsModifiedCommand(budget_agreement.budget_id)

        self._mediator.send(update_budget_cmd)
