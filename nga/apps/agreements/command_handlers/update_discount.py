from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.agreements.commands import UpdateBudgetAgreementAfterDiscountModifiedCommand, UpdateDiscountCommand
from nga.apps.agreements.domain.discount_model_properties import DiscountModelProperties
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.domain.specifications.abstract import AbstractDiscountSpecification
from nga.apps.budgets.commands import DeleteBudgetCommitmentTrafficCommand
from nga.core.types import DatePeriod


@Mediator.handler
class UpdateDiscountCommandHandler:
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
        discount_modification_spec: AbstractDiscountSpecification = Closing[Provide["discount_modification_spec"]],
    ) -> None:
        """Init deps."""

        self._mediator = mediator

        self._discount_repository = discount_repository
        self._discount_modification_spec = discount_modification_spec

    def handle(self, cmd: UpdateDiscountCommand) -> Discount:
        discount = self._update_discount(cmd.discount, cmd)

        self.verify_discount(discount)

        self._delete_commitment_traffic(cmd)

        self._notify_budget_agreement(cmd.budget_agreement.id)

        return discount

    def _delete_commitment_traffic(self, cmd: UpdateDiscountCommand) -> None:
        model_properties = DiscountModelProperties(cmd.discount)

        if model_properties.has_commitment is False:
            return None

        delete_traffic_cmd = DeleteBudgetCommitmentTrafficCommand(
            budget_id=cmd.budget_agreement.budget_id,
            discount_ids=[cmd.discount.id],
        )

        self._mediator.send(delete_traffic_cmd)

    def _notify_budget_agreement(self, budget_agreement_id: int) -> None:
        command = UpdateBudgetAgreementAfterDiscountModifiedCommand(budget_agreement_id)

        self._mediator.send(command)

    def _update_discount(self, discount: Discount, cmd: UpdateDiscountCommand) -> Discount:
        if cmd.data:
            for field, value in cmd.data.items():
                if field in ("start_date", "end_date"):
                    period = discount.period
                    setattr(period, field, value)
                    discount.period = DatePeriod(*period.period)
                else:
                    setattr(discount, field, value)

            discount = self._discount_repository.save(discount)

        if cmd.parameters_dtos:
            discount.parameters = self._discount_repository.set_parameters(discount.id, cmd.parameters_dtos)

        discount = self._discount_repository.save(discount)

        return discount

    def verify_discount(self, discount: Discount) -> None:
        self._discount_modification_spec.verify(discount)
