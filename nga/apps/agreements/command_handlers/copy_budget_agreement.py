from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.commands import BulkCopyBudgetAgreementCommand
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.apps.agreements.domain.utils import copy_discounts
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from nga.apps.budgets.domain.dto import BudgetParametersFilters
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.internal.uow import AbstractUnitOfWork


@Mediator.handler
class BulkCopyBudgetAgreementCommandHandler:
    @inject
    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
    ) -> None:
        self._uow = uow
        self._mediator = mediator

        self._budget_repository = budget_repository

        self._budget_agreement_repository = budget_agreement_repository
        self._discount_repository = discount_repository

    def handle(self, cmd: BulkCopyBudgetAgreementCommand) -> list[BudgetAgreement]:
        target_budget = self._budget_repository.get_by_id(cmd.target_budget_id)

        budget_agreements = self._budget_agreement_repository.get_many(
            budget_id=cmd.source_budget_id,
            budget_agreement_ids=cmd.budget_agreement_ids,
            budget_parameters=BudgetParametersFilters(home_operators=target_budget.home_operators),
            start_date=target_budget.period.start_date,
            end_date=target_budget.period.end_date,
            only_active=cmd.only_active,
        )

        target_confirmed_budget_agreements = self._budget_agreement_repository.get_many(
            budget_id=target_budget.id, statuses=list(AgreementStatusEnum.get_confirmed_statuses())
        )
        target_confirmed_agreement_ids = [ba.agreement_id for ba in target_confirmed_budget_agreements]

        copied_budget_agreements = []

        for ba in budget_agreements:
            if not set(ba.home_operators).issubset(set(target_budget.home_operators)):
                continue

            if (
                ba.status in AgreementStatusEnum.get_confirmed_statuses()
                and ba.agreement_id in target_confirmed_agreement_ids
            ):
                continue

            with self._uow:
                copied_budget_agreement = self._copy(ba, cmd.target_budget_id)

            copied_budget_agreements.append(copied_budget_agreement)

        update_budget_command = UpdateBudgetAfterAgreementsModifiedCommand(budget_id=cmd.target_budget_id)

        self._mediator.send(update_budget_command)

        return copied_budget_agreements

    def _copy(self, budget_agreement: BudgetAgreement, target_budget_id: int) -> BudgetAgreement:
        copied_budget_agreement = self._budget_agreement_repository.copy(budget_agreement, target_budget_id)

        if budget_agreement.is_confirmed is False:
            source_discounts = self._discount_repository.get_many(budget_agreement.agreement_id)

            copy_discounts(source_discounts, copied_budget_agreement, self._discount_repository)

        return copied_budget_agreement
