from typing import cast

from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.agreements.commands import DeleteDiscountCommand, UpdateBudgetAgreementAfterDiscountModifiedCommand
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.budgets.commands import DeleteBudgetCommitmentTrafficCommand


@Mediator.handler
class DeleteDiscountCommandHandler:
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
    ) -> None:
        """Init deps."""

        self._mediator = mediator
        self._discount_repository = discount_repository

    def handle(self, cmd: DeleteDiscountCommand) -> None:
        self._delete_commitment_traffic(cmd)

        self._discount_repository.delete_by_id(cmd.discount.id)

        command = UpdateBudgetAgreementAfterDiscountModifiedCommand(cmd.budget_agreement.id)

        self._mediator.send(command)

    def _delete_commitment_traffic(self, cmd: DeleteDiscountCommand) -> None:
        discount_for_budget_traffic_removal = cmd.discount

        if cmd.discount.is_parent is False:
            parent_discount = self._discount_repository.get_by_id(cast(int, cmd.discount.parent_id))

            discount_for_budget_traffic_removal = parent_discount

        delete_traffic_cmd = DeleteBudgetCommitmentTrafficCommand(
            budget_id=cmd.budget_agreement.budget_id,
            discount_ids=[discount_for_budget_traffic_removal.id],
        )

        self._mediator.send(delete_traffic_cmd)
