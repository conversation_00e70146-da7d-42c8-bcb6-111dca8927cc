from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.agreements.commands import BulkRenewBudgetAgreementCommand
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.agreements.domain.services import AbstractBudgetAgreementRenewService
from nga.apps.agreements.dto import BudgetAgreementError
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.internal.uow import AbstractUnitOfWork


@Mediator.handler
class BulkRenewBudgetAgreementCommandHandler:
    """
    Performs Agreement renew action. Agreement renew action is the process of creating new agreement in the same
    budget and with the same parameters except of status (DRAFT is set) and period. Period is evaluated by this
    formula:
        Renewed start date = end date + 1 month
        Renewed end date = Renewed start date + agreement period total months

    Examples:
        * 2023-01 - 2023-12 → 2024-01 - 2024-12
        * 2023-01 - 2023-06 → 2023-07 - 2023-12
        * 2023-01 - 2023-03 → 2023-04 - 2023-06
    """

    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        budget_agreement_renew_service: AbstractBudgetAgreementRenewService = Closing[
            Provide["budget_agreement_renew_service"]
        ],
    ) -> None:
        """Init deps."""

        self._uow = uow
        self._mediator = mediator

        self._budget_repository = budget_repository
        self._budget_agreement_repository = budget_agreement_repository

        self._budget_agreement_renew_service = budget_agreement_renew_service

    def handle(self, cmd: BulkRenewBudgetAgreementCommand) -> tuple[list[BudgetAgreement], list[BudgetAgreementError]]:
        """Performs Agreement renew action."""

        budget = self._budget_repository.get_by_id(cmd.budget_id)

        budget_agreements = self._budget_agreement_repository.get_many(
            budget_id=cmd.budget_id,
            budget_agreement_ids=cmd.budget_agreement_ids,
        )

        renewed_budget_agreements = []

        errors = []

        for budget_agreement in budget_agreements:
            try:
                with self._uow:
                    renewed_budget_agreement = self._budget_agreement_renew_service.renew(
                        budget,
                        budget_agreement,
                    )

                    renewed_budget_agreements.append(renewed_budget_agreement)

            except Exception as e:
                errors.append(BudgetAgreementError(agreement_id=budget_agreement.id, detail=str(e)))

        update_budget_command = UpdateBudgetAfterAgreementsModifiedCommand(budget_id=cmd.budget_id)

        self._mediator.send(update_budget_command)

        return renewed_budget_agreements, errors
