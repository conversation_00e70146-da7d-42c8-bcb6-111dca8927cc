from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.commands import BulkCopyBudgetAgreementCommand, ConnectAgreementWithBudgetsCommand
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository


@Mediator.handler
class ConnectAgreementWithBudgetsCommandHandler:
    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
    ) -> None:
        """Init deps."""

        self._mediator = mediator
        self._budget_repository = budget_repository
        self._budget_agreement_repository = budget_agreement_repository

    def handle(self, event: ConnectAgreementWithBudgetsCommand) -> None:
        budget_agreement = self._budget_agreement_repository.get_by_id(event.budget_agreement_id)

        budgets = self._budget_repository.get_many(home_operators=budget_agreement.home_operators)

        for budget in budgets:
            if budget.id == budget_agreement.budget_id or budget_agreement.period not in budget.period:
                continue

            copy_cmd = BulkCopyBudgetAgreementCommand(
                budget_agreement_ids=[budget_agreement.id],
                target_budget_id=budget.id,
                source_budget_id=None,
                only_active=None,
            )

            self._mediator.send(copy_cmd)
