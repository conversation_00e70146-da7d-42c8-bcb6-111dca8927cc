from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.agreements.commands import AutoRenewBudgetAgreementCommand, BulkStatusChangeBudgetAgreementCommand
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.agreements.domain.utils import unlink_agreements, verify_if_agreement_approved
from nga.apps.agreements.dto import BudgetAgreementError
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from nga.internal.domain import EventPublisher
from nga.internal.uow import AbstractUnitOfWork
from nga.utils.dt import get_current_datetime_utc


@Mediator.handler
class BulkStatusChangeBudgetAgreementCommandHandler:

    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Provide["mediator"],
        event_dispatcher: EventPublisher = Provide["event_dispatcher"],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
    ) -> None:
        """Init deps."""

        self._uow = uow
        self._mediator = mediator
        self._event_dispatcher = event_dispatcher

        self._budget_agreement_repository = budget_agreement_repository

    def handle(
        self, cmd: BulkStatusChangeBudgetAgreementCommand
    ) -> tuple[list[BudgetAgreement], list[BudgetAgreementError]]:
        budget_agreements = self._budget_agreement_repository.get_many(
            budget_id=cmd.budget_id,
            statuses=[cmd.initial_status],
            budget_agreement_ids=cmd.budget_agreement_ids,
            only_active=cmd.only_active,
        )

        status_changed_budget_agreements = []

        errors = []

        for budget_agreement in budget_agreements:
            try:
                with self._uow:
                    status_changed_budget_agreement = self.change_status_for_budget_agreement(
                        budget_agreement,
                        cmd.target_status,
                    )

                    status_changed_budget_agreements.append(status_changed_budget_agreement)

            except Exception as e:
                errors.append(BudgetAgreementError(agreement_id=budget_agreement.id, detail=str(e)))

        update_budget_command = UpdateBudgetAfterAgreementsModifiedCommand(budget_id=cmd.budget_id)

        self._mediator.send(update_budget_command)

        return status_changed_budget_agreements, errors

    def change_status_for_budget_agreement(
        self,
        budget_agreement: BudgetAgreement,
        target_status: AgreementStatusEnum,
    ) -> BudgetAgreement:
        budget_agreement.set_status(target_status)

        if budget_agreement.status == AgreementStatusEnum.APPROVED:
            verify_if_agreement_approved(self._budget_agreement_repository, budget_agreement)

            if budget_agreement.is_rolling:
                self._mediator.send(AutoRenewBudgetAgreementCommand(budget_agreement_id=budget_agreement.id))

        elif budget_agreement.status == AgreementStatusEnum.REJECTED:
            unlink_agreements(self._budget_agreement_repository, budget_agreement)

        budget_agreement.reset_calculation_status_after_modification()

        budget_agreement.updated_at = get_current_datetime_utc()

        events = budget_agreement.pop_events()

        self._event_dispatcher.publish(*events)

        self._budget_agreement_repository.save(budget_agreement)

        return budget_agreement
