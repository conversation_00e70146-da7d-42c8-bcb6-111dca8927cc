from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.agreements.commands import AutoRenewBudgetAgreementCommand, UpdateAgreementCommand
from nga.apps.agreements.domain.exceptions import (
    AgreementNegotiatorDoesNotExist,
    AgreementValidationError,
    WrongIncludePremiumInCommitment,
)
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import (
    AbstractAgreementNegotiatorRepository,
    AbstractBudgetAgreementRepository,
)
from nga.apps.agreements.domain.utils import unlink_agreements, verify_if_agreement_approved
from nga.apps.agreements.domain.validation import check_include_premium_in_commitment
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.internal.domain import EventPublisher
from nga.utils.dt import get_current_datetime_utc


@Mediator.handler
class UpdateAgreementCommandHandler:
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        event_dispatcher: EventPublisher = Provide["event_dispatcher"],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        agreement_negotiator_repository: AbstractAgreementNegotiatorRepository = Closing[
            Provide["agreement_negotiator_repository"]
        ],
    ) -> None:
        """Init deps."""

        self._mediator = mediator
        self._event_dispatcher = event_dispatcher

        self._budget_agreement_repository = budget_agreement_repository
        self._agreement_negotiator_repository = agreement_negotiator_repository

    def handle(self, cmd: UpdateAgreementCommand) -> BudgetAgreement:
        budget_agreement = self._budget_agreement_repository.get_by_id(cmd.agreement_id)

        budget_agreement = self.update(budget_agreement, cmd)

        events = budget_agreement.pop_events()

        budget_agreement = self._budget_agreement_repository.save(budget_agreement)

        if budget_agreement.status == AgreementStatusEnum.APPROVED:
            verify_if_agreement_approved(self._budget_agreement_repository, budget_agreement)

            if budget_agreement.is_rolling:
                self._mediator.send(AutoRenewBudgetAgreementCommand(budget_agreement_id=budget_agreement.id))

        elif budget_agreement.status == AgreementStatusEnum.REJECTED:
            unlink_agreements(self._budget_agreement_repository, budget_agreement)

        self._event_dispatcher.publish(*events)

        return budget_agreement

    def update(self, budget_agreement: BudgetAgreement, cmd: UpdateAgreementCommand) -> BudgetAgreement:
        try:
            if cmd.negotiator_id is not None:
                self._agreement_negotiator_repository.get_by_id(cmd.negotiator_id)

            check_include_premium_in_commitment(
                cmd.include_premium,
                cmd.include_premium_in_commitment,
            )

        except (AgreementNegotiatorDoesNotExist, WrongIncludePremiumInCommitment) as e:
            raise AgreementValidationError(e.message)

        budget_agreement.name = cmd.name
        budget_agreement.negotiator_id = cmd.negotiator_id

        budget_agreement.include_satellite = cmd.include_satellite
        budget_agreement.include_premium = cmd.include_premium
        budget_agreement.include_premium_in_commitment = cmd.include_premium_in_commitment

        budget_agreement.is_rolling = cmd.is_rolling

        budget_agreement.set_status(cmd.status)

        budget_agreement.reset_calculation_status_after_modification()

        budget_agreement.updated_at = get_current_datetime_utc()

        return budget_agreement
