from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.commands import BulkActivateBudgetAgreementCommand
from nga.apps.agreements.domain.exceptions import AgreementIntersectionError
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.agreements.dto import BudgetAgreementError
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from nga.internal.uow import AbstractUnitOfWork


@Mediator.handler
class BulkActivateBudgetAgreementCommandHandler:
    @inject
    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Provide["mediator"],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
    ) -> None:
        """Init deps."""

        self._uow = uow
        self._mediator = mediator

        self._budget_agreement_repository = budget_agreement_repository

    def handle(
        self, cmd: BulkActivateBudgetAgreementCommand
    ) -> tuple[list[BudgetAgreement], list[BudgetAgreementError]]:
        budget_agreements = self._budget_agreement_repository.get_many(
            budget_id=cmd.budget_id, budget_agreement_ids=cmd.budget_agreement_ids
        )

        activated_budget_agreements = []

        errors = []

        # NOTE: Cannot use bulk activation instead of loop, will found issue with intersections
        for budget_agreement in budget_agreements:

            if budget_agreement.is_active:
                continue

            try:
                with self._uow:
                    activated_agreement = self.activate_budget_agreement(budget_agreement)

                    activated_budget_agreements.append(activated_agreement)

            except Exception as e:
                errors.append(BudgetAgreementError(agreement_id=budget_agreement.id, detail=str(e)))

        update_budget_command = UpdateBudgetAfterAgreementsModifiedCommand(budget_id=cmd.budget_id)

        self._mediator.send(update_budget_command)

        return activated_budget_agreements, errors

    def activate_budget_agreement(self, budget_agreement: BudgetAgreement) -> BudgetAgreement:
        budget_agreement_has_intersection = self._budget_agreement_repository.has_intersection(
            budget_agreement,
            with_active=True,
        )

        if budget_agreement_has_intersection is True:
            raise AgreementIntersectionError(budget_agreement.id)

        budget_agreement.activate()

        activated_agreement = self._budget_agreement_repository.save(budget_agreement)

        return activated_agreement
