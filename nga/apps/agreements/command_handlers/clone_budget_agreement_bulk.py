from typing import Optional

from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.agreements.commands import BulkCloneBudgetAgreementCommand, CreateBudgetAgreementCommand
from nga.apps.agreements.domain.dto import BudgetAgreementCreateDTO
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.apps.agreements.domain.utils import copy_discounts
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from nga.internal.uow import AbstractUnitOfWork


@Mediator.handler
class BulkCloneBudgetAgreementCommandHandler:
    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Provide["mediator"],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
    ) -> None:
        """Init deps."""

        self._uow = uow
        self._mediator = mediator
        self._budget_agreement_repository = budget_agreement_repository
        self._discount_repository = discount_repository

    def handle(self, cmd: BulkCloneBudgetAgreementCommand) -> list[BudgetAgreement]:
        budget_agreements = self._budget_agreement_repository.get_many(
            budget_id=cmd.budget_id,
            budget_agreement_ids=cmd.budget_agreement_ids,
        )

        if not budget_agreements:
            return []

        cloned_budget_agreements = []

        for budget_agreement in budget_agreements:
            with self._uow:
                cloned_budget_agreement = self.clone_agreement(budget_agreement, new_name=cmd.new_name)

                self.clone_discounts(budget_agreement, cloned_budget_agreement)

                cloned_budget_agreements.append(cloned_budget_agreement)

        update_budget_command = UpdateBudgetAfterAgreementsModifiedCommand(budget_id=cmd.budget_id)

        self._mediator.send(update_budget_command)

        return cloned_budget_agreements

    def clone_discounts(self, base_budget_agreement: BudgetAgreement, cloned_budget_agreement: BudgetAgreement) -> None:
        discounts = self._discount_repository.get_many(base_budget_agreement.agreement_id)

        copy_discounts(discounts, cloned_budget_agreement, self._discount_repository)

    def clone_agreement(self, budget_agreement: BudgetAgreement, new_name: Optional[str]) -> BudgetAgreement:
        if new_name is None:
            new_name = f"{budget_agreement.name}_CLONED"

        budget_agreement_dto = BudgetAgreementCreateDTO(
            name=new_name,
            home_operators=budget_agreement.home_operators,
            partner_operators=budget_agreement.partner_operators,
            period=budget_agreement.period,
            negotiator_id=budget_agreement.negotiator_id,
            include_satellite=budget_agreement.include_satellite,
            include_premium=budget_agreement.include_premium,
            include_premium_in_commitment=budget_agreement.include_premium_in_commitment,
            is_rolling=budget_agreement.is_rolling,
        )

        create_agreement_cmd = CreateBudgetAgreementCommand(
            budget_id=budget_agreement.budget_id,
            budget_agreement_dto=budget_agreement_dto,
        )

        new_agreement = self._mediator.send(create_agreement_cmd)

        return new_agreement
