from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.agreements.commands import CreateSubDiscountCommand, UpdateBudgetAgreementAfterDiscountModifiedCommand
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.domain.specifications.abstract import AbstractDiscountSpecification
from nga.core.exceptions import BaseNGAException


@Mediator.handler
class CreateSubDiscountCommandHandler:
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
        sub_discount_modification_spec: AbstractDiscountSpecification = Closing[
            Provide["sub_discount_modification_spec"]
        ],
        sub_discount_setup_spec: AbstractDiscountSpecification = Closing[Provide["sub_discount_setup_spec"]],
    ) -> None:
        """Init dependencies."""

        self._mediator = mediator

        self._discount_repository = discount_repository
        self._sub_discount_modification_spec = sub_discount_modification_spec
        self._sub_discount_setup_spec = sub_discount_setup_spec

    def handle(self, cmd: CreateSubDiscountCommand) -> None:

        if not cmd.discount.is_parent:
            raise BaseNGAException("Forbidden to create sub-discount. Target discount is sub-discount.")

        sub_discount = self._discount_repository.create_sub_discount(cmd.discount.id, cmd.sub_discount_dto)

        sub_discount = self._discount_repository.save(sub_discount)

        cmd.discount.add_sub_discount(sub_discount)

        self._sub_discount_modification_spec.verify(cmd.discount)

        self._sub_discount_setup_spec.verify(sub_discount)

        self._notify_budget_agreement(cmd.budget_agreement.id)

    def _notify_budget_agreement(self, budget_agreement_id: int) -> None:
        command = UpdateBudgetAgreementAfterDiscountModifiedCommand(budget_agreement_id)

        self._mediator.send(command)
