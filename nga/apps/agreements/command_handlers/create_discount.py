from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.agreements.commands import CreateDiscountCommand, UpdateBudgetAgreementAfterDiscountModifiedCommand
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.domain.specifications.abstract import AbstractDiscountSpecification
from nga.internal.uow import AbstractUnitOfWork


@Mediator.handler
class CreateDiscountCommandHandler:
    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Provide["mediator"],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
        discount_modification_spec: AbstractDiscountSpecification = Closing[Provide["discount_modification_spec"]],
        sub_discount_modification_spec: AbstractDiscountSpecification = Closing[
            Provide["sub_discount_modification_spec"]
        ],
    ) -> None:
        """Init deps."""
        self._uow = uow

        self._mediator = mediator

        self._discount_repository = discount_repository
        self._discount_modification_spec = discount_modification_spec

        self._sub_discount_modification_spec = sub_discount_modification_spec

    def handle(self, cmd: CreateDiscountCommand) -> Discount:
        budget_agreement = cmd.budget_agreement

        discount = self._discount_repository.create(budget_agreement.agreement_id, cmd.discount_dto)

        discount = self._discount_repository.save(discount)

        if cmd.discount_dto.sub_discounts is not None:
            self.set_sub_discounts(budget_agreement.agreement_id, list(cmd.discount_dto.sub_discounts), discount.id)

        self._discount_modification_spec.verify(discount)

        update_budget_agreement_cmd = UpdateBudgetAgreementAfterDiscountModifiedCommand(budget_agreement.id)

        self._mediator.send(update_budget_agreement_cmd)

        return discount

    def set_sub_discounts(
        self,
        agreement_id: int,
        sub_discount_ids: list[int],
        parent_id: int,
    ) -> None:
        sub_discounts = self._discount_repository.get_many(agreement_id=agreement_id, discount_ids=sub_discount_ids)

        for sub_discount in sub_discounts:
            sub_discount.parent_id = parent_id

            with self._uow:
                self._discount_repository.save(sub_discount)

                parent_discount = self._discount_repository.get_by_id(parent_id)

                self._sub_discount_modification_spec.verify(parent_discount)
