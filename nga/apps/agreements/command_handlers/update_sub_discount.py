from typing import cast

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.command_handlers.update_discount import UpdateDiscountCommandHandler
from nga.apps.agreements.commands import UpdateSubDiscountCommand
from nga.apps.agreements.domain.discount_model_properties import DiscountModelProperties
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.domain.specifications.abstract import AbstractDiscountSpecification
from nga.apps.budgets.commands import DeleteBudgetCommitmentTrafficCommand


@Mediator.handler
class UpdateSubDiscountCommandHandler(UpdateDiscountCommandHandler):
    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
        sub_discount_modification_spec: AbstractDiscountSpecification = Closing[
            Provide["sub_discount_modification_spec"]
        ],
        sub_discount_setup_spec: AbstractDiscountSpecification = Closing[Provide["sub_discount_setup_spec"]],
    ) -> None:
        super().__init__(
            mediator=mediator,
            discount_repository=discount_repository,
            discount_modification_spec=sub_discount_modification_spec,
        )

        self._sub_discount_setup_spec = sub_discount_setup_spec

    def handle(self, cmd: UpdateSubDiscountCommand) -> Discount:
        return super().handle(cmd)

    def verify_discount(self, discount: Discount) -> None:
        self._sub_discount_setup_spec.verify(discount)

        parent_discount = self._discount_repository.get_by_id(cast(int, discount.parent_id))

        return super().verify_discount(parent_discount)

    def _delete_commitment_traffic(self, cmd: UpdateSubDiscountCommand) -> None:
        parent_discount = self._discount_repository.get_by_id(cast(int, cmd.discount.parent_id))

        model_properties = DiscountModelProperties(parent_discount)

        if model_properties.has_commitment is False:
            return None

        delete_traffic_cmd = DeleteBudgetCommitmentTrafficCommand(
            budget_id=cmd.budget_agreement.budget_id,
            discount_ids=[cast(int, cmd.discount.parent_id)],
        )

        self._mediator.send(delete_traffic_cmd)
