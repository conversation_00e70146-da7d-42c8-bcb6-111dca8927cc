import json
from decimal import Decimal
from typing import Optional

from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Case, IntegerField, Q, QuerySet, When

from nga.apps.agreements.domain.dto import DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.domain.exceptions import DiscountDoesNotExist
from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.domain.models.discount import CommitmentDistributionParameter
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.domain.value_objects import DiscountQualifyingRule
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.agreements.infra.encoders import CommitmentDistributionParameterJSONEncoder
from nga.apps.agreements.infra.orm import models
from nga.apps.agreements.infra.repositories.queries import qualifying_bounds_intersection_query
from nga.apps.common.queryset_utils import period_intersection_query
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import DatePeriod


class DiscountDjangoORMRepository(AbstractDiscountRepository):
    def get_by_id(self, discount_id: int) -> Discount:
        """Returns discount instance by provided ID."""

        orm_discount = self.get_orm_discount_by_id(discount_id)

        return from_orm_to_domain_discount(orm_discount)

    @classmethod
    def get_orm_discount_by_id(cls, discount_id: int) -> models.Discount:
        try:
            orm_discount = models.Discount.objects.prefetch_related("sub_discounts").get(pk=discount_id)
        except models.Discount.DoesNotExist:
            raise DiscountDoesNotExist(discount_id)

        return orm_discount

    def get_many(
        self,
        agreement_id: int,
        *,
        discount_ids: Optional[list[int]] = None,
        home_operators: Optional[list[int]] = None,
        partner_operators: Optional[list[int]] = None,
        directions: Optional[list[DiscountDirectionEnum]] = None,
        service_types: Optional[list[ServiceTypeEnum]] = None,
        period: Optional[DatePeriod] = None,
    ) -> tuple[Discount, ...]:
        qs = models.Discount.objects.filter(agreement_id=agreement_id, parent__isnull=True)

        if discount_ids is not None:
            qs = qs.filter(id__in=discount_ids)

        if home_operators is not None:
            qs = qs.filter(home_operators__in=home_operators)

        if partner_operators is not None:
            qs = qs.filter(partner_operators__in=partner_operators)

        if directions is not None:
            qs = qs.filter(direction__in=directions)

        if service_types is not None:
            qs = qs.filter(service_types__overlap=service_types)

        if period is not None:
            qs = qs.filter(
                start_date__gte=period.start_date,
                end_date__lte=period.end_date,
            )

        qs = qs.prefetch_related("parameters", "sub_discounts").distinct()

        return tuple(from_orm_to_domain_discount(orm_d) for orm_d in qs)

    def create(self, agreement_id: int, discount_dto: DiscountDTO) -> Discount:
        json_commitment_distribution_parameters = None

        if discount_dto.commitment_distribution_parameters is not None:
            json_commitment_distribution_parameters = CommitmentDistributionParameterJSONEncoder.encode_many(
                discount_dto.commitment_distribution_parameters
            )
        orm_discount = models.Discount.objects.create(
            agreement_id=agreement_id,
            parent=None,
            direction=discount_dto.direction,
            service_types=discount_dto.service_types,
            start_date=discount_dto.start_date,
            end_date=discount_dto.end_date,
            currency_code=discount_dto.currency_code,
            tax_type=discount_dto.tax_type,
            volume_type=discount_dto.volume_type,
            settlement_method=discount_dto.settlement_method,
            call_destinations=discount_dto.call_destinations,
            imsi_count_type=discount_dto.imsi_count_type,
            qualifying_direction=(
                discount_dto.qualifying_rule.direction if discount_dto.qualifying_rule is not None else None
            ),
            qualifying_service_types=(
                discount_dto.qualifying_rule.service_types if discount_dto.qualifying_rule is not None else None
            ),
            qualifying_basis=discount_dto.qualifying_rule.basis if discount_dto.qualifying_rule is not None else None,
            qualifying_lower_bound=(
                discount_dto.qualifying_rule.lower_bound if discount_dto.qualifying_rule is not None else None
            ),
            qualifying_upper_bound=(
                discount_dto.qualifying_rule.upper_bound if discount_dto.qualifying_rule is not None else None
            ),
            model_type=discount_dto.model_type,
            above_commitment_rate=discount_dto.above_commitment_rate,
            inbound_market_share=discount_dto.inbound_market_share,
            commitment_distribution_parameters=json_commitment_distribution_parameters,
            financial_threshold=discount_dto.financial_threshold,
            above_financial_threshold_rate=discount_dto.above_financial_threshold_rate,
        )

        orm_discount.home_operators.set(discount_dto.home_operators)
        orm_discount.partner_operators.set(discount_dto.partner_operators)

        if discount_dto.called_countries:
            orm_discount.called_countries.set(discount_dto.called_countries)

        if discount_dto.traffic_segments:
            orm_discount.traffic_segments.set(discount_dto.traffic_segments)

        domain_parameters = self.create_parameters(orm_discount.pk, discount_dto.parameters)

        return from_orm_to_domain_discount(orm_discount, domain_parameters=domain_parameters)

    def create_sub_discount(self, discount_id: int, sub_discount_dto: DiscountDTO) -> Discount:
        orm_discount = self.get_orm_discount_by_id(discount_id)

        sub_discount = self.create(orm_discount.agreement_id, sub_discount_dto)

        orm_sub_discount = self.get_orm_discount_by_id(sub_discount.id)
        orm_sub_discount.parent_id = discount_id
        orm_sub_discount.save(update_fields=["parent_id"])

        return from_orm_to_domain_discount(orm_sub_discount)

    @staticmethod
    def create_parameters(
        discount_id: int,
        parameters_dtos: tuple[DiscountParameterDTO, ...],
    ) -> tuple[DiscountParameter, ...]:
        """Creates and persists collection of Discount Parameters."""

        domain_parameters = []

        for parameter in parameters_dtos:
            orm_param = models.DiscountParameter.objects.create(
                discount_id=discount_id,
                calculation_type=parameter.calculation_type,
                basis=parameter.basis,
                basis_value=parameter.basis_value,
                balancing=parameter.balancing,
                bound_type=parameter.bound_type,
                lower_bound=parameter.lower_bound,
                upper_bound=parameter.upper_bound,
                toll_rate=parameter.toll_rate,
                airtime_rate=parameter.airtime_rate,
                fair_usage_rate=parameter.fair_usage_rate,
                fair_usage_threshold=parameter.fair_usage_threshold,
                access_fee_rate=parameter.access_fee_rate,
                incremental_rate=parameter.incremental_rate,
            )
            domain_parameters.append(from_orm_to_domain_discount_parameter(orm_param))

        return tuple(domain_parameters)

    def save(self, discount: Discount) -> Discount:
        """Persists Discount instance to storage."""

        orm_discount = self.get_orm_discount_by_id(discount.id)

        fields = [
            "direction",
            "service_types",
            "currency_code",
            "tax_type",
            "volume_type",
            "settlement_method",
            "call_destinations",
            "imsi_count_type",
            "parent_id",
            "above_commitment_rate",
            "inbound_market_share",
            "model_type",
        ]
        for field in fields:
            setattr(orm_discount, field, getattr(discount, field))

        orm_discount.start_date = discount.period.start_date
        orm_discount.end_date = discount.period.end_date

        orm_discount.qualifying_direction = (
            discount.qualifying_rule.direction if discount.qualifying_rule is not None else None
        )
        orm_discount.qualifying_service_types = (
            discount.qualifying_rule.service_types if discount.qualifying_rule is not None else None
        )
        orm_discount.qualifying_basis = discount.qualifying_rule.basis if discount.qualifying_rule is not None else None
        orm_discount.qualifying_lower_bound = (
            discount.qualifying_rule.lower_bound  # type: ignore[assignment]
            if discount.qualifying_rule is not None
            else None
        )
        orm_discount.qualifying_upper_bound = (
            discount.qualifying_rule.upper_bound  # type: ignore[assignment]
            if discount.qualifying_rule is not None
            else None
        )

        json_commitment_distribution_parameters = None

        if discount.commitment_distribution_parameters is not None:
            json_commitment_distribution_parameters = CommitmentDistributionParameterJSONEncoder.encode_many(
                discount.commitment_distribution_parameters
            )

        orm_discount.commitment_distribution_parameters = json_commitment_distribution_parameters

        orm_discount.save(
            update_fields=[
                *fields,
                "start_date",
                "end_date",
                "qualifying_direction",
                "qualifying_service_types",
                "qualifying_basis",
                "qualifying_lower_bound",
                "qualifying_upper_bound",
                "commitment_distribution_parameters",
            ]
        )

        orm_discount.home_operators.set(discount.home_operators)
        orm_discount.partner_operators.set(discount.partner_operators)
        orm_discount.called_countries.set(discount.called_countries or [])
        orm_discount.traffic_segments.set(discount.traffic_segments or [])

        orm_discount.refresh_from_db()

        return from_orm_to_domain_discount(orm_discount, domain_parameters=discount.parameters)

    def set_parameters(
        self,
        discount_id: int,
        parameters_dtos: tuple[DiscountParameterDTO, ...],
    ) -> tuple[DiscountParameter, ...]:
        """Replaces discount parameters with new created from DTOs."""

        models.DiscountParameter.objects.filter(discount_id=discount_id).delete()

        domain_parameters = self.create_parameters(discount_id, parameters_dtos)

        return domain_parameters

    def delete_by_id(self, discount_id: int) -> None:
        """Performs delete action of provided Discount along with its Parameters."""

        models.Discount.objects.filter(pk=discount_id).delete()

    def has_intersection_within_agreement(self, discount: Discount) -> bool:
        qs = models.Discount.objects.filter(agreement_id=discount.agreement_id, parent__isnull=True)

        return self.has_intersection(discount, qs)

    def has_intersection_between_sub_discounts(self, discount: Discount) -> bool:
        """Returns boolean that marks whether discount is intersected with other parent discounts."""

        orm_discount = self.get_orm_discount_by_id(discount.id)

        for sb in discount.sub_discounts:
            has_intersection = self.has_intersection(sb, orm_discount.sub_discounts.all())

            if has_intersection is True:
                return True

        return False

    @classmethod
    def has_intersection(cls, discount: Discount, qs: QuerySet[models.Discount]) -> bool:
        qs = qs.filter(
            period_intersection_query(discount.period),
            home_operators__pk__in=discount.home_operators,
            partner_operators__pk__in=discount.partner_operators,
            service_types__overlap=[s.value for s in discount.service_types],
        ).exclude(pk=discount.id)

        if discount.direction != DiscountDirectionEnum.BIDIRECTIONAL:
            qs = qs.filter(direction__in=[discount.direction, DiscountDirectionEnum.BIDIRECTIONAL])

        if qs.exists() is False:
            return False

        if discount.call_destinations or discount.called_countries:
            qs = qs.annotate(called_countries_ids=ArrayAgg("called_countries", distinct=True, default=[]))

            both_empty_q = Q(
                Q(call_destinations__isnull=True, called_countries_ids=[None])
                | Q(call_destinations__len=0, called_countries_ids=[None])
            )

            if discount.call_destinations:
                qs = qs.filter(both_empty_q | Q(call_destinations__overlap=discount.call_destinations))

            elif discount.called_countries:
                qs = qs.filter(both_empty_q | Q(called_countries_ids__overlap=discount.called_countries))

        if discount.traffic_segments:
            qs = qs.annotate(traffic_segments_ids=ArrayAgg("traffic_segments", distinct=True, default=[]))
            qs = qs.filter(Q(traffic_segments_ids__overlap=discount.traffic_segments) | Q(traffic_segments_ids=[None]))

        if discount.qualifying_rule is not None:

            # Check on discounts wih empty qualifying rule
            is_exists = qs.filter(
                Q(qualifying_service_types__isnull=True) | Q(qualifying_service_types=[]),
                qualifying_direction__isnull=True,
                qualifying_basis__isnull=True,
                qualifying_lower_bound__isnull=True,
                qualifying_upper_bound__isnull=True,
            ).exists()

            if is_exists:
                return True

            if discount.qualifying_rule.direction != DiscountDirectionEnum.BIDIRECTIONAL:
                qs = qs.filter(
                    qualifying_direction__in=[discount.qualifying_rule.direction, DiscountDirectionEnum.BIDIRECTIONAL]
                )

            qs = qs.filter(
                qualifying_bounds_intersection_query(
                    discount.qualifying_rule.lower_bound, discount.qualifying_rule.upper_bound
                ),
                qualifying_service_types=[qs.value for qs in discount.qualifying_rule.service_types],
                qualifying_basis=discount.qualifying_rule.basis,
            )

        intersection_exists = qs.exists()

        return intersection_exists


def from_orm_to_domain_discount(
    orm_discount: models.Discount,
    domain_parameters: Optional[tuple[DiscountParameter, ...]] = None,
) -> Discount:
    if not domain_parameters:
        orm_parameters = orm_discount.parameters.annotate(
            order_relevancy=Case(
                When(calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_TRAFFIC.value, then=0),
                default=1,
                output_field=IntegerField(),
            )
        ).order_by("order_relevancy", "balancing", "lower_bound")

        domain_parameters = tuple(from_orm_to_domain_discount_parameter(p) for p in orm_parameters)

    else:
        domain_parameters = tuple(
            sorted(
                domain_parameters,
                key=lambda p: (p.balancing is None, p.balancing, p.lower_bound is None, p.lower_bound),
            )
        )

    if (
        orm_discount.qualifying_direction is not None
        and orm_discount.qualifying_service_types is not None
        and orm_discount.qualifying_basis is not None
        and orm_discount.qualifying_lower_bound is not None
    ):

        qualifying_rule = DiscountQualifyingRule(
            direction=DiscountDirectionEnum(orm_discount.qualifying_direction),
            service_types=tuple(ServiceTypeEnum(s) for s in orm_discount.qualifying_service_types),
            basis=DiscountQualifyingBasisEnum(orm_discount.qualifying_basis),
            volume_type=VolumeTypeEnum(orm_discount.volume_type),
            lower_bound=orm_discount.qualifying_lower_bound,
            upper_bound=orm_discount.qualifying_upper_bound,
        )
    else:
        qualifying_rule = None

    commitment_distribution_parameters = None

    if orm_discount.commitment_distribution_parameters is not None:
        commitment_distribution_parameters = tuple(
            CommitmentDistributionParameter(
                home_operators=tuple(p["home_operators"]),
                partner_operators=tuple(p["partner_operators"]),
                charge=Decimal(p["charge"]),
            )
            for p in json.loads(orm_discount.commitment_distribution_parameters)
        )

    discount = Discount(
        id=orm_discount.pk,
        agreement_id=orm_discount.agreement_id,
        home_operators=tuple(orm_discount.home_operators.values_list("pk", flat=True)),
        partner_operators=tuple(orm_discount.partner_operators.values_list("pk", flat=True)),
        direction=DiscountDirectionEnum(orm_discount.direction),
        service_types=tuple(ServiceTypeEnum(s) for s in orm_discount.service_types),
        period=DatePeriod(orm_discount.start_date, orm_discount.end_date),
        model_type=DiscountModelTypeEnum(orm_discount.model_type) if orm_discount.model_type else None,
        currency_code=orm_discount.currency_code,
        tax_type=TaxTypeEnum(orm_discount.tax_type),
        volume_type=VolumeTypeEnum(orm_discount.volume_type),
        settlement_method=DiscountSettlementMethodEnum(orm_discount.settlement_method),
        call_destinations=(
            tuple(CallDestinationEnum(cd) for cd in orm_discount.call_destinations)
            if orm_discount.call_destinations
            else None
        ),
        qualifying_rule=qualifying_rule,
        called_countries=tuple(orm_discount.called_countries.values_list("pk", flat=True)) or None,
        traffic_segments=tuple(orm_discount.traffic_segments.values_list("pk", flat=True)) or None,
        parent_id=orm_discount.parent_id,
        parameters=domain_parameters,
        sub_discounts=tuple(from_orm_to_domain_discount(sb) for sb in orm_discount.sub_discounts.all()),
        imsi_count_type=IMSICountTypeEnum(orm_discount.imsi_count_type) if orm_discount.imsi_count_type else None,
        include_premium=orm_discount.agreement.include_premium,
        include_premium_in_commitment=orm_discount.agreement.include_premium_in_commitment,
        financial_threshold=orm_discount.financial_threshold,
        above_financial_threshold_rate=orm_discount.above_financial_threshold_rate,
        above_commitment_rate=orm_discount.above_commitment_rate,
        inbound_market_share=orm_discount.inbound_market_share,
        commitment_distribution_parameters=commitment_distribution_parameters,
    )

    return discount


def from_orm_to_domain_discount_parameter(orm_param: models.DiscountParameter) -> DiscountParameter:
    discount_parameter = DiscountParameter(
        discount_id=orm_param.discount_id,
        calculation_type=DiscountCalculationTypeEnum(orm_param.calculation_type),
        basis=DiscountBasisEnum(orm_param.basis) if orm_param.basis else None,
        basis_value=orm_param.basis_value,
        balancing=DiscountBalancingEnum(orm_param.balancing) if orm_param.balancing else None,
        bound_type=DiscountBoundTypeEnum(orm_param.bound_type) if orm_param.bound_type else None,
        lower_bound=orm_param.lower_bound,
        upper_bound=orm_param.upper_bound,
        toll_rate=orm_param.toll_rate,
        airtime_rate=orm_param.airtime_rate,
        fair_usage_rate=orm_param.fair_usage_rate,
        fair_usage_threshold=orm_param.fair_usage_threshold,
        access_fee_rate=orm_param.access_fee_rate,
        incremental_rate=orm_param.incremental_rate,
    )

    return discount_parameter
