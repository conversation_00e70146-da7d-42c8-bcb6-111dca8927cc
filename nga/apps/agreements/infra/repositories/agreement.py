from nga.apps.agreements.domain.exceptions import AgreementDoesNotExist
from nga.apps.agreements.domain.models import Agreement
from nga.apps.agreements.domain.repositories import AbstractAgreementRepository
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.agreements.infra.orm import models
from nga.apps.common.queryset_utils import to_pk_list
from nga.core.types import DatePeriod


class AgreementDjangoORMRepository(AbstractAgreementRepository):
    def get_by_id(self, agreement_id: int) -> Agreement:
        try:
            orm_agreement = models.Agreement.objects.get(pk=agreement_id)
        except models.Agreement.DoesNotExist:
            raise AgreementDoesNotExist(agreement_id)

        return from_orm_to_domain(orm_agreement)


def from_orm_to_domain(orm_agreement: models.Agreement) -> Agreement:
    return Agreement(
        id=orm_agreement.id,
        external_id=orm_agreement.external_id,
        parent_id=orm_agreement.parent_id,
        name=orm_agreement.name,
        status=AgreementStatusEnum(orm_agreement.status),
        home_operators=to_pk_list(orm_agreement.home_operators.all()),
        partner_operators=to_pk_list(orm_agreement.partner_operators.all()),
        period=DatePeriod(orm_agreement.start_date, orm_agreement.end_date),
        updated_at=orm_agreement.updated_at,
        negotiator_id=orm_agreement.negotiator_id,
        include_satellite=orm_agreement.include_satellite,
        include_premium=orm_agreement.include_premium,
        include_premium_in_commitment=orm_agreement.include_premium_in_commitment,
        is_rolling=orm_agreement.is_rolling,
    )
