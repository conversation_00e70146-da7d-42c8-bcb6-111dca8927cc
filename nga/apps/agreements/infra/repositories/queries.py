from decimal import Decimal
from typing import Optional

from django.db.models import Q


def qualifying_bounds_intersection_query(lower_bound: Decimal, upper_bound: Optional[Decimal]) -> Q:
    if upper_bound is None:

        return Q(qualifying_upper_bound__isnull=True) | Q(qualifying_upper_bound__gt=lower_bound)

    else:
        in_middle = Q(qualifying_lower_bound__lte=lower_bound) & (
            Q(qualifying_upper_bound__gte=upper_bound) | Q(qualifying_upper_bound__isnull=True)
        )

        intersects_from_right = Q(qualifying_upper_bound__gt=lower_bound, qualifying_upper_bound__lte=upper_bound) | Q(
            qualifying_lower_bound__lt=upper_bound, qualifying_upper_bound__isnull=True
        )

        intersects_from_left = Q(qualifying_lower_bound__gte=lower_bound, qualifying_lower_bound__lt=upper_bound)

        over_full_period = Q(qualifying_lower_bound__gte=lower_bound) & Q(qualifying_upper_bound__lte=upper_bound)

        return in_middle | intersects_from_left | intersects_from_right | over_full_period
