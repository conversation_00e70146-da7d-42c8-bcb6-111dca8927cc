from typing import Optional

from nga.apps.agreements.domain.exceptions import AgreementNegotiatorDoesNotExist
from nga.apps.agreements.domain.models import AgreementNegotiator
from nga.apps.agreements.domain.repositories import AbstractAgreementNegotiatorRepository
from nga.apps.agreements.infra.orm import models


class AgreementNegotiatorDjangoORMRepository(AbstractAgreementNegotiatorRepository):
    def get_many(self, negotiator_ids: Optional[list[int]] = None) -> list[AgreementNegotiator]:
        queryset = models.AgreementNegotiator.objects.order_by("pk").all()

        if negotiator_ids is not None:
            queryset = queryset.filter(id__in=negotiator_ids)

        return list(map(from_orm_to_domain, queryset))

    def get_by_id(self, agreement_negotiator_id: int) -> AgreementNegotiator:
        try:
            orm_agreement_negotiator = models.AgreementNegotiator.objects.get(pk=agreement_negotiator_id)
        except models.AgreementNegotiator.DoesNotExist:
            raise AgreementNegotiatorDoesNotExist(agreement_negotiator_id)

        return from_orm_to_domain(orm_agreement_negotiator)


def from_orm_to_domain(orm_negotiator: models.AgreementNegotiator) -> AgreementNegotiator:
    return AgreementNegotiator(
        id=orm_negotiator.id,
        name=orm_negotiator.name,
    )
