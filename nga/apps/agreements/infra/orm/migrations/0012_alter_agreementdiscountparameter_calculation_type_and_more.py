# Generated by Django 4.2 on 2023-11-16 22:40

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agreements", "0011_remove_agreement_applied_at_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="agreementdiscountparameter",
            name="calculation_type",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, "SINGLE_RATE_EFFECTIVE"),
                    (2, "ALL_YOU_CAN_EAT"),
                    (3, "BACK_TO_FIRST"),
                    (4, "CONTRIBUTION_TO_GROUP_SHORTFALL"),
                    (5, "SEND_OR_PAY_FINANCIAL"),
                    (6, "SEND_OR_PAY_TRAFFIC"),
                    (7, "STEPPED_TIERED"),
                    (8, "ALL_YOU_CAN_EAT_MONTHLY"),
                    (9, "STEPPED_TIERED_MONTHLY"),
                    (10, "PER_MONTH_PER_IMSI_ABOVE_THRESHOLD"),
                    (11, "PER_MONTH_PER_IMSI_STEPPED_OR_TIERED"),
                    (12, "PER_MONTH_PER_IMSI"),
                    (13, "PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING"),
                    (14, "ALL_YOU_CAN_EAT_DAILY"),
                    (15, "PER_DAY_PER_IMSI_ALL_YOU_CAN_EAT"),
                    (16, "PER_DAY_PER_IMSI_CAPPED_CHARGE"),
                    (17, "PER_DAY_PER_IMSI_CAPPED_CHARGE_HIGHEST"),
                ],
                verbose_name="Discount Calculation Type",
            ),
        ),
        migrations.AlterField(
            model_name="discountparameter",
            name="calculation_type",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, "SINGLE_RATE_EFFECTIVE"),
                    (2, "ALL_YOU_CAN_EAT"),
                    (3, "BACK_TO_FIRST"),
                    (4, "CONTRIBUTION_TO_GROUP_SHORTFALL"),
                    (5, "SEND_OR_PAY_FINANCIAL"),
                    (6, "SEND_OR_PAY_TRAFFIC"),
                    (7, "STEPPED_TIERED"),
                    (8, "ALL_YOU_CAN_EAT_MONTHLY"),
                    (9, "STEPPED_TIERED_MONTHLY"),
                    (10, "PER_MONTH_PER_IMSI_ABOVE_THRESHOLD"),
                    (11, "PER_MONTH_PER_IMSI_STEPPED_OR_TIERED"),
                    (12, "PER_MONTH_PER_IMSI"),
                    (13, "PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING"),
                    (14, "ALL_YOU_CAN_EAT_DAILY"),
                    (15, "PER_DAY_PER_IMSI_ALL_YOU_CAN_EAT"),
                    (16, "PER_DAY_PER_IMSI_CAPPED_CHARGE"),
                    (17, "PER_DAY_PER_IMSI_CAPPED_CHARGE_HIGHEST"),
                ],
                verbose_name="Calculation Type",
            ),
        ),
    ]
