# Generated by Django 4.2 on 2024-04-03 09:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0022_externalagreement'),
    ]

    operations = [
        migrations.AddField(
            model_name='externalagreement',
            name='processed_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Processed date'),
        ),
        migrations.AddField(
            model_name='externalagreement',
            name='processed_status',
            field=models.PositiveSmallIntegerField(choices=[(1, 'NOT_PROCESSED'), (2, 'NEW_CREATED'), (3, 'MOVED_TO_LIVE'), (4, 'SKIPPED'), (5, 'INTERSECTED')], default=1, verbose_name='Processed Status'),
        ),
    ]
