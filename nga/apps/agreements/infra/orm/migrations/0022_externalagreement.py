# Generated by Django 4.2 on 2024-03-31 19:02

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0021_alter_agreement_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExternalAgreement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=248, verbose_name='Agreement Reference')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('home_operators', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=5), size=None)),
                ('partner_operators', django.contrib.postgres.fields.ArrayField(base_field=models.Char<PERSON><PERSON>(max_length=5), size=None)),
                ('discounts', models.J<PERSON><PERSON>ield(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Creation date')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last updated date')),
            ],
            options={
                'db_table': 'external_agreements',
            },
        ),
    ]
