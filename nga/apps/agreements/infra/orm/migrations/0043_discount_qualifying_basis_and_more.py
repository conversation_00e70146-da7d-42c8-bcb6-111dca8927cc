# Generated by Django 4.2 on 2024-11-25 13:48

from django.db import migrations, models
import nga.apps.agreements.infra.orm.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0042_remove_discountparameter_qualifying_basis_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='discount',
            name='qualifying_basis',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VOLUME'), (2, 'MARKET_SHARE_PERCENTAGE'), (3, 'YOY_TRAFFIC_GROWTH_PERCENTAGE'), (4, 'UNIQUE_IMSI_COUNT_PER_MONTH'), (5, 'AVERAGE_MONTHLY_USAGE_PER_IMSI')], null=True, verbose_name='Qualifying Basis'),
        ),
        migrations.AddField(
            model_name='discount',
            name='qualifying_lower_bound',
            field=nga.apps.agreements.infra.orm.models.fields.BoundDecimalField(blank=True, decimal_places=2, max_digits=18, null=True, verbose_name='Qualifying Lower Bound'),
        ),
        migrations.AddField(
            model_name='discount',
            name='qualifying_upper_bound',
            field=nga.apps.agreements.infra.orm.models.fields.BoundDecimalField(blank=True, decimal_places=2, max_digits=18, null=True, verbose_name='Qualifying Upper Bound'),
        ),
    ]
