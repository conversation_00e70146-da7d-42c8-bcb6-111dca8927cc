# Generated by Django 4.2 on 2024-07-31 11:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0030_alter_discountparameter_calculation_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='discount',
            name='model_type',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'SINGLE_RATE_EFFECTIVE'), (2, 'STEPPED_TIERED'), (3, 'SEND_OR_PAY_TRAFFIC_SINGLE_RATE_EFFECTIVE'), (4, 'SEND_OR_PAY_TRAFFIC_STEPPED_TIERED'), (5, 'SEND_OR_PAY_FINANCIAL'), (6, 'BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE'), (7, 'BALANCED_UNBALANCED_STEPPED_TIERED'), (8, 'BACK_TO_FIRST'), (9, 'PER_MONTH_PER_IMSI'), (10, 'PER_MONTH_PER_IMSI_ABOVE_THRESHOLD'), (11, 'PER_MONTH_PER_IMSI_STEPPED_TIERED'), (12, 'PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING'), (13, 'PER_MONTH_PER_IMSI_BACK_TO_FIRST')], null=True, verbose_name='Model Type'),
        ),
    ]
