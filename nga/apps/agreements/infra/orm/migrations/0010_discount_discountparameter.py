# Generated by Django 4.2 on 2023-10-12 14:20

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import nga.apps.agreements.infra.orm.models


class Migration(migrations.Migration):

    dependencies = [
        ('references', '0005_alter_homeoperator_operator'),
        ('agreements', '0009_alter_agreement_table_alter_budgetagreement_table'),
    ]

    operations = [
        migrations.CreateModel(
            name='Discount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('direction', models.PositiveSmallIntegerField(choices=[(1, 'INBOUND'), (2, 'OUTBOUND'), (3, 'BIDIRECTIONAL')], verbose_name='Direction')),
                ('service_types', django.contrib.postgres.fields.ArrayField(base_field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA'), (6, 'VOLTE'), (7, 'ACCESS_FEE')]), size=7)),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('currency_code', models.CharField(db_index=True, max_length=3, verbose_name='Currency Code')),
                ('tax_type', models.PositiveSmallIntegerField(choices=[(1, 'NET'), (2, 'GROSS')], verbose_name='Tax Type')),
                ('volume_type', models.PositiveSmallIntegerField(choices=[(1, 'ACTUAL'), (2, 'BILLED')], verbose_name='Volume Type')),
                ('settlement_method', models.PositiveSmallIntegerField(choices=[(1, 'CREDIT_NOTE_EOA'), (2, 'TAP_LVL_DCH'), (3, 'CREDIT_NOTE_MONTHLY')], verbose_name='Settlement Method')),
                ('call_destinations', django.contrib.postgres.fields.ArrayField(base_field=models.PositiveSmallIntegerField(choices=[(1, 'HOME'), (2, 'LOCAL'), (3, 'INTERNATIONAL')]), blank=True, null=True, size=3)),
                ('qualifying_direction', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'INBOUND'), (2, 'OUTBOUND'), (3, 'BIDIRECTIONAL')], null=True, verbose_name='Qualifying Direction')),
                ('qualifying_service_types', django.contrib.postgres.fields.ArrayField(base_field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA'), (6, 'VOLTE'), (7, 'ACCESS_FEE')]), blank=True, null=True, size=7)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Creation date')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last updated date')),
                ('agreement', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='discounts', related_query_name='discount', to='agreements.agreement')),
                ('called_countries', models.ManyToManyField(blank=True, to='references.country', verbose_name='Called Countries')),
                ('home_operators', models.ManyToManyField(related_name='home_agreement_discounts', related_query_name='home_agreement_discount', to='references.operator', verbose_name='Home Operators')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sub_discounts', related_query_name='sub_discount', to='agreements.discount')),
                ('partner_operators', models.ManyToManyField(related_name='partner_agreement_discounts', related_query_name='partner_agreement_discount', to='references.operator', verbose_name='Partner Operators')),
                ('traffic_segments', models.ManyToManyField(blank=True, to='references.trafficsegment', verbose_name='Traffic Segments')),
            ],
            options={
                'db_table': 'agreement_discounts',
            },
        ),
        migrations.CreateModel(
            name='DiscountParameter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('calculation_type', models.PositiveSmallIntegerField(choices=[(1, 'SINGLE_RATE_EFFECTIVE'), (2, 'ALL_YOU_CAN_EAT'), (3, 'BACK_TO_FIRST'), (4, 'CONTRIBUTION_TO_GROUP_SHORTFALL'), (5, 'SEND_OR_PAY_FINANCIAL'), (6, 'SEND_OR_PAY_TRAFFIC'), (7, 'STEPPED_TIERED'), (8, 'ALL_YOU_CAN_EAT_MONTHLY'), (9, 'STEPPED_TIERED_MONTHLY')], verbose_name='Calculation Type')),
                ('basis', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VALUE'), (2, 'PERCENTAGE'), (3, 'NET_TRAFFIC_SENDER_TAP_RATE'), (4, 'NET_TRAFFIC_RECEIVER_TAP_RATE'), (5, 'DEDUCTION'), (6, 'AVERAGE_IOT_RATE')], null=True, verbose_name='Basis')),
                ('basis_value', nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=6, max_digits=18, null=True, verbose_name='Basis Value')),
                ('balancing', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'BALANCED'), (2, 'BALANCED_WITH_GROUP_COMPENSATION'), (3, 'BALANCING_FOR_SOP_TRAFFIC'), (4, 'INCREMENTAL_TRAFFIC'), (5, 'NO_BALANCING'), (6, 'UNBALANCED'), (7, 'UNBALANCED_WITH_GROUP_COMPENSATION')], null=True, verbose_name='Balancing')),
                ('bound_type', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VOLUME'), (2, 'FINANCIAL'), (3, 'MARKET_SHARE'), (4, 'YOY_TRAFFIC_GROWTH')], null=True, verbose_name='Bound Type')),
                ('lower_bound', nga.apps.agreements.infra.orm.models.BoundDecimalField(blank=True, decimal_places=6, max_digits=18, null=True, verbose_name='Lower Bound')),
                ('upper_bound', nga.apps.agreements.infra.orm.models.BoundDecimalField(blank=True, decimal_places=6, max_digits=18, null=True, verbose_name='Upper Bound')),
                ('qualifying_basis', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VOLUME'), (2, 'MARKET_SHARE_PERCENTAGE'), (3, 'YOY_TRAFFIC_GROWTH_PERCENTAGE'), (4, 'UNIQUE_IMSI_COUNT_PER_MONTH'), (5, 'AVERAGE_MONTHLY_USAGE_PER_IMSI')], null=True, verbose_name='Qualifying Basis')),
                ('qualifying_basis_value', nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=6, max_digits=18, null=True, verbose_name='Qualifying Basis Value')),
                ('toll_rate', nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=6, max_digits=18, null=True, verbose_name='Toll Rate')),
                ('airtime_rate', nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=6, max_digits=18, null=True, verbose_name='Airtime Rate')),
                ('fair_usage_rate', nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=6, max_digits=18, null=True, verbose_name='Fair Usage Rate')),
                ('fair_usage_threshold', nga.apps.agreements.infra.orm.models.BoundDecimalField(blank=True, decimal_places=6, max_digits=18, null=True, verbose_name='Fair Usage Threshold')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Creation date')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last updated date')),
                ('discount', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parameters', related_query_name='parameter', to='agreements.discount')),
            ],
            options={
                'db_table': 'agreement_discount_parameters',
            },
        ),
    ]
