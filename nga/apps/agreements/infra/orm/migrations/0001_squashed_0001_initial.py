# Generated by Django 4.2 on 2024-01-18 14:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('budgets', '0012_budget_is_master_and_more'),
        ('references', '0001_initial_squashed'),
    ]

    operations = [
        migrations.CreateModel(
            name='Agreement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=248, verbose_name='Agreement Reference')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('status', models.PositiveSmallIntegerField(choices=[(1, 'DRAFT'), (2, 'LIVE')], db_index=True, default=1, verbose_name='Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Creation date')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last updated date')),
                ('applied_at', models.DateTimeField(blank=True, null=True, verbose_name='Applied date')),
            ],
        ),
        migrations.CreateModel(
            name='BudgetAgreement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=False, verbose_name='Active')),
                ('agreement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='agreements.agreement')),
                ('budget', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='budgets.budget')),
            ],
        ),
        migrations.AddField(
            model_name='agreement',
            name='budgets',
            field=models.ManyToManyField(related_name='agreements', related_query_name='agreement', through='agreements.BudgetAgreement', to='budgets.budget'),
        ),
        migrations.AddField(
            model_name='agreement',
            name='home_operators',
            field=models.ManyToManyField(related_name='home_agreements', related_query_name='home_agreement', to='references.operator', verbose_name='Home Operators'),
        ),
        migrations.AddField(
            model_name='agreement',
            name='partner_operators',
            field=models.ManyToManyField(related_name='partner_agreements', related_query_name='partner_agreement', to='references.operator', verbose_name='Partner Operators'),
        ),
    ]
