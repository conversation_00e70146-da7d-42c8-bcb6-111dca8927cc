# Generated by Django 4.2 on 2023-04-24 08:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0002_agreementdiscountparameter'),
    ]

    operations = [
        migrations.AlterField(
            model_name='agreementdiscountparameter',
            name='bound_type',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VOLUME'), (2, 'FINANCIAL'), (3, 'MARKET_SHARE'), (4, 'YOY_TRAFFIC_GROWTH')], null=True, verbose_name='Bound Type'),
        ),
        migrations.AlterField(
            model_name='agreementdiscountparameter',
            name='partner_operators',
            field=models.ManyToManyField(related_name='partner_discounts', related_query_name='partner_discount', to='references.operator', verbose_name='Partner Operators'),
        ),
        migrations.AlterField(
            model_name='agreementdiscountparameter',
            name='qualifying_basis',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VOLUME'), (2, 'MARKET_SHARE_PERCENTAGE'), (3, 'YOY_TRAFFIC_GROWTH_PERCENTAGE'), (4, 'UNIQUE_IMSI_COUNT_PER_MONTH'), (5, 'AVERAGE_MONTHLY_USAGE_PER_IMSI')], null=True, verbose_name='Qualifying Basis'),
        ),
    ]
