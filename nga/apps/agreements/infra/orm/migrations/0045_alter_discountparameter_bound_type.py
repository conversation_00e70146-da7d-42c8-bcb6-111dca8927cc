# Generated by Django 4.2 on 2024-12-03 09:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0044_alter_discount_qualifying_basis'),
    ]

    operations = [
        migrations.AlterField(
            model_name='discountparameter',
            name='bound_type',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VOLUME'), (2, 'FINANCIAL'), (3, 'MARKET_SHARE'), (5, 'UNIQUE_IMSI_COUNT_PER_MONTH'), (6, 'VOLUME_INCLUDED_IN_ACCESS_FEE')], null=True, verbose_name='Bound Type'),
        ),
    ]
