# Generated by Django 4.2 on 2024-07-08 13:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0028_discountparameter_access_fee_rate_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='discountparameter',
            name='bound_type',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VOLUME'), (2, 'FINANCIAL'), (3, 'MARKET_SHARE'), (4, 'YOY_TRAFFIC_GROWTH'), (5, 'UNIQUE_IMSI_COUNT_PER_MONTH'), (6, 'VOLUME_INCLUDED_IN_ACCESS_FEE')], null=True, verbose_name='Bound Type'),
        ),
    ]
