# Generated by Django 5.1.7 on 2025-06-04 11:54

import nga.apps.agreements.infra.orm.models.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0053_agreement_parent'),
    ]

    operations = [
        migrations.AddField(
            model_name='discount',
            name='above_financial_threshold_rate',
            field=nga.apps.agreements.infra.orm.models.fields.DiscountDecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Above Financial Threshold Rate'),
        ),
        migrations.AddField(
            model_name='discount',
            name='financial_threshold',
            field=nga.apps.agreements.infra.orm.models.fields.DiscountDecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Financial Threshold'),
        ),
        migrations.AlterField(
            model_name='discountparameter',
            name='bound_type',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VOLUME'), (2, 'FINANCIAL_COMMITMENT'), (3, 'MARKET_SHARE'), (5, 'UNIQUE_IMSI_COUNT_PER_MONTH'), (6, 'VOLUME_INCLUDED_IN_ACCESS_FEE'), (7, 'FINANCIAL_THRESHOLD')], null=True, verbose_name='Bound Type'),
        ),
        migrations.AlterField(
            model_name='discountparameter',
            name='calculation_type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'SINGLE_RATE_EFFECTIVE'), (2, 'ALL_YOU_CAN_EAT'), (3, 'BACK_TO_FIRST'), (4, 'CONTRIBUTION_TO_GROUP_SHORTFALL'), (5, 'SEND_OR_PAY_FINANCIAL'), (6, 'SEND_OR_PAY_TRAFFIC'), (7, 'STEPPED_TIERED'), (8, 'ALL_YOU_CAN_EAT_MONTHLY'), (9, 'STEPPED_TIERED_MONTHLY'), (10, 'PER_MONTH_PER_IMSI_ABOVE_THRESHOLD'), (11, 'PER_MONTH_PER_IMSI_STEPPED_TIERED'), (12, 'PER_MONTH_PER_IMSI'), (13, 'PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING'), (14, 'ALL_YOU_CAN_EAT_DAILY'), (15, 'PER_DAY_PER_IMSI_ALL_YOU_CAN_EAT'), (16, 'PER_DAY_PER_IMSI_CAPPED_CHARGE'), (17, 'PER_DAY_PER_IMSI_CAPPED_CHARGE_HIGHEST'), (18, 'PER_MONTH_PER_IMSI_BACK_TO_FIRST'), (19, 'FINANCIAL_THRESHOLD')], verbose_name='Calculation Type'),
        ),
    ]
