# Generated by Django 4.2 on 2023-08-25 19:54

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0007_agreementdiscountparameter_traffic_segments'),
    ]

    operations = [
        migrations.AlterField(
            model_name='agreementdiscountparameter',
            name='qualifying_service_types',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA'), (6, 'VOLTE'), (7, 'ACCESS_FEE')]), blank=True, null=True, size=7),
        ),
        migrations.AlterField(
            model_name='agreementdiscountparameter',
            name='service_types',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA'), (6, 'VOLTE'), (7, 'ACCESS_FEE')]), size=7),
        ),
    ]
