# Generated by Django 4.2 on 2024-07-08 11:47

from django.db import migrations
import nga.apps.agreements.infra.orm.models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0027_discount_imsi_count_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='discountparameter',
            name='access_fee_rate',
            field=nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Access Fee Rate'),
        ),
        migrations.AddField(
            model_name='discountparameter',
            name='incremental_rate',
            field=nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Incremental Rate'),
        ),
    ]
