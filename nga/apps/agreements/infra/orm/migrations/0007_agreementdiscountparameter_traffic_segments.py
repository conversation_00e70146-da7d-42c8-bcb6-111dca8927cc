# Generated by Django 4.2 on 2023-08-03 10:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('references', '0005_alter_homeoperator_operator'),
        ('agreements', '0006_alter_agreementdiscountparameter_qualifying_service_types_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='agreementdiscountparameter',
            name='traffic_segments',
            field=models.ManyToManyField(blank=True, related_name='segment_discounts', related_query_name='segment_discount', to='references.trafficsegment', verbose_name='Traffic Segments'),
        ),
    ]
