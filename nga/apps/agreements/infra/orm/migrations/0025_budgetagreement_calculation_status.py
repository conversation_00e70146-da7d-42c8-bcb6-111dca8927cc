# Generated by Django 4.2 on 2024-06-18 08:05

from django.db import migrations, models
import nga.apps.agreements.enums


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0024_externalagreement_move_table_to_references'),
    ]

    operations = [
        migrations.AddField(
            model_name='budgetagreement',
            name='calculation_status',
            field=models.PositiveSmallIntegerField(choices=[(1, 'NOT_APPLIED'), (2, 'APPLIED'), (3, 'FAILED'), (4, 'OUTDATED')], default=nga.apps.agreements.enums.AgreementCalculationStatusEnum['NOT_APPLIED']),
        ),
    ]
