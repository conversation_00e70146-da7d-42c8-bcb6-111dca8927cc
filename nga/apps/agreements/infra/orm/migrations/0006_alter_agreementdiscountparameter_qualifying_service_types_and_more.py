# Generated by Django 4.2.3 on 2023-08-02 14:36

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("agreements", "0005_alter_agreement_created_at_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="agreementdiscountparameter",
            name="qualifying_service_types",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.PositiveSmallIntegerField(
                    choices=[(1, "VOICE_MO"), (2, "VOICE_MT"), (3, "SMS_MO"), (4, "SMS_MT"), (5, "DATA"), (6, "VOLTE")]
                ),
                blank=True,
                null=True,
                size=6,
            ),
        ),
        migrations.AlterField(
            model_name="agreementdiscountparameter",
            name="service_types",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.PositiveSmallIntegerField(
                    choices=[(1, "VOICE_MO"), (2, "VOICE_MT"), (3, "SMS_MO"), (4, "SMS_MT"), (5, "DATA"), (6, "VOLTE")]
                ),
                size=6,
            ),
        ),
    ]
