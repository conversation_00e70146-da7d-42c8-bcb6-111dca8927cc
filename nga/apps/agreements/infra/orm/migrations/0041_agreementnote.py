# Generated by Django 4.2 on 2024-10-22 15:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('agreements', '0040_alter_agreement_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='AgreementNote',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('type', models.PositiveSmallIntegerField(choices=[(1, 'SYSTEM'), (2, 'USER')], verbose_name='Note Type')),
                ('content', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Creation date')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last updated date')),
                ('agreement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', related_query_name='note', to='agreements.agreement')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='agreement_notes', related_query_name='agreement_note', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'agreement_notes',
            },
        ),
    ]
