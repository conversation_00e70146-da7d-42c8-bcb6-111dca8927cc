# Generated by Django 5.1.7 on 2025-04-15 11:02
from django.db import migrations
from django.contrib.postgres.operations import RemoveCollation


class RemoveCollationIfExists(RemoveCollation):
    def remove_collation(self, schema_editor):
        schema_editor.execute(
            "DROP COLLATION IF EXISTS %s" % schema_editor.quote_name(self.name),
        )


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0050_alter_agreementnegotiator_name'),
    ]

    operations = [
        RemoveCollationIfExists(
            'case_insensitive',
            provider='icu',
            locale='und-u-ks-level2',
            deterministic=False
        )
    ]
