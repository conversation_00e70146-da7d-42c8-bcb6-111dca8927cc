# Generated by Django 4.2 on 2023-04-21 12:58

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0001_squashed_0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AgreementDiscountParameter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('settlement_method', models.PositiveSmallIntegerField(choices=[(1, 'CREDIT_NOTE_EOA'), (2, 'TAP_LVL_DCH'), (3, 'CREDIT_NOTE_MONTHLY')], verbose_name='Discount Settlement Method')),
                ('direction', models.PositiveSmallIntegerField(choices=[(1, 'INBOUND'), (2, 'OUTBOUND'), (3, 'BIDIRECTIONAL')], verbose_name='Discount Direction')),
                ('service_types', django.contrib.postgres.fields.ArrayField(base_field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA')]), size=5)),
                ('call_destinations', django.contrib.postgres.fields.ArrayField(base_field=models.PositiveSmallIntegerField(choices=[(1, 'HOME'), (2, 'LOCAL'), (3, 'INTERNATIONAL')]), size=3)),
                ('volume_type', models.PositiveSmallIntegerField(choices=[(1, 'ACTUAL'), (2, 'BILLED')], verbose_name='Discount Volume Type')),
                ('calculation_type', models.PositiveSmallIntegerField(choices=[(1, 'SINGLE_RATE_EFFECTIVE'), (2, 'ALL_YOU_CAN_EAT'), (3, 'BACK_TO_FIRST'), (4, 'CONTRIBUTION_TO_GROUP_SHORTFALL'), (5, 'SEND_OR_PAY_FINANCIAL'), (6, 'SEND_OR_PAY_TRAFFIC'), (7, 'STEPPED_TIERED'), (8, 'ALL_YOU_CAN_EAT_MONTHLY'), (9, 'STEPPED_TIERED_MONTHLY')], verbose_name='Discount Calculation Type')),
                ('basis', models.PositiveSmallIntegerField(choices=[(1, 'VALUE'), (2, 'PERCENTAGE'), (3, 'NET_TRAFFIC_SENDER_TAP_RATE'), (4, 'NET_TRAFFIC_RECEIVER_TAP_RATE'), (5, 'DEDUCTION'), (6, 'AVERAGE_IOT_RATE')], verbose_name='Discount Basis')),
                ('basis_value', models.DecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Discount Basis Value')),
                ('currency_code', models.CharField(max_length=3, verbose_name='Discount Currency')),
                ('incl_tax', models.BooleanField(verbose_name='Incl. Tax')),
                ('balancing', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'BALANCED'), (2, 'BALANCED_WITH_GROUP_COMPENSATION'), (3, 'BALANCING_FOR_SOP_TRAFFIC'), (4, 'INCREMENTAL_TRAFFIC'), (5, 'NO_BALANCING'), (6, 'UNBALANCED'), (7, 'UNBALANCED_WITH_GROUP_COMPENSATION')], null=True, verbose_name='Balancing')),
                ('bound_type', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VOLUME'), (2, 'FINANCIAL'), (3, 'MARKET_SHARE'), (4, 'YOY_TRAFFIC_GROWTH'), (5, 'UNIQUE_IMSI_COUNT_PER_MONTH'), (6, 'AVERAGE_MONTHLY_USAGE_PER_IMSI')], null=True, verbose_name='Bound Type')),
                ('lower_bound', models.DecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Lower Bound')),
                ('upper_bound', models.DecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Upper Bound')),
                ('valid_from', models.DateField(blank=True, null=True, verbose_name='Valid From')),
                ('valid_to', models.DateField(blank=True, null=True, verbose_name='Valid From')),
                ('qualifying_direction', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'INBOUND'), (2, 'OUTBOUND'), (3, 'BIDIRECTIONAL')], null=True, verbose_name='Qualifying Direction')),
                ('qualifying_service_types', django.contrib.postgres.fields.ArrayField(base_field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA')]), blank=True, null=True, size=5)),
                ('qualifying_basis', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'VALUE'), (2, 'PERCENTAGE'), (3, 'NET_TRAFFIC_SENDER_TAP_RATE'), (4, 'NET_TRAFFIC_RECEIVER_TAP_RATE'), (5, 'DEDUCTION'), (6, 'AVERAGE_IOT_RATE')], null=True, verbose_name='Qualifying Basis')),
                ('qualifying_value', models.DecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Qualifying Value')),
                ('toll_rate', models.DecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Toll Rate')),
                ('airtime_rate', models.DecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Airtime Rate')),
                ('fair_usage_treshold', models.DecimalField(blank=True, decimal_places=2, max_digits=18, null=True, verbose_name='Fair Usage Treshold')),
                ('fair_usage_rate', models.DecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Fair Usage Rate')),
                ('created_at', models.DateTimeField(verbose_name='Creation date')),
                ('updated_at', models.DateTimeField(verbose_name='Last updated date')),
                ('agreement', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='discount_parameters', related_query_name='discount_parameter', to='agreements.agreement')),
                ('called_countries', models.ManyToManyField(related_name='called_discounts', related_query_name='called_discount', to='references.country', verbose_name='Called Countries')),
                ('home_operators', models.ManyToManyField(related_name='home_discounts', related_query_name='home_discount', to='references.operator', verbose_name='Home Operators')),
                ('partner_operators', models.ManyToManyField(related_name='partner_discounts', related_query_name='partner_discount', to='references.operator', verbose_name='Home Operators')),
            ],
        ),
    ]
