# Generated by Django 4.2 on 2023-11-24 11:02

from django.db import migrations
import nga.apps.agreements.infra.orm.models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0012_alter_agreementdiscountparameter_calculation_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='discountparameter',
            name='airtime_rate',
            field=nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Airtime Rate'),
        ),
        migrations.AlterField(
            model_name='discountparameter',
            name='basis_value',
            field=nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Basis Value'),
        ),
        migrations.AlterField(
            model_name='discountparameter',
            name='fair_usage_rate',
            field=nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Fair Usage Rate'),
        ),
        migrations.AlterField(
            model_name='discountparameter',
            name='fair_usage_threshold',
            field=nga.apps.agreements.infra.orm.models.BoundDecimalField(blank=True, decimal_places=2, max_digits=18, null=True, verbose_name='Fair Usage Threshold'),
        ),
        migrations.AlterField(
            model_name='discountparameter',
            name='lower_bound',
            field=nga.apps.agreements.infra.orm.models.BoundDecimalField(blank=True, decimal_places=2, max_digits=18, null=True, verbose_name='Lower Bound'),
        ),
        migrations.AlterField(
            model_name='discountparameter',
            name='qualifying_basis_value',
            field=nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Qualifying Basis Value'),
        ),
        migrations.AlterField(
            model_name='discountparameter',
            name='toll_rate',
            field=nga.apps.agreements.infra.orm.models.DiscountDecimalField(blank=True, decimal_places=10, max_digits=16, null=True, verbose_name='Toll Rate'),
        ),
        migrations.AlterField(
            model_name='discountparameter',
            name='upper_bound',
            field=nga.apps.agreements.infra.orm.models.BoundDecimalField(blank=True, decimal_places=2, max_digits=18, null=True, verbose_name='Upper Bound'),
        ),
    ]
