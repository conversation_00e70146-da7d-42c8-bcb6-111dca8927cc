# Generated by Django 4.2 on 2024-01-30 18:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0017_create_citext_extension_and_case_insensitive_collation'),
    ]

    operations = [
        migrations.CreateModel(
            name='AgreementNegotiator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_collation='case_insensitive', max_length=50, unique=True, verbose_name='Full Name')),
            ],
            options={
                'db_table': 'agreement_negotiators',
            },
        ),
    ]
