from django.db import models
from django.utils.translation import gettext_lazy as _

from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
)
from nga.apps.agreements.infra.orm.models.fields import BoundDecimalField, DiscountDecimalField
from nga.core.enums import get_choices


class DiscountParameter(models.Model):
    discount = models.ForeignKey(
        "Discount",
        on_delete=models.CASCADE,
        related_name="parameters",
        related_query_name="parameter",
    )

    calculation_type = models.PositiveSmallIntegerField(
        _("Calculation Type"),
        choices=get_choices(DiscountCalculationTypeEnum),
    )

    basis = models.PositiveSmallIntegerField(_("Basis"), choices=get_choices(DiscountBasisEnum), null=True, blank=True)

    basis_value = DiscountDecimalField(_("Basis Value"), null=True, blank=True)

    balancing = models.PositiveSmallIntegerField(
        _("Balancing"),
        choices=get_choices(DiscountBalancingEnum),
        null=True,
        blank=True,
    )

    bound_type = models.PositiveSmallIntegerField(
        _("Bound Type"),
        choices=get_choices(DiscountBoundTypeEnum),
        null=True,
        blank=True,
    )

    lower_bound = BoundDecimalField(_("Lower Bound"), null=True, blank=True)

    upper_bound = BoundDecimalField(_("Upper Bound"), null=True, blank=True)

    toll_rate = DiscountDecimalField(_("Toll Rate"), null=True, blank=True)

    airtime_rate = DiscountDecimalField(_("Airtime Rate"), null=True, blank=True)

    fair_usage_rate = DiscountDecimalField(_("Fair Usage Rate"), null=True, blank=True)

    fair_usage_threshold = BoundDecimalField(_("Fair Usage Threshold"), null=True, blank=True)

    access_fee_rate = DiscountDecimalField(_("Access Fee Rate"), null=True, blank=True)

    incremental_rate = DiscountDecimalField(_("Incremental Rate"), null=True, blank=True)

    created_at = models.DateTimeField(_("Creation date"), auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(_("Last updated date"), auto_now=True)

    class Meta:
        db_table = "agreement_discount_parameters"

    def __str__(self) -> str:
        return f"DiscountParameter #{self.id} - Discount #{self.discount_id}"
