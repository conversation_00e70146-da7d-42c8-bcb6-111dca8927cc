from django.db import models
from django.utils.translation import gettext_lazy as _

from nga.apps.agreements.enums import AgreementCalculationStatusEnum
from nga.apps.agreements.infra.orm.managers import BudgetAgreementQuerySet
from nga.core.enums import get_choices


class BudgetAgreement(models.Model):
    budget = models.ForeignKey("budgets.Budget", on_delete=models.CASCADE)

    agreement = models.ForeignKey("Agreement", on_delete=models.CASCADE)

    is_active = models.BooleanField("Active", default=False)

    applied_at = models.DateTimeField(_("Applied date"), null=True, blank=True)

    calculation_status = models.PositiveSmallIntegerField(
        choices=get_choices(AgreementCalculationStatusEnum),
        default=AgreementCalculationStatusEnum.NOT_APPLIED,
    )

    objects = BudgetAgreementQuerySet.as_manager()

    class Meta:
        db_table = "budget_agreements"
