from django.db import models
from django.utils.translation import gettext_lazy as _

from nga.apps.agreements.enums import AgreementStatusEnum
from nga.core.enums import get_choices


class Agreement(models.Model):
    external_id = models.IntegerField(_("External ID"), null=True, blank=True)

    parent = models.OneToOneField(
        "Agreement",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    name = models.CharField(_("Agreement Reference"), max_length=248)

    home_operators = models.ManyToManyField(
        "references.Operator",
        verbose_name=_("Home Operators"),
        related_name="home_agreements",
        related_query_name="home_agreement",
    )

    partner_operators = models.ManyToManyField(
        "references.Operator",
        verbose_name=_("Partner Operators"),
        related_name="partner_agreements",
        related_query_name="partner_agreement",
    )

    start_date = models.DateField(_("Start Date"))

    end_date = models.DateField(_("End Date"))

    budgets = models.ManyToManyField(
        "budgets.Budget",
        through="BudgetAgreement",
        related_name="agreements",
        related_query_name="agreement",
    )

    status = models.PositiveSmallIntegerField(
        _("Status"),
        choices=get_choices(AgreementStatusEnum),
        db_index=True,
        default=AgreementStatusEnum.DRAFT.value,
    )

    negotiator = models.ForeignKey(
        "AgreementNegotiator",
        on_delete=models.CASCADE,
        related_name="negotiators",
        related_query_name="negotiator",
        null=True,
        blank=True,
    )

    include_satellite = models.BooleanField(_("Include satellite"), default=True)

    include_premium = models.BooleanField(_("Include premium"), default=True)

    include_premium_in_commitment = models.BooleanField(_("Include premium in commitment"), default=True)

    is_rolling = models.BooleanField(_("Is rolling"), default=True)

    created_at = models.DateTimeField(_("Creation date"))
    updated_at = models.DateTimeField(_("Last updated date"))

    class Meta:
        db_table = "agreements"

    def __str__(self) -> str:
        return f"#{self.id} - {self.name}"

    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id}, name={self.name}, status={self.status})>"
