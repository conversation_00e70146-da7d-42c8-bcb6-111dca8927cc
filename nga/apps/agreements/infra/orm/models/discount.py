from django.contrib.postgres.fields import Array<PERSON>ield
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils.translation import gettext_lazy as _

from nga.apps.agreements.enums import (
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.agreements.infra.orm.models.fields import (
    BoundDecimalField,
    DiscountDecimalField,
)
from nga.apps.common.model_field_utils import get_array_choices_display
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum, get_choices


class Discount(models.Model):
    agreement = models.ForeignKey(
        "Agreement",
        on_delete=models.CASCADE,
        related_name="discounts",
        related_query_name="discount",
    )

    parent = models.ForeignKey(
        "Discount",
        on_delete=models.CASCADE,
        related_name="sub_discounts",
        related_query_name="sub_discount",
        null=True,
        blank=True,
    )

    home_operators = models.ManyToManyField(
        "references.Operator",
        verbose_name=_("Home Operators"),
        related_name="home_agreement_discounts",
        related_query_name="home_agreement_discount",
    )

    partner_operators = models.ManyToManyField(
        "references.Operator",
        verbose_name=_("Partner Operators"),
        related_name="partner_agreement_discounts",
        related_query_name="partner_agreement_discount",
    )

    direction = models.PositiveSmallIntegerField(_("Direction"), choices=get_choices(DiscountDirectionEnum))

    service_types = ArrayField(
        models.PositiveSmallIntegerField(choices=get_choices(ServiceTypeEnum)),
        size=len(ServiceTypeEnum),
    )

    start_date = models.DateField(_("Start Date"))
    end_date = models.DateField(_("End Date"))

    currency_code = models.CharField(_("Currency Code"), max_length=3, db_index=True)

    tax_type = models.PositiveSmallIntegerField(_("Tax Type"), choices=get_choices(TaxTypeEnum))

    volume_type = models.PositiveSmallIntegerField(_("Volume Type"), choices=get_choices(VolumeTypeEnum))

    settlement_method = models.PositiveSmallIntegerField(
        _("Settlement Method"),
        choices=get_choices(DiscountSettlementMethodEnum),
    )

    call_destinations = ArrayField(
        models.PositiveSmallIntegerField(choices=get_choices(CallDestinationEnum)),
        size=len(CallDestinationEnum),
        null=True,
        blank=True,
    )

    called_countries = models.ManyToManyField(
        "references.Country",
        verbose_name=_("Called Countries"),
        blank=True,
    )

    traffic_segments = models.ManyToManyField(
        "references.TrafficSegment",
        verbose_name=_("Traffic Segments"),
        blank=True,
    )

    imsi_count_type = models.PositiveSmallIntegerField(
        _("IMSI Count Type"),
        choices=get_choices(IMSICountTypeEnum),
        null=True,
        blank=True,
    )

    qualifying_direction = models.PositiveSmallIntegerField(
        _("Qualifying Direction"),
        choices=get_choices(DiscountDirectionEnum),
        null=True,
        blank=True,
    )

    qualifying_service_types = ArrayField(
        models.PositiveSmallIntegerField(choices=get_choices(ServiceTypeEnum)),
        size=len(ServiceTypeEnum),
        null=True,
        blank=True,
    )

    qualifying_basis = models.PositiveSmallIntegerField(
        _("Qualifying Basis"),
        choices=get_choices(DiscountQualifyingBasisEnum),
        null=True,
        blank=True,
    )

    qualifying_lower_bound = BoundDecimalField(_("Qualifying Lower Bound"), null=True, blank=True)

    qualifying_upper_bound = BoundDecimalField(_("Qualifying Upper Bound"), null=True, blank=True)

    model_type = models.PositiveSmallIntegerField(
        _("Model Type"),
        choices=get_choices(DiscountModelTypeEnum),
        null=True,
        blank=True,
    )

    financial_threshold = BoundDecimalField(_("Financial Threshold"), null=True, blank=True)

    above_financial_threshold_rate = DiscountDecimalField(_("Above Financial Threshold Rate"), null=True, blank=True)

    above_commitment_rate = DiscountDecimalField(_("Above Commitment Rate"), null=True, blank=True)

    inbound_market_share = BoundDecimalField(_("Inbound Market Share"), null=True, blank=True)

    commitment_distribution_parameters = JSONField(
        _("Manual Commitment Distribution Parameters"),
        null=True,
        blank=True,
        default=None,
    )

    created_at = models.DateTimeField(_("Creation date"), auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(_("Last updated date"), auto_now=True)

    class Meta:
        db_table = "agreement_discounts"

    def __str__(self) -> str:
        return f"Discount #{self.id} - Agreement #{self.agreement_id}"

    def get_service_types_display(self) -> list[str]:
        return get_array_choices_display(ServiceTypeEnum, self.service_types)

    def get_call_destinations_display(self) -> list[str]:
        return get_array_choices_display(CallDestinationEnum, self.call_destinations)

    def get_qualifying_service_types_display(self) -> list[str]:
        return get_array_choices_display(ServiceTypeEnum, self.qualifying_service_types)
