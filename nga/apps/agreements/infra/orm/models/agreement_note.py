import uuid

from django.db import models

from nga.apps.agreements.enums import AgreementNoteTypeEnum
from nga.core.enums import get_choices


class AgreementNote(models.Model):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, editable=False, unique=True)

    agreement = models.ForeignKey(
        "agreements.Agreement",
        on_delete=models.CASCADE,
        related_name="notes",
        related_query_name="note",
    )

    type = models.PositiveSmallIntegerField("Note Type", choices=get_choices(AgreementNoteTypeEnum))

    content = models.TextField()

    user = models.ForeignKey(
        "users.User",
        null=True,
        blank=True,
        related_name="agreement_notes",
        related_query_name="agreement_note",
        on_delete=models.CASCADE,
    )

    created_at = models.DateTimeField("Creation date", auto_now_add=True)

    updated_at = models.DateTimeField("Last updated date", auto_now=True)

    class Meta:
        db_table = "agreement_notes"
