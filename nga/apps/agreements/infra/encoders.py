from typing import Sequence

from django.core.serializers.json import DjangoJSONEncoder

from nga.apps.agreements.domain.models.discount import CommitmentDistributionParameter


class CommitmentDistributionParameterJSONEncoder:
    @classmethod
    def encode_many(cls, commitment_distribution_parameters: Sequence[CommitmentDistributionParameter]) -> str:
        json_encoder = DjangoJSONEncoder()

        data = tuple(p.to_dict() for p in commitment_distribution_parameters)

        return json_encoder.encode(data)
