from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.requests import GetDiscountsRequest


@Mediator.handler
class GetDiscountsRequestHandler:
    @inject
    def __init__(
        self,
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
    ) -> None:
        self._discount_repository = discount_repository

    def handle(self, request: GetDiscountsRequest) -> tuple[Discount, ...]:
        discounts = self._discount_repository.get_many(
            request.agreement_id,
            home_operators=request.home_operators,
            partner_operators=request.partner_operators,
        )

        return discounts
