from typing import Collection

from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.enums import AgreementStatusEnum


class BudgetAgreementStateContainer:
    def __init__(self, budget_agreements: Collection[BudgetAgreement]) -> None:
        self._budget_agreements = budget_agreements

    @property
    def records(self) -> Collection[BudgetAgreement]:
        return self._budget_agreements

    @property
    def only_approved(self) -> Collection[BudgetAgreement]:
        return [
            a
            for a in self._budget_agreements
            if a.status
            in [
                AgreementStatusEnum.APPROVED,
                AgreementStatusEnum.LIVE,
                AgreementStatusEnum.CLOSED,
                AgreementStatusEnum.BUDGETING,
                AgreementStatusEnum.AUTO_RENEWED,
            ]
        ]

    @property
    def only_submitted(self) -> Collection[BudgetAgreement]:
        return [a for a in self._budget_agreements if a.status == AgreementStatusEnum.SUBMITTED]

    @property
    def any_approved(self) -> bool:

        return any(self.only_approved)

    @property
    def any_non_confirmed(self) -> bool:

        return any(a.is_non_confirmed for a in self._budget_agreements)

    @property
    def any_submitted(self) -> bool:

        return any(self.only_submitted)
