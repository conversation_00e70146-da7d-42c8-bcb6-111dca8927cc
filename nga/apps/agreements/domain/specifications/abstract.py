from abc import ABC, abstractmethod
from typing import Sequence

from nga.apps.agreements.domain.models import Discount


class AbstractDiscountSpecification(ABC):
    @abstractmethod
    def verify(self, discount: Discount) -> None:
        """Verifies Discount implies the defined rules."""


class AbstractDiscountScopeSpecification(AbstractDiscountSpecification, ABC):
    """Base specification for discount scope logic."""


class ContainerDiscountSpecification(AbstractDiscountSpecification):
    def __init__(self, specifications: Sequence[AbstractDiscountSpecification]) -> None:
        self._specifications = specifications

    @property
    def specifications(self) -> Sequence[AbstractDiscountSpecification]:
        return self._specifications

    def verify(self, discount: Discount) -> None:
        for spec in self._specifications:
            spec.verify(discount)
