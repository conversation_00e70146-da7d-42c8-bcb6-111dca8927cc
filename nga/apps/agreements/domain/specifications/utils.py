from typing import Optional

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.abstract import AbstractDiscountSpecification
from nga.apps.agreements.domain.specifications.consts import DISCOUNT_SPECIFICATION_MAP
from nga.apps.agreements.enums import DiscountModelTypeEnum


def discount_fulfills_spec(discount: Discount, spec: AbstractDiscountSpecification) -> bool:
    try:
        spec.verify(discount)

        return True

    except Exception:
        return False


def get_spec_by_model_type(
    model_type: Optional[DiscountModelTypeEnum],
) -> AbstractDiscountSpecification:
    if model_type not in DISCOUNT_SPECIFICATION_MAP:
        raise DiscountValidationError("Not found specification for model type")

    spec = DISCOUNT_SPECIFICATION_MAP[model_type]  # type: ignore[index]

    return spec
