from typing import Mapping

from nga.apps.agreements.domain.specifications.abstract import AbstractDiscountSpecification
from nga.apps.agreements.domain.specifications.discount_setup import (
    AYCEDiscountSetupSpecification,
    BackToFirstDiscountSetupSpecification,
    BalancedUnbalancedSREDiscountSetupSpecification,
    BalancedUnbalancedSteppedTieredDiscountSetupSpecification,
    PMPIAboveThresholdDiscountSetupSpecification,
    PMPIBackToFirstDiscountSetupSpecification,
    PMPIDiscountSetupSpecification,
    PMPISteppedTieredDiscountSetupSpecification,
    PMPIWithIncrementalChargingDiscountSetupSpecification,
    SoPFinancialDiscountSetupSpecification,
    SoPTrafficSREDiscountSetupSpecification,
    SoPTrafficSteppedTieredDiscountSetupSpecification,
    SREDiscountSetupSpecification,
    SteppedTieredDiscountSetupSpecification,
)
from nga.apps.agreements.enums import DiscountModelTypeEnum

DISCOUNT_SPECIFICATION_MAP: Mapping[DiscountModelTypeEnum, AbstractDiscountSpecification] = {
    DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL: SoPFinancialDiscountSetupSpecification(),
    DiscountModelTypeEnum.SEND_OR_PAY_TRAFFIC_SINGLE_RATE_EFFECTIVE: SoPTrafficSREDiscountSetupSpecification(),
    DiscountModelTypeEnum.SEND_OR_PAY_TRAFFIC_STEPPED_TIERED: SoPTrafficSteppedTieredDiscountSetupSpecification(),
    DiscountModelTypeEnum.ALL_YOU_CAN_EAT: AYCEDiscountSetupSpecification(),
    DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE: SREDiscountSetupSpecification(),
    DiscountModelTypeEnum.STEPPED_TIERED: SteppedTieredDiscountSetupSpecification(),
    DiscountModelTypeEnum.BACK_TO_FIRST: BackToFirstDiscountSetupSpecification(),
    DiscountModelTypeEnum.BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE: BalancedUnbalancedSREDiscountSetupSpecification(),
    DiscountModelTypeEnum.BALANCED_UNBALANCED_STEPPED_TIERED: BalancedUnbalancedSteppedTieredDiscountSetupSpecification(),  # noqa
    DiscountModelTypeEnum.PER_MONTH_PER_IMSI: PMPIDiscountSetupSpecification(),
    DiscountModelTypeEnum.PER_MONTH_PER_IMSI_ABOVE_THRESHOLD: PMPIAboveThresholdDiscountSetupSpecification(),
    DiscountModelTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED: PMPISteppedTieredDiscountSetupSpecification(),
    DiscountModelTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING: PMPIWithIncrementalChargingDiscountSetupSpecification(),  # noqa
    DiscountModelTypeEnum.PER_MONTH_PER_IMSI_BACK_TO_FIRST: PMPIBackToFirstDiscountSetupSpecification(),
}
