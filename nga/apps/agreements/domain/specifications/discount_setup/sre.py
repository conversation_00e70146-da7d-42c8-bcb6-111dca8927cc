from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup.base import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import verify_single_calculation_type
from nga.apps.agreements.enums import DiscountCalculationTypeEnum


class SREDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    def verify(self, discount: Discount) -> None:
        """Verifies that provided discount fulfills SRE setup."""

        verify_single_calculation_type(discount, DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE)

        super().verify(discount)
