"""All You Can Eat"""

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup.base import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    bound_type_is_financial,
    credit_note_eoa_settlement_method,
    lower_bound_filled,
    no_balancing,
    verify_single_calculation_type,
)
from nga.apps.agreements.enums import DiscountCalculationTypeEnum


class AYCEDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    discount_validators = (credit_note_eoa_settlement_method,)

    discount_parameter_validators = (
        no_balancing,
        lower_bound_filled,
        bound_type_is_financial,
    )

    def verify(self, discount: Discount) -> None:
        verify_single_calculation_type(discount, DiscountCalculationTypeEnum.ALL_YOU_CAN_EAT)

        super().verify(discount)
