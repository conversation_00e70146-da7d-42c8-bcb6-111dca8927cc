from decimal import Decimal

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.domain.specifications.discount_setup.balanced_unbalanced_sre import (
    BalancedUnbalancedSREDiscountSetupSpecification,
)
from nga.apps.agreements.domain.specifications.discount_setup.base import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    bound_type_is_volume,
    param_basis_is_value,
    param_basis_value_is_required,
    unbalanced_only,
)
from nga.apps.agreements.domain.specifications.discount_setup.stepped_tiered import stepped_tiered_bounds
from nga.apps.agreements.enums import DiscountCalculationTypeEnum


class BalancedUnbalancedSteppedTieredDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    _model = "Bal/Unbal Tier"
    _min_parameters = 2

    discount_parameter_validators = (
        param_basis_is_value,
        param_basis_value_is_required,
    )

    def verify(self, discount: Discount) -> None:
        """Verifies that discount fulfills Balanced Unbalanced Stepped Tiered setup."""

        if discount.total_parameters < self._min_parameters:
            raise DiscountValidationError(f"{self._model} requires at least {self._min_parameters} parameters.")

        expected_calc_types = sorted(
            (DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE, DiscountCalculationTypeEnum.STEPPED_TIERED)
        )
        calc_param_types = sorted([p.calculation_type for p in discount.parameters])

        if set(calc_param_types) != set(expected_calc_types):
            raise DiscountValidationError(f"Invalid parameters calculation type setup for {self._model}.")

        super().verify(discount)

        self.get_balanced_basis_value(discount)

        unbalanced_tiers = self.get_tiers(discount)

        for tier in unbalanced_tiers:
            bound_type_is_volume(tier)
            unbalanced_only(tier)

        stepped_tiered_bounds(unbalanced_tiers)

    @classmethod
    def get_balanced_basis_value(cls, discount: Discount) -> Decimal:
        return BalancedUnbalancedSREDiscountSetupSpecification.get_balanced_basis_value(discount)

    @classmethod
    def get_tiers(cls, discount: Discount) -> list[DiscountParameter]:
        return [p for p in discount.parameters if p.calculation_type == DiscountCalculationTypeEnum.STEPPED_TIERED]
