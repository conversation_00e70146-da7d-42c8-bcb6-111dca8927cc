from decimal import Decimal
from typing import cast

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    access_fee_rate_filled,
    credit_note_eoa_settlement_method,
    lower_bound_filled,
    no_balancing,
    verify_single_calculation_type,
)
from nga.apps.agreements.enums import DiscountBoundTypeEnum, DiscountCalculationTypeEnum
from nga.core.enums import IMSICountTypeEnum, ServiceTypeEnum


class PMPIWithIncrementalChargingDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    _model = "Per Month Per IMSI with Incremental charging"

    discount_validators = (credit_note_eoa_settlement_method,)

    discount_parameter_validators = (
        no_balancing,
        lower_bound_filled,
        access_fee_rate_filled,
    )

    def verify(self, discount: Discount) -> None:
        """Verifies that discount fulfills model setup."""

        verify_single_calculation_type(
            discount=discount,
            expected_calculation_type=DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING,
        )

        super().verify(discount)

        expected_service_types = sorted((ServiceTypeEnum.ACCESS_FEE, ServiceTypeEnum.DATA))

        if sorted(discount.service_types) != expected_service_types:
            raise DiscountValidationError(f"{self._model} requires {expected_service_types} service types")

        expected_imsi_count_type = IMSICountTypeEnum.DATA

        if discount.imsi_count_type is None or discount.imsi_count_type != expected_imsi_count_type:
            raise DiscountValidationError(f"{self._model} requires {expected_imsi_count_type.name} IMSI count type")

        expected_bound_type = DiscountBoundTypeEnum.VOLUME_INCLUDED_IN_ACCESS_FEE

        param = discount.parameters[0]

        if param.bound_type is None or param.bound_type != expected_bound_type:
            raise DiscountValidationError(f"{self._model} requires {expected_bound_type.name} bound type")

        if param.incremental_rate is None:
            raise DiscountValidationError(f"{self._model} requires incremental rate to be filled")

    @classmethod
    def get_access_fee_rate(cls, discount: Discount) -> Decimal:
        return cast(Decimal, discount.parameters[0].access_fee_rate)

    @classmethod
    def get_incremental_rate(cls, discount: Discount) -> Decimal:
        return cast(Decimal, discount.parameters[0].incremental_rate)

    @classmethod
    def get_lower_bound(cls, discount: Discount) -> Decimal:
        return cast(Decimal, discount.parameters[0].lower_bound)
