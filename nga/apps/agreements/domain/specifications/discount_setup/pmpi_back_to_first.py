from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    bound_type_is_unique_per_month_per_imsi,
    lower_bound_filled,
    only_access_fee,
    param_basis_is_value,
    param_basis_value_is_required,
    verify_single_calculation_type,
)
from nga.apps.agreements.enums import DiscountCalculationTypeEnum


class PMPIBackToFirstDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    discount_validators = (only_access_fee,)

    discount_parameter_validators = (
        param_basis_is_value,
        param_basis_value_is_required,
        lower_bound_filled,
        bound_type_is_unique_per_month_per_imsi,
    )

    def verify(self, discount: Discount) -> None:
        verify_single_calculation_type(discount, DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_BACK_TO_FIRST)

        super().verify(discount)
