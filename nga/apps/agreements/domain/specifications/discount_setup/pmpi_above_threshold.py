from decimal import Decimal
from typing import cast

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    bound_type_is_unique_per_month_per_imsi,
    credit_note_eoa_settlement_method,
    lower_bound_filled,
    only_access_fee,
    verify_single_calculation_type,
)
from nga.apps.agreements.enums import DiscountCalculationTypeEnum


class PMPIAboveThresholdDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    discount_validators = (
        credit_note_eoa_settlement_method,
        only_access_fee,
    )

    discount_parameter_validators = (
        *AbstractDiscountSetupSpecification.discount_parameter_validators,
        lower_bound_filled,
        bound_type_is_unique_per_month_per_imsi,
    )

    def verify(self, discount: Discount) -> None:
        """Verifies that discount fulfills model setup."""

        verify_single_calculation_type(discount, DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_ABOVE_THRESHOLD)

        super().verify(discount)

    @classmethod
    def get_lower_bound(cls, discount: Discount) -> Decimal:
        return cast(Decimal, discount.parameters[0].lower_bound)

    @classmethod
    def get_basis_value(cls, discount: Discount) -> Decimal:
        return cast(Decimal, discount.parameters[0].basis_value)
