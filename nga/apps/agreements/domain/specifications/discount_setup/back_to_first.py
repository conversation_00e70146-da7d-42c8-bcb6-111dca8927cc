from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    bound_type_is_volume,
    lower_bound_filled,
)
from nga.apps.agreements.enums import DiscountCalculationTypeEnum

from .base import AbstractDiscountSetupSpecification


class BackToFirstDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    _model = "Back To First"

    discount_parameter_validators = (
        *AbstractDiscountSetupSpecification.discount_parameter_validators,
        bound_type_is_volume,
        lower_bound_filled,
    )

    def verify(self, discount: Discount) -> None:
        """Verifies that discount fulfills setup."""

        all_btf = all(p.calculation_type == DiscountCalculationTypeEnum.BACK_TO_FIRST for p in discount.parameters)

        if not all_btf:
            raise DiscountValidationError(f"All params must be {DiscountCalculationTypeEnum.BACK_TO_FIRST.name}")

        super().verify(discount)

        first_tier, *tiers = discount.parameters

        if first_tier.lower_bound != 0:
            raise DiscountValidationError("First tier lower bound must be zero")

        for curr_tier, next_tier in zip(discount.parameters[:-1], discount.parameters[1:]):
            if curr_tier.lower_bound > next_tier.lower_bound:  # type: ignore[operator]
                raise DiscountValidationError("Tier's lower_bound must be in ascending order")
