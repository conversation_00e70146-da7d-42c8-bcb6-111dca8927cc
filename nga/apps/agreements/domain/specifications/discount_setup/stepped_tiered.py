from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.domain.specifications.discount_setup.base import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import bound_type_is_volume
from nga.apps.agreements.enums import DiscountCalculationTypeEnum


def stepped_tiered_bounds(tiers: Sequence[DiscountParameter], first_tier_start_from: Decimal = Decimal("0")) -> None:
    """Validates tiers bounds."""

    # define tiers
    if len(tiers) == 1:
        first_tier, n_tiers, last_tier = tiers[0], [], None  # type: ignore[var-annotated]
    elif len(tiers) == 2:
        first_tier, n_tiers, last_tier = tiers[0], [], tiers[-1]
    else:
        first_tier, *n_tiers, last_tier = tiers

    # first tier validation
    if first_tier.lower_bound is None or first_tier.lower_bound != first_tier_start_from:
        raise DiscountValidationError(f"First tier must have lower bound equal to {first_tier_start_from}.")

    if not first_tier.upper_bound and (n_tiers or last_tier):
        raise DiscountValidationError("First tier must have upper bound.")

    if first_tier.upper_bound and first_tier.lower_bound > first_tier.upper_bound:
        raise DiscountValidationError("First tier lower bound must be less then upper bound.")

    # n-tier validation
    prev_upper_bound = first_tier.upper_bound
    for n_tier in n_tiers:
        if not n_tier.lower_bound or not n_tier.upper_bound:
            raise DiscountValidationError("N-Tier must have filled bounds.")

        if prev_upper_bound != n_tier.lower_bound:
            raise DiscountValidationError("N-tier lower bound must equal to previous tier upper bound.")

        if n_tier.lower_bound > n_tier.upper_bound:
            raise DiscountValidationError("N-Tier lower bound must be less then upper bound.")

        prev_upper_bound = n_tier.upper_bound

    # last tier validation
    if last_tier:
        if not last_tier.lower_bound:
            raise DiscountValidationError("Last tier must have lower bound.")

        if last_tier.lower_bound != prev_upper_bound:
            raise DiscountValidationError("Last tier lower bound must equal to previous upper bound.")


class SteppedTieredDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    discount_parameter_validators = (
        *AbstractDiscountSetupSpecification.discount_parameter_validators,
        bound_type_is_volume,
    )

    def verify(self, discount: Discount) -> None:
        """Verifies that provided discount fulfills STEPPED_TIERED setup."""

        if not all(p.calculation_type == DiscountCalculationTypeEnum.STEPPED_TIERED for p in discount.parameters):
            raise DiscountValidationError("Discount parameters calculation type is invalid for STEPPED_TIERED setup.")

        super().verify(discount)

        stepped_tiered_bounds(discount.parameters)
