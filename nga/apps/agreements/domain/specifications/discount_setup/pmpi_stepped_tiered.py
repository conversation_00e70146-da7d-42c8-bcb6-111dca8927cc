from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    bound_type_is_unique_per_month_per_imsi,
    credit_note_eoa_settlement_method,
    only_access_fee,
)
from nga.apps.agreements.domain.specifications.discount_setup.stepped_tiered import stepped_tiered_bounds
from nga.apps.agreements.enums import DiscountCalculationTypeEnum


class PMPISteppedTieredDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    _model = "Per Month Per IMSI Stepped Tiered"

    discount_validators = (
        credit_note_eoa_settlement_method,
        only_access_fee,
    )

    discount_parameter_validators = (
        *AbstractDiscountSetupSpecification.discount_parameter_validators,
        bound_type_is_unique_per_month_per_imsi,
    )

    def verify(self, discount: Discount) -> None:
        """Verifies that discount fulfills setup."""

        calculation_types_are_valid = all(
            p.calculation_type == DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED
            for p in discount.parameters
        )

        if not calculation_types_are_valid:
            raise DiscountValidationError(f"Discount parameters calculation type is invalid for {self._model} setup")

        super().verify(discount)

        stepped_tiered_bounds(discount.parameters)
