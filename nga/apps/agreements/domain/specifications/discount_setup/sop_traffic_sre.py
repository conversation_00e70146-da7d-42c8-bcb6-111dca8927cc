from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.domain.specifications.discount_setup import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import verify_number_of_parameters
from nga.apps.agreements.enums import DiscountBoundTypeEnum, DiscountCalculationTypeEnum, DiscountDirectionEnum


class SoPTrafficSREDiscountSetupSpecification(AbstractDiscountSetupSpecification):

    _total_parameters = 2

    def verify(self, discount: Discount) -> None:
        """Verifies that discount fulfills SoP Traffic SRE setup."""

        verify_number_of_parameters(discount, self._total_parameters)

        expected_calc_types = sorted(
            (DiscountCalculationTypeEnum.SEND_OR_PAY_TRAFFIC, DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE)
        )
        calc_param_types = sorted([p.calculation_type for p in discount.parameters])

        if calc_param_types != expected_calc_types:
            raise DiscountValidationError("Invalid parameters calculation type setup for SoP Traffic SRE.")

        sop_param = self.get_sop_parameter(discount)

        if not sop_param.lower_bound:
            raise DiscountValidationError("SoP parameter lower bound must be set.")

        if sop_param.bound_type not in [DiscountBoundTypeEnum.VOLUME, DiscountBoundTypeEnum.MARKET_SHARE]:
            raise DiscountValidationError(
                f"SoP parameter bound type must be {DiscountBoundTypeEnum.VOLUME.name} "
                f"or {DiscountBoundTypeEnum.MARKET_SHARE.name}."
            )

        if (
            discount.direction in [DiscountDirectionEnum.INBOUND, DiscountDirectionEnum.BIDIRECTIONAL]
            and sop_param.bound_type == DiscountBoundTypeEnum.MARKET_SHARE
            and discount.inbound_market_share is None
        ):
            raise DiscountValidationError(
                f"Inbound market share must be set for discount with direction {discount.direction.name}."
            )

        super().verify(discount)

    @classmethod
    def get_sop_parameter(cls, discount: Discount) -> DiscountParameter:
        sop_parameter = next(
            p for p in discount.parameters if p.calculation_type == DiscountCalculationTypeEnum.SEND_OR_PAY_TRAFFIC
        )

        return sop_parameter

    @classmethod
    def get_sre_parameter(cls, discount: Discount) -> DiscountParameter:
        sre_parameter = next(
            p for p in discount.parameters if p.calculation_type == DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE
        )

        return sre_parameter
