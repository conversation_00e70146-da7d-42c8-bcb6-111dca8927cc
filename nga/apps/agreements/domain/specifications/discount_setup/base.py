from abc import ABC
from typing import Callable

from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.domain.specifications.abstract import (
    AbstractDiscountSpecification,
)
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    above_commitment_rate_only_for_sub_discounts,
    credit_note_eoa_settlement_method,
    financial_threshold_only_for_sop_financial,
    no_balancing,
    param_basis_is_value,
    param_basis_value_is_required,
    service_types_with_combo,
    sub_discounts_only_for_sop_financial,
)


class AbstractDiscountSetupSpecification(AbstractDiscountSpecification, ABC):
    """Base specification for discount setup logic."""

    discount_validators: tuple[Callable[[Discount], None], ...] = (
        service_types_with_combo,
        credit_note_eoa_settlement_method,
        financial_threshold_only_for_sop_financial,
        sub_discounts_only_for_sop_financial,
        above_commitment_rate_only_for_sub_discounts,
    )

    discount_parameter_validators: tuple[Callable[[DiscountParameter], None], ...] = (
        param_basis_is_value,
        param_basis_value_is_required,
        no_balancing,
    )

    def verify(self, discount: Discount) -> None:
        for discount_validator in self.discount_validators:
            discount_validator(discount)

        for param in discount.parameters:
            for discount_parameter_validator in self.discount_parameter_validators:
                discount_parameter_validator(param)
