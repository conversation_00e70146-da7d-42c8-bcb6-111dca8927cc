from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.abstract import AbstractDiscountSpecification
from nga.apps.agreements.domain.specifications.utils import get_spec_by_model_type


class DiscountSetupSpecification(AbstractDiscountSpecification):

    def verify(self, discount: Discount) -> None:
        discount_spec = get_spec_by_model_type(discount.model_type)

        discount_spec.verify(discount)
