from decimal import Decimal
from itertools import product
from typing import cast

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.domain.specifications.discount_setup import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    bound_type_is_financial,
    credit_note_eoa_settlement_method,
    lower_bound_filled,
    no_balancing,
    verify_single_calculation_type,
)
from nga.apps.agreements.enums import DiscountCalculationTypeEnum


class SoPFinancialDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    discount_validators = (credit_note_eoa_settlement_method,)

    discount_parameter_validators = (
        no_balancing,
        lower_bound_filled,
        bound_type_is_financial,
    )

    def verify(self, discount: Discount) -> None:
        """Verifies that discount fulfills SoP Financial setup."""

        verify_single_calculation_type(discount, DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL)

        self.validate_commitment_distribution_parameters(discount)

        super().verify(discount)

    @classmethod
    def get_parameter(cls, discount: Discount) -> DiscountParameter:
        return discount.parameters[0]

    @classmethod
    def get_lower_bound(cls, discount: Discount) -> Decimal:
        return cast(Decimal, cls.get_parameter(discount).lower_bound)

    def validate_commitment_distribution_parameters(self, discount: Discount) -> None:
        if discount.commitment_distribution_parameters is None:
            return

        if sum(c.charge for c in discount.commitment_distribution_parameters) != self.get_lower_bound(discount):
            raise DiscountValidationError("Commitment Distribution Parameters total value isn't equal to lower bound.")

        unique_operator_combinations: set[tuple[int, int]] = set()

        for commitment in discount.commitment_distribution_parameters:
            if not set(commitment.home_operators).issubset(discount.home_operators):
                raise DiscountValidationError("Commitment Distribution Parameter's Home operators exceed Discount.")

            if not set(commitment.partner_operators).issubset(discount.partner_operators):
                raise DiscountValidationError("Commitment Distribution Parameter's Partner operators exceed Discount.")

            commitment_operator_combinations = set(product(commitment.home_operators, commitment.partner_operators))

            if unique_operator_combinations.intersection(commitment_operator_combinations):
                raise DiscountValidationError("Commitment Distribution Parameters have intersection.")

            unique_operator_combinations = unique_operator_combinations.union(commitment_operator_combinations)
