from decimal import Decimal
from typing import cast

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    param_basis_is_value,
    param_basis_value_is_required,
    verify_number_of_parameters,
)
from nga.apps.agreements.enums import DiscountBalancingEnum, DiscountCalculationTypeEnum

from .base import AbstractDiscountSetupSpecification


class BalancedUnbalancedSREDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    _model = "Bal/Unbal SRE"
    _total_parameters = 2

    discount_parameter_validators = (
        param_basis_is_value,
        param_basis_value_is_required,
    )

    def verify(self, discount: Discount) -> None:
        """Verifies that discount fulfills Balanced Unbalanced SRE setup."""

        verify_number_of_parameters(discount, self._total_parameters)

        params_are_sre = all(
            p.calculation_type == DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE for p in discount.parameters
        )

        if not params_are_sre:
            msg = f"All params must be {DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name} for {self._model}"
            raise DiscountValidationError(msg)

        super().verify(discount)

        self.get_balanced_basis_value(discount)
        self.get_unbalanced_basis_value(discount)

    @classmethod
    def get_balanced_basis_value(cls, discount: Discount) -> Decimal:
        try:
            balanced_param = next(p for p in discount.parameters if p.balancing == DiscountBalancingEnum.BALANCED)
        except StopIteration:
            raise DiscountValidationError("Balanced parameter is required.")

        return cast(Decimal, balanced_param.basis_value)

    @classmethod
    def get_unbalanced_basis_value(cls, discount: Discount) -> Decimal:
        try:
            unbalanced_param = next(p for p in discount.parameters if p.balancing == DiscountBalancingEnum.UNBALANCED)
        except StopIteration:
            raise DiscountValidationError("Unbalanced parameter is required.")

        return cast(Decimal, unbalanced_param.basis_value)
