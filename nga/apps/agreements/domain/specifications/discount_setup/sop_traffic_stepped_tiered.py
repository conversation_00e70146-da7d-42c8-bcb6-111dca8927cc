from typing import Sequence

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.domain.specifications.discount_setup import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import bound_type_is_volume
from nga.apps.agreements.domain.specifications.discount_setup.stepped_tiered import stepped_tiered_bounds
from nga.apps.agreements.enums import DiscountCalculationTypeEnum


class SoPTrafficSteppedTieredDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    _model = "SoP Traffic Stepped Tiered"
    _min_parameters = 2

    discount_parameter_validators = (
        *AbstractDiscountSetupSpecification.discount_parameter_validators,
        bound_type_is_volume,
    )

    def verify(self, discount: Discount) -> None:
        """Verifies that discount fulfills Send Or Pay Traffic + Stepped Tiered setup."""

        if discount.total_parameters < self._min_parameters:
            raise DiscountValidationError(f"{self._model} requires at least {self._min_parameters} parameters.")

        expected_calc_types = sorted(
            (DiscountCalculationTypeEnum.SEND_OR_PAY_TRAFFIC, DiscountCalculationTypeEnum.STEPPED_TIERED)
        )
        calc_param_types = sorted([p.calculation_type for p in discount.parameters])

        if set(calc_param_types) != set(expected_calc_types):
            raise DiscountValidationError(f"Invalid parameters calculation type setup for {self._model}.")

        sop_param = self.get_sop_parameter(discount)

        if not sop_param.lower_bound:
            raise DiscountValidationError("SoP parameter lower bound must be set.")

        super().verify(discount)

        tiers = self.get_tiers_parameters(discount)

        stepped_tiered_bounds(tiers)

    @classmethod
    def get_sop_parameter(cls, discount: Discount) -> DiscountParameter:
        sop_parameter = next(
            p for p in discount.parameters if p.calculation_type == DiscountCalculationTypeEnum.SEND_OR_PAY_TRAFFIC
        )

        return sop_parameter

    @classmethod
    def get_tiers_parameters(cls, discount: Discount) -> Sequence[DiscountParameter]:
        return tuple(p for p in discount.parameters if p.calculation_type == DiscountCalculationTypeEnum.STEPPED_TIERED)
