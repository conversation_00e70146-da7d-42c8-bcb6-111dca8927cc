from decimal import Decimal
from typing import cast

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    credit_note_eoa_settlement_method,
    only_access_fee,
    verify_single_calculation_type,
)
from nga.apps.agreements.enums import DiscountCalculationTypeEnum


class PMPIDiscountSetupSpecification(AbstractDiscountSetupSpecification):
    discount_validators = (
        credit_note_eoa_settlement_method,
        only_access_fee,
    )

    def verify(self, discount: Discount) -> None:
        """Verifies that discount fulfills model setup."""

        verify_single_calculation_type(discount, DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI)

        super().verify(discount)

    @classmethod
    def get_basis_value(cls, discount: Discount) -> Decimal:
        return cast(Decimal, discount.parameters[0].basis_value)
