from typing import Any, Optional, Sequence

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractAgreementRepository, AbstractDiscountRepository
from nga.apps.agreements.domain.specifications.abstract import AbstractDiscountScopeSpecification
from nga.apps.agreements.enums import DiscountDirectionEnum


class DiscountExceedingAgreementSpecification(AbstractDiscountScopeSpecification):
    """Defines rule that verifies that Discount does not exceed its Agreement traffic."""

    def __init__(self, agreement_repository: AbstractAgreementRepository) -> None:
        self._agreement_repository = agreement_repository

    def verify(self, discount: Discount) -> None:
        agreement = self._agreement_repository.get_by_id(discount.agreement_id)

        if not set(discount.home_operators).issubset(agreement.home_operators):
            raise DiscountValidationError("Home operators exceed Agreement")

        if not set(discount.partner_operators).issubset(agreement.partner_operators):
            raise DiscountValidationError("Partner operators exceed Agreement")

        a_period, d_period = agreement.period, discount.period

        if d_period.start_date < a_period.start_date or d_period.end_date > a_period.end_date:
            raise DiscountValidationError("Period exceeds Agreement period")


class DiscountIntersectionSpecification(AbstractDiscountScopeSpecification):
    """Defines rule that verifies that Discount must not intersect other Discounts within its Agreement."""

    def __init__(self, discount_repository: AbstractDiscountRepository) -> None:
        self._discount_repository = discount_repository

    def verify(self, discount: Discount) -> None:
        has_intersection = self._discount_repository.has_intersection_within_agreement(discount)

        if has_intersection:
            raise DiscountValidationError("Discount has intersection with other Agreement discounts.")


class SubDiscountExceedingDiscountSpecification(AbstractDiscountScopeSpecification):
    def verify(self, discount: Discount) -> None:
        """Verifies that sub-discounts do not exceed discount parameters."""

        for sd in discount.sub_discounts:
            if not set(sd.home_operators).issubset(discount.home_operators):
                raise DiscountValidationError("Sub-Discount Home operators exceed Discount")

            if not set(sd.partner_operators).issubset(discount.partner_operators):
                raise DiscountValidationError("Sub-Discount Partner operators exceed Discount")

            sb_period, d_period = sd.period, discount.period

            if d_period.start_date > sb_period.start_date or d_period.end_date < sb_period.end_date:
                raise DiscountValidationError("Sub-Discount Period exceeds Discount period")

            if discount.direction != DiscountDirectionEnum.BIDIRECTIONAL and sd.direction != discount.direction:
                raise DiscountValidationError("Sub-Discount Direction exceeds Discount direction")

            if not set(sd.service_types).issubset(discount.service_types):
                raise DiscountValidationError("Sub-Discount Service types exceed Discount service types")

            self.verify_optional_collection(sd.call_destinations, discount.call_destinations, "call destinations")

            self.verify_optional_collection(sd.called_countries, discount.called_countries, "called countries")

            self.verify_optional_collection(sd.traffic_segments, discount.traffic_segments, "traffic segments")

    @classmethod
    def verify_optional_collection(
        cls,
        comparable: Optional[Sequence[Any]],
        target: Optional[Sequence[Any]],
        field_name: str,
    ) -> None:
        error_msg = f"Sub-Discount {field_name} exceed Discount {field_name}"

        if not comparable and target:
            raise DiscountValidationError(error_msg)

        elif target and comparable:
            if not set(comparable).issubset(set(target)):
                raise DiscountValidationError(error_msg)


class SubDiscountIntersectionSpecification(AbstractDiscountScopeSpecification):
    def __init__(self, discount_repository: AbstractDiscountRepository) -> None:
        self._discount_repository = discount_repository

    def verify(self, discount: Discount) -> None:
        """Verifies that sub-discounts are not intersected."""

        has_intersection = self._discount_repository.has_intersection_between_sub_discounts(discount)

        if has_intersection:
            raise DiscountValidationError("Discount has intersection between sub-discounts.")
