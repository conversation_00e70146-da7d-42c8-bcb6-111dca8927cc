from typing import Sequence

from nga.apps.agreements.domain.exceptions import AgreementIntersectionError
from nga.apps.agreements.domain.mappers import from_discount_to_dto
from nga.apps.agreements.domain.models import BudgetAgreement, Discount
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.apps.agreements.enums import AgreementStatusEnum


def verify_if_agreement_approved(
    budget_agreement_repository: AbstractBudgetAgreementRepository,
    budget_agreement: BudgetAgreement,
) -> None:
    has_intersection = budget_agreement_repository.has_intersection(
        budget_agreement,
        with_statuses=AgreementStatusEnum.get_confirmed_statuses(),
    )

    if has_intersection:
        raise AgreementIntersectionError(budget_agreement.id)


def copy_discounts(
    discounts: Sequence[Discount],
    target_budget_agreement: BudgetAgreement,
    discount_repository: AbstractDiscountRepository,
) -> None:
    for discount in discounts:
        discount_dto = from_discount_to_dto(discount)

        copied_discount = discount_repository.create(target_budget_agreement.agreement_id, discount_dto)

        for sub_discount in discount.sub_discounts:
            sub_discount_dto = from_discount_to_dto(sub_discount)

            discount_repository.create_sub_discount(copied_discount.id, sub_discount_dto)


def unlink_agreements(
    budget_agreement_repository: AbstractBudgetAgreementRepository,
    budget_agreement: BudgetAgreement,
) -> None:
    if budget_agreement.parent_id is not None:
        budget_agreement.parent_id = None

        budget_agreement_repository.save(budget_agreement)

    nested_agreement = budget_agreement_repository.get_by_parent_id(
        budget_agreement.agreement_id,
        budget_id=budget_agreement.budget_id,
    )

    if nested_agreement is not None:
        budget_agreement_repository.delete_by_id(nested_agreement.id, for_all_budgets=True)
