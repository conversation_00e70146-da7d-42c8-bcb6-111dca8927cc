from nga.apps.agreements.domain.dto import DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.core.types import Month


def from_discount_parameter_to_dto(parameter: DiscountParameter) -> DiscountParameterDTO:
    return DiscountParameterDTO(
        calculation_type=parameter.calculation_type,
        basis=parameter.basis,
        basis_value=parameter.basis_value,
        balancing=parameter.balancing,
        bound_type=parameter.bound_type,
        lower_bound=parameter.lower_bound,
        upper_bound=parameter.upper_bound,
        toll_rate=parameter.toll_rate,
        airtime_rate=parameter.airtime_rate,
        fair_usage_rate=parameter.fair_usage_rate,
        fair_usage_threshold=parameter.fair_usage_threshold,
        access_fee_rate=parameter.access_fee_rate,
        incremental_rate=parameter.incremental_rate,
    )


def from_discount_to_dto(discount: Discount) -> DiscountDTO:
    discount_dto = DiscountDTO(
        home_operators=discount.home_operators,
        partner_operators=discount.partner_operators,
        direction=discount.direction,
        service_types=discount.service_types,
        start_date=Month.create_from_date(discount.period.start_date),
        end_date=Month.create_from_date(discount.period.end_date),
        model_type=discount.model_type,
        currency_code=discount.currency_code,
        tax_type=discount.tax_type,
        volume_type=discount.volume_type,
        settlement_method=discount.settlement_method,
        call_destinations=discount.call_destinations,
        called_countries=discount.called_countries,
        traffic_segments=discount.traffic_segments,
        imsi_count_type=discount.imsi_count_type,
        qualifying_rule=discount.qualifying_rule,
        above_commitment_rate=discount.above_commitment_rate,
        inbound_market_share=discount.inbound_market_share,
        commitment_distribution_parameters=discount.commitment_distribution_parameters,
        parameters=tuple(from_discount_parameter_to_dto(p) for p in discount.parameters),
    )

    return discount_dto
