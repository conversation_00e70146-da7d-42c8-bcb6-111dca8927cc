from nga.apps.agreements.enums import AgreementStatusEnum
from nga.core.exceptions import BaseNGAException

__all__ = [
    "AgreementIntersectionError",
    "AgreementValidationError",
    "AgreementNegotiatorDoesNotExist",
    "DiscountDoesNotExist",
    "DiscountValidationError",
    "InvalidAgreementStatusTransition",
    "AgreementDoesNotExist",
    "BudgetAgreementDoesNotExist",
    "RenewedAgreementPeriodOverrideBudgetPeriodError",
    "WrongIncludePremiumInCommitment",
]


class AgreementValidationError(BaseNGAException):
    """Agreement validation exception."""


class AgreementIntersectionError(BaseNGAException):
    _template = "BudgetAgreement id={agreement_id} intersects other agreements"

    def __init__(self, agreement_id: int) -> None:
        msg = self._template.format(agreement_id=agreement_id)
        super().__init__(msg)


class DiscountValidationError(BaseNGAException):
    """Raised when Discount specification rule is violated."""


class DiscountDoesNotExist(BaseNGAException):
    _template = "Discount does not exist id={discount_id}"

    def __init__(self, discount_id: int) -> None:
        msg = self._template.format(discount_id=discount_id)
        super().__init__(msg)


class AgreementNegotiatorDoesNotExist(BaseNGAException):
    _template = "Agreement Negotiator does not exist id={agreement_negotiator_id}"

    def __init__(self, agreement_negotiator_id: int) -> None:
        msg = self._template.format(agreement_negotiator_id=agreement_negotiator_id)
        super().__init__(msg)


class InvalidAgreementStatusTransition(BaseNGAException):
    def __init__(self, next_statuses: tuple[AgreementStatusEnum, ...]) -> None:
        next_statuses_msg = ",".join(s.name for s in next_statuses)

        msg = f"Invalid status transition. Next statuses are: {next_statuses_msg}"

        super().__init__(msg)


class AgreementDoesNotExist(BaseNGAException):
    _template = "Agreement does not exist id={agreement_id}"

    def __init__(self, agreement_id: int) -> None:
        msg = self._template.format(agreement_id=agreement_id)
        super().__init__(msg)


class BudgetAgreementDoesNotExist(BaseNGAException):
    _template = "BudgetAgreement does not exist id={agreement_id}"

    def __init__(self, agreement_id: int) -> None:
        msg = self._template.format(agreement_id=agreement_id)
        super().__init__(msg)


class WrongIncludePremiumInCommitment(BaseNGAException):
    default_message = "`Include Premium in Commitment` must be True if is `Include Premium` is True"


class RenewedAgreementPeriodOverrideBudgetPeriodError(BaseNGAException):
    default_message = "Renewed Agreement period is out of Budget period"
