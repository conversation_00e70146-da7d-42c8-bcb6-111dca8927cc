from abc import ABC, abstractmethod
from typing import Optional

from nga.apps.agreements.domain.models import AgreementNegotiator

__all__ = [
    "AbstractAgreementNegotiatorRepository",
]


class AbstractAgreementNegotiatorRepository(ABC):
    @abstractmethod
    def get_many(self, negotiator_ids: Optional[list[int]] = None) -> list[AgreementNegotiator]:
        """Return list of Agreement Negotiators."""

    @abstractmethod
    def get_by_id(self, agreement_negotiator_id: int) -> AgreementNegotiator:
        """Return AgreementNegotiator by provided ID."""
