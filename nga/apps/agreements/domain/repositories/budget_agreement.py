from abc import ABC, abstractmethod
from datetime import date
from typing import Optional, Sequence

from nga.apps.agreements.domain.dto import BudgetAgreementCreateDTO
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.budgets.domain.dto import BudgetParametersFilters
from nga.core.types import DatePeriod

__all__ = [
    "AbstractBudgetAgreementRepository",
]


class AbstractBudgetAgreementRepository(ABC):
    @abstractmethod
    def get_many(
        self,
        *,
        budget_id: Optional[int] = None,
        budget_agreement_ids: Optional[list[int]] = None,
        budget_parameters: Optional[BudgetParametersFilters] = None,
        agreement_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        statuses: Optional[list[AgreementStatusEnum]] = None,
        only_active: Optional[bool] = None,
        only_modified: Optional[bool] = None,
    ) -> tuple[BudgetAgreement, ...]:
        """Returns agreements for specified budget."""

    @abstractmethod
    def get_by_id(self, budget_agreement_id: int) -> BudgetAgreement:
        """Returns agreement by provided id."""

    @abstractmethod
    def get_by_external_id(self, external_id: int, budget_id: int) -> Optional[BudgetAgreement]:
        """Returns agreement by provided external id and budget id."""

    @abstractmethod
    def get_by_parent_id(self, parent_id: int, budget_id: int) -> Optional[BudgetAgreement]:
        """Returns agreement by provided parent id and budget id."""

    @abstractmethod
    def create(self, agreement_dto: BudgetAgreementCreateDTO, budget_id: int) -> BudgetAgreement:
        """Creates agreement in a budget."""

    @abstractmethod
    def delete_by_id(self, budget_agreement_id: int, *, for_all_budgets: bool = False) -> None:
        """Deletes agreement."""

    @abstractmethod
    def copy(self, budget_agreement: BudgetAgreement, target_budget_id: int) -> BudgetAgreement:
        """Copies Agreement to target Budget."""

    @abstractmethod
    def save(self, budget_agreement: BudgetAgreement) -> BudgetAgreement:
        """Persists agreement to storage."""

    @abstractmethod
    def update_many(self, budget_agreements: Sequence[BudgetAgreement]) -> None:
        """Persists collection of agreements to storage. Currently, persists only applied_at field."""

    @abstractmethod
    def has_intersection(
        self,
        budget_agreement: BudgetAgreement,
        with_statuses: Optional[Sequence[AgreementStatusEnum]] = None,
        with_active: Optional[bool] = None,
    ) -> bool:
        """Returns boolean that marks whether agreement is intersected with other agreements."""

    @abstractmethod
    def get_intersected_many(
        self,
        budget_id: int,
        home_operators: list[int],
        partner_operators: list[int],
        period: DatePeriod,
        excluded_id: Optional[int] = None,
        with_statuses: Optional[Sequence[AgreementStatusEnum]] = None,
        with_active: Optional[bool] = None,
    ) -> tuple[BudgetAgreement, ...]:
        """Returns intersected Budget Agreements by Home and Partner operators, Period and Budget ID."""

    @abstractmethod
    def count(
        self,
        budget_id: int,
        *,
        budget_parameters: Optional[BudgetParametersFilters] = None,
        is_active: Optional[bool] = None,
    ) -> int:
        """Returns number of agreements by provided options."""
