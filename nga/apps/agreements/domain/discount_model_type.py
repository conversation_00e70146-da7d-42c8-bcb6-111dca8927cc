from typing import Optional

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import (
    AbstractDiscountSetupSpecification,
    AYCEDiscountSetupSpecification,
    BackToFirstDiscountSetupSpecification,
    BalancedUnbalancedSREDiscountSetupSpecification,
    BalancedUnbalancedSteppedTieredDiscountSetupSpecification,
    PMPIAboveThresholdDiscountSetupSpecification,
    PMPIBackToFirstDiscountSetupSpecification,
    PMPIDiscountSetupSpecification,
    PMPISteppedTieredDiscountSetupSpecification,
    PMPIWithIncrementalChargingDiscountSetupSpecification,
    SoPFinancialDiscountSetupSpecification,
    SoPTrafficSREDiscountSetupSpecification,
    SoPTrafficSteppedTieredDiscountSetupSpecification,
    SREDiscountSetupSpecification,
    SteppedTieredDiscountSetupSpecification,
)
from nga.apps.agreements.domain.specifications.utils import discount_fulfills_spec
from nga.apps.agreements.enums import DiscountModelTypeEnum


def evaluate_discount_model_type(discount: Discount) -> Optional[DiscountModelTypeEnum]:
    def is_verified_by(discount_setup_spec: AbstractDiscountSetupSpecification) -> bool:
        return discount_fulfills_spec(discount, discount_setup_spec)

    if is_verified_by(SREDiscountSetupSpecification()):
        return DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE

    if is_verified_by(SteppedTieredDiscountSetupSpecification()):
        return DiscountModelTypeEnum.STEPPED_TIERED

    if is_verified_by(SoPTrafficSREDiscountSetupSpecification()):
        return DiscountModelTypeEnum.SEND_OR_PAY_TRAFFIC_SINGLE_RATE_EFFECTIVE

    if is_verified_by(SoPTrafficSteppedTieredDiscountSetupSpecification()):
        return DiscountModelTypeEnum.SEND_OR_PAY_TRAFFIC_STEPPED_TIERED

    if is_verified_by(SoPFinancialDiscountSetupSpecification()):
        return DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL

    if is_verified_by(BalancedUnbalancedSREDiscountSetupSpecification()):
        return DiscountModelTypeEnum.BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE

    if is_verified_by(BalancedUnbalancedSteppedTieredDiscountSetupSpecification()):
        return DiscountModelTypeEnum.BALANCED_UNBALANCED_STEPPED_TIERED

    if is_verified_by(BackToFirstDiscountSetupSpecification()):
        return DiscountModelTypeEnum.BACK_TO_FIRST

    if is_verified_by(PMPIDiscountSetupSpecification()):
        return DiscountModelTypeEnum.PER_MONTH_PER_IMSI

    if is_verified_by(PMPIAboveThresholdDiscountSetupSpecification()):
        return DiscountModelTypeEnum.PER_MONTH_PER_IMSI_ABOVE_THRESHOLD

    if is_verified_by(PMPISteppedTieredDiscountSetupSpecification()):
        return DiscountModelTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED

    if is_verified_by(PMPIWithIncrementalChargingDiscountSetupSpecification()):
        return DiscountModelTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING

    if is_verified_by(PMPIBackToFirstDiscountSetupSpecification()):
        return DiscountModelTypeEnum.PER_MONTH_PER_IMSI_BACK_TO_FIRST

    if is_verified_by(AYCEDiscountSetupSpecification()):
        return DiscountModelTypeEnum.ALL_YOU_CAN_EAT

    return None
