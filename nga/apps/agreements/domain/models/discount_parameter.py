from dataclasses import dataclass
from decimal import Decimal
from typing import Optional

from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
)


@dataclass
class DiscountParameter:
    discount_id: int

    calculation_type: DiscountCalculationTypeEnum

    basis: Optional[DiscountBasisEnum]
    basis_value: Optional[Decimal]

    balancing: Optional[DiscountBalancingEnum]

    bound_type: Optional[DiscountBoundTypeEnum]
    lower_bound: Optional[Decimal]
    upper_bound: Optional[Decimal]

    toll_rate: Optional[Decimal]

    airtime_rate: Optional[Decimal]

    fair_usage_rate: Optional[Decimal]
    fair_usage_threshold: Optional[Decimal]

    access_fee_rate: Optional[Decimal]
    incremental_rate: Optional[Decimal]

    def __copy__(self) -> "DiscountParameter":
        return DiscountParameter(
            discount_id=self.discount_id,
            calculation_type=self.calculation_type,
            basis=self.basis,
            basis_value=self.basis_value,
            balancing=self.balancing,
            bound_type=self.bound_type,
            lower_bound=self.lower_bound,
            upper_bound=self.upper_bound,
            toll_rate=self.toll_rate,
            airtime_rate=self.airtime_rate,
            fair_usage_rate=self.fair_usage_rate,
            fair_usage_threshold=self.fair_usage_threshold,
            access_fee_rate=self.access_fee_rate,
            incremental_rate=self.incremental_rate,
        )
