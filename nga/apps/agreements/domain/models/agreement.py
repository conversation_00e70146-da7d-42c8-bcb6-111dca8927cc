from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from nga.apps.agreements.enums import AgreementStatusEnum
from nga.core.types import DatePeriod
from nga.internal.domain import Aggregate


@dataclass(kw_only=True)
class Agreement(Aggregate):
    id: int
    external_id: Optional[int]

    parent_id: Optional[int]

    name: str
    status: AgreementStatusEnum

    home_operators: list[int]
    partner_operators: list[int]

    period: DatePeriod

    include_satellite: bool

    include_premium: bool

    is_rolling: bool

    include_premium_in_commitment: bool

    negotiator_id: Optional[int]

    updated_at: datetime
