from dataclasses import dataclass
from decimal import Decimal
from typing import Optional

from nga.apps.agreements.enums import DiscountDirectionEnum, DiscountQualifyingBasisEnum
from nga.core.enums import ServiceTypeEnum, VolumeTypeEnum


@dataclass
class DiscountQualifyingRule:
    direction: DiscountDirectionEnum

    service_types: tuple[ServiceTypeEnum, ...]

    basis: DiscountQualifyingBasisEnum

    volume_type: VolumeTypeEnum  # the same value as discount has to allow to use rule independently of the discount

    lower_bound: Decimal

    upper_bound: Optional[Decimal]

    def is_volume_within_bounds(self, volume: Decimal) -> bool:
        """Check whether volume fits in the defined bounds."""

        if self.upper_bound is None:
            return self.lower_bound <= volume

        return self.lower_bound <= volume < self.upper_bound
