from dataclasses import dataclass
from decimal import Decimal
from typing import Optional

from nga.apps.agreements.domain.models.discount import CommitmentDistributionParameter
from nga.apps.agreements.domain.value_objects import DiscountQualifyingRule
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import DatePeriod, Month

__all__ = [
    "BudgetAgreementCreateDTO",
    "DiscountDTO",
    "DiscountParameterDTO",
]


@dataclass
class BudgetAgreementCreateDTO:
    name: str

    home_operators: list[int]
    partner_operators: list[int]

    period: DatePeriod

    include_satellite: bool
    include_premium: bool
    include_premium_in_commitment: bool

    is_rolling: bool

    negotiator_id: Optional[int]
    external_id: Optional[int] = None


@dataclass
class DiscountParameterDTO:
    calculation_type: DiscountCalculationTypeEnum

    basis: Optional[DiscountBasisEnum]
    basis_value: Optional[Decimal]

    balancing: Optional[DiscountBalancingEnum]

    bound_type: Optional[DiscountBoundTypeEnum]
    lower_bound: Optional[Decimal]
    upper_bound: Optional[Decimal]

    toll_rate: Optional[Decimal]
    airtime_rate: Optional[Decimal]

    fair_usage_rate: Optional[Decimal]
    fair_usage_threshold: Optional[Decimal]

    access_fee_rate: Optional[Decimal]
    incremental_rate: Optional[Decimal]


@dataclass
class DiscountDTO:
    home_operators: tuple[int, ...]
    partner_operators: tuple[int, ...]

    direction: DiscountDirectionEnum
    service_types: tuple[ServiceTypeEnum, ...]

    start_date: Month
    end_date: Month

    model_type: Optional[DiscountModelTypeEnum]

    currency_code: str

    tax_type: TaxTypeEnum
    volume_type: VolumeTypeEnum

    settlement_method: DiscountSettlementMethodEnum

    call_destinations: Optional[tuple[CallDestinationEnum, ...]]
    called_countries: Optional[tuple[int, ...]]
    traffic_segments: Optional[tuple[int, ...]]

    imsi_count_type: Optional[IMSICountTypeEnum]

    qualifying_rule: Optional[DiscountQualifyingRule]

    parameters: tuple["DiscountParameterDTO", ...]

    inbound_market_share: Optional[Decimal]

    commitment_distribution_parameters: Optional[tuple[CommitmentDistributionParameter, ...]] = None

    above_commitment_rate: Optional[Decimal] = None

    sub_discounts: Optional[tuple[int, ...]] = None

    financial_threshold: Optional[Decimal] = None

    above_financial_threshold_rate: Optional[Decimal] = None
