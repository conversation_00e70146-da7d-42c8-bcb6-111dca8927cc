from django.urls import path

from nga.apps.agreements.api.views.discount_api import DiscountAPIView
from nga.apps.agreements.api.views.discount_collection_api import DiscountCollectionAPIView
from nga.apps.agreements.api.views.sub_discount_api import SubDiscountAPIView
from nga.apps.agreements.api.views.sub_discount_update import SubDiscountPatchUpdateAPIView

urlpatterns = [
    path(
        r"discounts/<int:discount_id>/sub-discounts/<int:sub_discount_id>",
        SubDiscountPatchUpdateAPIView.as_view(),
        name="sub_discount",
    ),
    path(r"discounts/<int:discount_id>/sub-discounts", SubDiscountAPIView.as_view(), name="sub_discounts"),
    path(r"discounts/<int:discount_id>", DiscountAPIView.as_view(), name="discount"),
    path(r"discounts", DiscountCollectionAPIView.as_view(), name="agreement_discounts"),
]
