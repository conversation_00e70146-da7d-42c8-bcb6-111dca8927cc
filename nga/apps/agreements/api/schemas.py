from dataclasses import dataclass
from datetime import date, datetime
from decimal import Decimal
from typing import Optional

from nga.apps.agreements.domain.models import AgreementNegotiator, DiscountParameter
from nga.apps.agreements.dto import BudgetAgreementError
from nga.apps.agreements.enums import (
    AgreementCalculationStatusEnum,
    AgreementIntersectionTypeEnum,
    AgreementStatusEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.references.api.schemas import TrafficSegmentSchema
from nga.apps.references.domain.models import Country, Operator
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import DatePeriod


@dataclass
class BudgetAgreementSchema:
    id: int
    budget_id: int
    agreement_id: int

    name: str
    status: AgreementStatusEnum
    next_statuses: list[AgreementStatusEnum]

    calculation_status: AgreementCalculationStatusEnum

    start_date: date
    end_date: date

    is_active: bool

    home_operators: list[Operator]
    partner_operators: list[Operator]
    partner_countries: list[Country]

    intersection_types: list[AgreementIntersectionTypeEnum]

    include_satellite: bool
    include_premium: bool
    include_premium_in_commitment: bool

    is_rolling: bool

    negotiator: Optional[AgreementNegotiator]

    updated_at: datetime
    applied_at: Optional[datetime]


@dataclass
class AgreementCreateSchema:
    name: str
    home_operators: list[int]
    partner_operators: list[int]
    start_date: date
    end_date: date
    include_satellite: bool
    include_premium: bool
    include_premium_in_commitment: bool
    is_rolling: bool
    negotiator: Optional[AgreementNegotiator]


@dataclass
class AgreementCloneSchema:
    source_agreement_id: int
    new_name: str


@dataclass
class AgreementBulkCloneSchema:
    agreement_ids: list[int]


@dataclass
class AgreementUpdateSchema:
    name: str
    status: AgreementStatusEnum

    include_satellite: bool
    include_premium: bool
    include_premium_in_commitment: bool

    is_rolling: bool

    negotiator: Optional[AgreementNegotiator] = None


@dataclass
class AgreementBulkDeleteSchema:
    agreement_ids: list[int]


@dataclass
class AgreementActionSchema:
    agreement_ids: list[int]


@dataclass
class AgreementActionResponseSchema:
    id: int


@dataclass
class AgreementCopySchema:
    agreement_ids: list[int]


@dataclass
class AgreementBulkCopySchema:
    source_budget_id: int
    only_active: Optional[bool]


@dataclass
class AgreementBulkStatusChangeSchema:
    initial_status: AgreementStatusEnum
    target_status: AgreementStatusEnum
    agreement_ids: Optional[list[int]]
    only_active: Optional[bool]


@dataclass
class AgreementErrorResponseSchema:
    failed_agreements: list[BudgetAgreementError]


@dataclass
class DiscountQualifyingRuleSchema:
    direction: DiscountDirectionEnum

    service_types: tuple[ServiceTypeEnum, ...]

    basis: DiscountQualifyingBasisEnum

    lower_bound: Decimal

    upper_bound: Optional[Decimal]


@dataclass
class CommitmentDistributionParameterSchema:
    home_operators: tuple[Operator, ...]
    partner_operators: tuple[Operator, ...]

    charge: Decimal


@dataclass
class DiscountSchema:
    id: int
    agreement_id: int

    home_operators: tuple[Operator, ...]
    partner_operators: tuple[Operator, ...]

    direction: DiscountDirectionEnum
    service_types: tuple[ServiceTypeEnum, ...]
    period: DatePeriod

    model_type: Optional[DiscountModelTypeEnum]

    imsi_count_type: Optional[IMSICountTypeEnum]

    currency_code: str

    tax_type: TaxTypeEnum
    volume_type: VolumeTypeEnum

    settlement_method: DiscountSettlementMethodEnum

    call_destinations: Optional[tuple[CallDestinationEnum, ...]]
    called_countries: Optional[tuple[Country, ...]]
    traffic_segments: Optional[tuple[TrafficSegmentSchema, ...]]

    qualifying_rule: Optional[DiscountQualifyingRuleSchema]

    parent_id: Optional[int]

    above_commitment_rate: Optional[Decimal]

    inbound_market_share: Optional[Decimal]

    commitment_distribution_parameters: Optional[tuple[CommitmentDistributionParameterSchema, ...]]

    parameters: tuple["DiscountParameter", ...]

    sub_discounts: tuple["DiscountSchema", ...]

    financial_threshold: Optional[Decimal]

@dataclass
class AgreementNegotiatorSchema:
    id: int
    name: str


@dataclass
class AgreementFilterParameters:
    start_date_min: date
    start_date_max: date

    end_date_min: date
    end_date_max: date

    calculation_statuses: list[AgreementCalculationStatusEnum]

    statuses: list[AgreementStatusEnum]
    negotiators: list[int]

    is_active: list[bool]

    include_satellite: list[bool]
    include_premium: list[bool]
    include_premium_in_commitment: list[bool]

    is_rolling: list[bool]

    has_intersection: list[bool]

    updated_at_min: datetime
    updated_at_max: datetime

    applied_at_min: Optional[datetime]
    applied_at_max: Optional[datetime]
