from rest_framework import serializers
from rest_framework.utils.serializer_helpers import Return<PERSON>ist
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.agreements.api.schemas import (
    CommitmentDistributionParameterSchema,
    DiscountQualifyingRuleSchema,
    DiscountSchema,
)
from nga.apps.agreements.api.serializers.fields import BoundDecimalField, DiscountDecimalField
from nga.apps.agreements.domain.models import DiscountParameter
from nga.apps.agreements.domain.models.discount import CommitmentDistributionParameter
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.common.serializer_fields import EnumChoiceField, YearMonthField
from nga.apps.references.api.serializers import CountryORMSerializer, OperatorSerializer, TrafficSegmentSchemaSerializer
from nga.core.enums import CallD<PERSON><PERSON><PERSON><PERSON>, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum


class DiscountQualifyingRuleSerializer(DataclassSerializer[DiscountQualifyingRuleSchema]):

    direction = EnumChoiceField(enum_class=DiscountDirectionEnum)
    service_types = serializers.ListField(child=EnumChoiceField(enum_class=ServiceTypeEnum))

    basis = EnumChoiceField(enum_class=DiscountQualifyingBasisEnum)
    lower_bound = BoundDecimalField()
    upper_bound = BoundDecimalField(required=False, allow_null=True, default=None)

    class Meta:
        dataclass = DiscountQualifyingRuleSchema
        fields = (
            "direction",
            "service_types",
            "basis",
            "lower_bound",
            "upper_bound",
        )


class CommitmentDistributionParameterSerializer(DataclassSerializer[CommitmentDistributionParameter]):
    home_operators = serializers.ListField(child=serializers.IntegerField(), allow_empty=False)
    partner_operators = serializers.ListField(child=serializers.IntegerField(), allow_empty=False)

    charge = BoundDecimalField()

    class Meta:
        dataclass = CommitmentDistributionParameter
        fields = (
            "home_operators",
            "partner_operators",
            "charge",
        )


class CommitmentDistributionParameterSchemaSerializer(DataclassSerializer[CommitmentDistributionParameterSchema]):
    home_operators = OperatorSerializer(many=True)
    partner_operators = OperatorSerializer(many=True)

    charge = BoundDecimalField()

    class Meta:
        dataclass = CommitmentDistributionParameterSchema
        fields = (
            "home_operators",
            "partner_operators",
            "charge",
        )


class DiscountParameterSerializer(DataclassSerializer[DiscountParameter]):
    calculation_type = EnumChoiceField(enum_class=DiscountCalculationTypeEnum, required=False)

    basis = EnumChoiceField(enum_class=DiscountBasisEnum, required=False)

    basis_value = DiscountDecimalField(required=False)

    balancing = EnumChoiceField(enum_class=DiscountBalancingEnum, required=False)

    bound_type = EnumChoiceField(enum_class=DiscountBoundTypeEnum, required=False)

    lower_bound = BoundDecimalField(required=False)

    upper_bound = BoundDecimalField(required=False)

    toll_rate = DiscountDecimalField(required=False)

    airtime_rate = DiscountDecimalField(required=False)

    fair_usage_rate = DiscountDecimalField(required=False)

    fair_usage_threshold = BoundDecimalField(required=False)

    access_fee_rate = DiscountDecimalField(required=False)

    incremental_rate = DiscountDecimalField(required=False)

    class Meta:
        dataclass = DiscountParameter
        fields = (
            "discount_id",
            "calculation_type",
            "basis",
            "basis_value",
            "balancing",
            "bound_type",
            "lower_bound",
            "upper_bound",
            "toll_rate",
            "airtime_rate",
            "fair_usage_rate",
            "fair_usage_threshold",
            "access_fee_rate",
            "incremental_rate",
        )


class BaseDiscountSchemaSerializer(DataclassSerializer[DiscountSchema]):
    home_operators = OperatorSerializer(many=True)
    partner_operators = OperatorSerializer(many=True)

    direction = EnumChoiceField(enum_class=DiscountDirectionEnum)

    service_types = serializers.ListField(child=EnumChoiceField(enum_class=ServiceTypeEnum))

    start_date = YearMonthField(source="period.start_date")
    end_date = YearMonthField(source="period.end_date")

    model_type = EnumChoiceField(enum_class=DiscountModelTypeEnum, required=False)

    tax_type = EnumChoiceField(enum_class=TaxTypeEnum, required=False)
    volume_type = EnumChoiceField(enum_class=VolumeTypeEnum, required=False)
    settlement_method = EnumChoiceField(enum_class=DiscountSettlementMethodEnum, required=False)

    call_destinations = serializers.ListField(child=EnumChoiceField(enum_class=CallDestinationEnum))

    called_countries = CountryORMSerializer(many=True)

    traffic_segments = TrafficSegmentSchemaSerializer(many=True)

    imsi_count_type = EnumChoiceField(enum_class=IMSICountTypeEnum)

    qualifying_rule = DiscountQualifyingRuleSerializer(required=False)

    inbound_market_share = BoundDecimalField(required=False)

    parameters = DiscountParameterSerializer(many=True)


class SubDiscountSchemaSerializer(BaseDiscountSchemaSerializer):

    above_commitment_rate = DiscountDecimalField(required=False, allow_null=True, default=None)

    class Meta:
        dataclass = DiscountSchema
        fields = (
            "id",
            "agreement_id",
            "home_operators",
            "partner_operators",
            "direction",
            "start_date",
            "end_date",
            "service_types",
            "model_type",
            "currency_code",
            "tax_type",
            "volume_type",
            "settlement_method",
            "call_destinations",
            "called_countries",
            "traffic_segments",
            "imsi_count_type",
            "qualifying_rule",
            "parent_id",
            "inbound_market_share",
            "above_commitment_rate",
            "parameters",
        )


class DiscountSchemaSerializer(BaseDiscountSchemaSerializer):

    commitment_distribution_parameters = CommitmentDistributionParameterSchemaSerializer(many=True, required=False)

    sub_discounts = serializers.SerializerMethodField()

    class Meta:
        dataclass = DiscountSchema
        fields = (
            "id",
            "agreement_id",
            "home_operators",
            "partner_operators",
            "direction",
            "start_date",
            "end_date",
            "service_types",
            "model_type",
            "currency_code",
            "tax_type",
            "volume_type",
            "settlement_method",
            "call_destinations",
            "called_countries",
            "traffic_segments",
            "imsi_count_type",
            "qualifying_rule",
            "inbound_market_share",
            "commitment_distribution_parameters",
            "parameters",
            "sub_discounts",
            "financial_threshold",
        )

    @classmethod
    def get_sub_discounts(cls, obj: DiscountSchema) -> ReturnList:
        return SubDiscountSchemaSerializer(obj.sub_discounts, many=True).data
