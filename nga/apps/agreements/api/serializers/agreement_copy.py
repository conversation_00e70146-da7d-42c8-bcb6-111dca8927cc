from rest_framework import serializers
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.agreements.api.schemas import AgreementBulkCopySchema, AgreementCopySchema


class AgreementCopySchemaSerializer(DataclassSerializer[AgreementCopySchema]):
    class Meta:
        dataclass = AgreementCopySchema
        fields = ("agreement_ids",)


class AgreementBulkCopySchemaSerializer(DataclassSerializer[AgreementBulkCopySchema]):
    only_active = serializers.BooleanField(required=False, default=None, allow_null=True)

    class Meta:
        dataclass = AgreementBulkCopySchema
        fields = ("source_budget_id", "only_active")
