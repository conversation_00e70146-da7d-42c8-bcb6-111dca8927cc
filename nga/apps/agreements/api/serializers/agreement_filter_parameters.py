from dependency_injector.wiring import Closing, Provide, inject
from rest_framework import serializers
from rest_framework.utils.serializer_helpers import ReturnList
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.agreements.api.schemas import AgreementFilterParameters
from nga.apps.agreements.domain.repositories import AbstractAgreementNegotiatorRepository
from nga.apps.agreements.enums import AgreementCalculationStatusEnum, AgreementStatusEnum
from nga.apps.common.serializer_fields import EnumChoiceField, YearMonthField

from .agreement_negotiator import AgreementNegotiatorSchemaSerializer


class AgreementFilterParametersSchemaSerializer(DataclassSerializer[AgreementFilterParameters]):
    start_date_min = YearMonthField()
    start_date_max = YearMonthField()

    end_date_min = YearMonthField()
    end_date_max = YearMonthField()

    calculation_statuses = serializers.ListField(
        child=EnumChoiceField(enum_class=AgreementCalculationStatusEnum),
        allow_empty=False,
    )

    statuses = serializers.ListField(child=EnumChoiceField(enum_class=AgreementStatusEnum), allow_empty=False)

    negotiators = serializers.SerializerMethodField("get_negotiators")

    is_active = serializers.ListField(child=serializers.BooleanField(), allow_empty=False)

    include_satellite = serializers.ListField(child=serializers.BooleanField(), allow_empty=False)
    include_premium = serializers.ListField(child=serializers.BooleanField(), allow_empty=False)
    include_premium_in_commitment = serializers.ListField(child=serializers.BooleanField(), allow_empty=False)

    is_rolling = serializers.ListField(child=serializers.BooleanField(), allow_empty=False)

    has_intersection = serializers.ListField(child=serializers.BooleanField(), allow_empty=False)

    updated_at_min = serializers.DateTimeField()
    updated_at_max = serializers.DateTimeField()

    applied_at_min = serializers.DateTimeField(allow_null=True)
    applied_at_max = serializers.DateTimeField(allow_null=True)

    class Meta:
        dataclass = AgreementFilterParameters
        fields = (
            "start_date_min",
            "start_date_max",
            "end_date_min",
            "end_date_max",
            "calculation_statuses",
            "statuses",
            "negotiators",
            "is_active",
            "include_satellite",
            "include_premium",
            "include_premium_in_commitment",
            "is_rolling",
            "has_intersection",
            "updated_at_min",
            "updated_at_max",
            "applied_at_min",
            "applied_at_max",
        )

    @inject
    def get_negotiators(
        self,
        instance: AgreementFilterParameters,
        agreement_negotiator_repository: AbstractAgreementNegotiatorRepository = Closing[
            Provide["agreement_negotiator_repository"]
        ],
    ) -> ReturnList:
        if not instance.negotiators:
            negotiators = []

        else:
            agreement_negotiators = agreement_negotiator_repository.get_many(negotiator_ids=instance.negotiators)
            agreement_negotiators_map = {a.id: a for a in agreement_negotiators}

            negotiators = [agreement_negotiators_map[negotiator_id] for negotiator_id in instance.negotiators]

        return AgreementNegotiatorSchemaSerializer(negotiators, many=True).data
