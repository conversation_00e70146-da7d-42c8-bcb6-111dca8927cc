from rest_framework import serializers
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.agreements.api.schemas import AgreementBulkStatusChangeSchema
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.common.serializer_fields import EnumChoiceField


class AgreementBulkStatusChangeSchemaSerializer(DataclassSerializer[AgreementBulkStatusChangeSchema]):
    initial_status = EnumChoiceField(enum_class=AgreementStatusEnum)
    target_status = EnumChoiceField(enum_class=AgreementStatusEnum)

    agreement_ids = serializers.ListField(
        child=serializers.IntegerField(min_value=1),
        required=False,
        allow_null=True,
        default=None,
    )

    only_active = serializers.BooleanField(required=False, default=None, allow_null=True)

    class Meta:
        dataclass = AgreementBulkStatusChangeSchema
        fields = (
            "initial_status",
            "target_status",
            "agreement_ids",
            "only_active",
        )
