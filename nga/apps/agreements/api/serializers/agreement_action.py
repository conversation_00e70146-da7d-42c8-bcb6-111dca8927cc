from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.agreements.api.schemas import AgreementActionSchema
from nga.apps.agreements.domain.models import BudgetAgreement


class AgreementActionSchemaSerializer(DataclassSerializer[AgreementActionSchema]):
    class Meta:
        dataclass = AgreementActionSchema
        fields = ("agreement_ids",)


class AgreementActionResponseSchemaSerializer(DataclassSerializer[BudgetAgreement]):
    class Meta:
        dataclass = BudgetAgreement
        fields = ("id",)
