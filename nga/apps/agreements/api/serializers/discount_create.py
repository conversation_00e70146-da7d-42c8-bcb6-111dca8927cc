from rest_framework import serializers
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.agreements.api.serializers.discount_detail import (
    CommitmentDistributionParameterSerializer,
    DiscountQualifyingRuleSerializer,
)
from nga.apps.agreements.api.serializers.fields import BoundDecimalField, DiscountDecimalField
from nga.apps.agreements.domain.dto import DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.common.serializer_fields import EnumChoice<PERSON>ield, MonthField
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum


class DiscountParameterCreateSerializer(DataclassSerializer[DiscountParameterDTO]):
    calculation_type = EnumChoiceField(enum_class=DiscountCalculationTypeEnum)

    basis = EnumChoiceField(enum_class=DiscountBasisEnum, required=False, allow_null=True, default=None)

    basis_value = DiscountDecimalField(required=False, allow_null=True, default=None)

    balancing = EnumChoiceField(enum_class=DiscountBalancingEnum, required=False, allow_null=True, default=None)

    bound_type = EnumChoiceField(enum_class=DiscountBoundTypeEnum, required=False, allow_null=True, default=None)

    lower_bound = BoundDecimalField(required=False, allow_null=True, default=None)

    upper_bound = BoundDecimalField(required=False, allow_null=True, default=None)

    toll_rate = DiscountDecimalField(required=False, allow_null=True, default=None)

    airtime_rate = DiscountDecimalField(required=False, allow_null=True, default=None)

    fair_usage_rate = DiscountDecimalField(required=False, allow_null=True, default=None)

    fair_usage_threshold = BoundDecimalField(required=False, allow_null=True, default=None)

    access_fee_rate = DiscountDecimalField(required=False, allow_null=True, default=None)

    incremental_rate = DiscountDecimalField(required=False, allow_null=True, default=None)

    class Meta:
        dataclass = DiscountParameterDTO
        fields = (
            "calculation_type",
            "basis",
            "basis_value",
            "balancing",
            "bound_type",
            "lower_bound",
            "upper_bound",
            "toll_rate",
            "airtime_rate",
            "fair_usage_rate",
            "fair_usage_threshold",
            "access_fee_rate",
            "incremental_rate",
        )


class DiscountCreateSerializer(DataclassSerializer[DiscountDTO]):
    home_operators = serializers.ListField(child=serializers.IntegerField(), allow_empty=False)
    partner_operators = serializers.ListField(child=serializers.IntegerField(), allow_empty=False)

    direction = EnumChoiceField(enum_class=DiscountDirectionEnum)

    service_types = serializers.ListField(child=EnumChoiceField(enum_class=ServiceTypeEnum), allow_empty=False)

    start_date = MonthField()
    end_date = MonthField()

    model_type = EnumChoiceField(enum_class=DiscountModelTypeEnum, allow_null=False, required=True)

    tax_type = EnumChoiceField(enum_class=TaxTypeEnum)
    volume_type = EnumChoiceField(enum_class=VolumeTypeEnum)
    settlement_method = EnumChoiceField(enum_class=DiscountSettlementMethodEnum)

    call_destinations = serializers.ListField(
        child=EnumChoiceField(enum_class=CallDestinationEnum),
        required=False,
        allow_null=True,
        allow_empty=True,
        default=None,
    )

    called_countries = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_null=True,
        allow_empty=True,
        default=None,
    )

    traffic_segments = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_null=True,
        allow_empty=True,
        default=None,
    )

    imsi_count_type = EnumChoiceField(
        enum_class=IMSICountTypeEnum,
        required=False,
        allow_null=True,
        default=None,
    )

    qualifying_rule = DiscountQualifyingRuleSerializer(
        required=False,
        allow_null=True,
        default=None,
    )

    inbound_market_share = BoundDecimalField(required=False, allow_null=True, default=None)

    commitment_distribution_parameters = serializers.ListField(
        child=CommitmentDistributionParameterSerializer(),
        required=False,
        allow_empty=True,
        allow_null=True,
        default=None,
    )

    parameters = DiscountParameterCreateSerializer(many=True, required=True, allow_empty=False)

    sub_discounts = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_empty=True,
        allow_null=True,
        default=None,
    )

    financial_threshold = BoundDecimalField(
        required=False,
        allow_null=True,
        default=None,
    )

    class Meta:
        dataclass = DiscountDTO
        fields = (
            "home_operators",
            "partner_operators",
            "direction",
            "start_date",
            "end_date",
            "service_types",
            "currency_code",
            "model_type",
            "tax_type",
            "volume_type",
            "settlement_method",
            "call_destinations",
            "called_countries",
            "traffic_segments",
            "imsi_count_type",
            "qualifying_rule",
            "inbound_market_share",
            "commitment_distribution_parameters",
            "parameters",
            "sub_discounts",
            "financial_threshold",
        )


class SubDiscountCreateSerializer(DiscountCreateSerializer):
    above_commitment_rate = DiscountDecimalField(required=False, allow_null=True, default=None)

    above_financial_threshold_rate = DiscountDecimalField(required=False, allow_null=True, default=None)

    class Meta:
        dataclass = DiscountDTO
        fields = (
            "home_operators",
            "partner_operators",
            "direction",
            "start_date",
            "end_date",
            "service_types",
            "currency_code",
            "model_type",
            "tax_type",
            "volume_type",
            "settlement_method",
            "call_destinations",
            "called_countries",
            "traffic_segments",
            "imsi_count_type",
            "qualifying_rule",
            "above_commitment_rate",
            "inbound_market_share",
            "parameters",
            "above_financial_threshold_rate",
        )
