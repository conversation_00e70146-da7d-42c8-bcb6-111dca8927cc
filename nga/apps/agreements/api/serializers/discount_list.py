from rest_framework import serializers


class DiscountListQuerySerializer(serializers.Serializer):

    home_operators = serializers.ListField(
        child=serializers.IntegerField(min_value=1),
        required=False,
        default=None,
    )
    partner_operators = serializers.ListField(
        child=serializers.IntegerField(min_value=1),
        required=False,
        default=None,
    )

    class Meta:
        fields = (
            "home_operators",
            "partner_operators",
        )
