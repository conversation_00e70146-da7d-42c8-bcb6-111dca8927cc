from rest_framework import serializers

from nga.apps.agreements.api.filters import BudgetAgreementAPIFilterSet, BudgetAgreementFilterSet
from nga.apps.agreements.enums import AgreementCalculationStatusEnum, AgreementStatusEnum
from nga.apps.common.serializer_fields import EnumChoiceField, YearMonthField
from nga.apps.common.serializer_utils import get_choices_for_sort_field
from nga.apps.common.serializers import BudgetQuerySerializer


class BaseAgreementListQuerySerializer(BudgetQuerySerializer):
    start_date_min = YearMonthField(required=False, default=None, allow_null=True)
    start_date_max = YearMonthField(required=False, default=None, allow_null=True)

    end_date_min = YearMonthField(required=False, default=None, allow_null=True)
    end_date_max = YearMonthField(required=False, default=None, allow_null=True)

    is_active = serializers.BooleanField(required=False, default=None, allow_null=True)

    include_satellite = serializers.BooleanField(required=False, default=None, allow_null=True)
    include_premium = serializers.BooleanField(required=False, default=None, allow_null=True)
    include_premium_in_commitment = serializers.BooleanField(required=False, default=None, allow_null=True)

    is_rolling = serializers.BooleanField(required=False, default=None, allow_null=True)

    calculation_statuses = serializers.ListField(
        child=EnumChoiceField(enum_class=AgreementCalculationStatusEnum),
        required=False,
        default=[],
    )

    statuses = serializers.ListField(
        child=EnumChoiceField(enum_class=AgreementStatusEnum),
        required=False,
        default=[],
    )
    negotiators = serializers.ListField(
        child=serializers.IntegerField(min_value=1),
        required=False,
        default=[],
    )

    updated_at_min = serializers.DateTimeField(required=False, default=None, allow_null=True)
    updated_at_max = serializers.DateTimeField(required=False, default=None, allow_null=True)

    applied_at_min = serializers.DateTimeField(required=False, default=None, allow_null=True)
    applied_at_max = serializers.DateTimeField(required=False, default=None, allow_null=True)


class AgreementListQuerySerializer(BaseAgreementListQuerySerializer):
    sort_field = serializers.ChoiceField(
        choices=get_choices_for_sort_field(BudgetAgreementAPIFilterSet),
        required=False,
        default=None,
        allow_null=True,
    )

    has_intersection = serializers.BooleanField(required=False, default=None, allow_null=True)

    class Meta:
        fields = (
            "sort_field",
            "home_operators",
            "partner_operators",
            "partner_countries",
            "start_date_min",
            "start_date_max",
            "end_date_min",
            "end_date_max",
            "is_active",
            "has_intersection",
            "calculation_statuses",
            "statuses",
            "negotiators",
            "updated_at_min",
            "updated_at_max",
            "applied_at_min",
            "applied_at_max",
        )


class AgreementListXlsxQuerySerializer(BaseAgreementListQuerySerializer):
    sort_field = serializers.ChoiceField(
        choices=get_choices_for_sort_field(BudgetAgreementFilterSet),
        required=False,
        default=None,
        allow_null=True,
    )

    class Meta:
        fields = (
            "sort_field",
            "home_operators",
            "partner_operators",
            "partner_countries",
            "start_date_min",
            "start_date_max",
            "end_date_min",
            "end_date_max",
            "is_active",
            "calculation_statuses",
            "statuses",
            "negotiators",
            "updated_at_min",
            "updated_at_max",
            "applied_at_min",
            "applied_at_max",
        )
