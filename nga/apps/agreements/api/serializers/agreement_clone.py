from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.agreements.api.schemas import AgreementBulkCloneSchema, AgreementCloneSchema


class AgreementCloneSchemaSerializer(DataclassSerializer[AgreementCloneSchema]):
    class Meta:
        dataclass = AgreementCloneSchema
        fields = ("source_agreement_id", "new_name")


class AgreementBulkCloneSchemaSerializer(DataclassSerializer[AgreementBulkCloneSchema]):
    class Meta:
        dataclass = AgreementBulkCloneSchema
        fields = ("agreement_ids",)
