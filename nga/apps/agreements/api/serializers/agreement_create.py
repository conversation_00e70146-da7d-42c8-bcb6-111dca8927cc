from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.agreements.api.schemas import AgreementCreateSchema
from nga.apps.common.serializer_fields import YearMonthField


class AgreementCreateSchemaSerializer(DataclassSerializer[AgreementCreateSchema]):
    home_operators = serializers.ListField(child=serializers.IntegerField(), allow_empty=False)
    partner_operators = serializers.ListField(child=serializers.IntegerField(), allow_empty=False)

    start_date = YearMonthField()
    end_date = YearMonthField()

    include_satellite = serializers.BooleanField()
    include_premium = serializers.BooleanField()
    include_premium_in_commitment = serializers.BooleanField()

    is_rolling = serializers.BooleanField()

    class Meta:
        dataclass = AgreementCreateSchema
        fields = (
            "name",
            "home_operators",
            "partner_operators",
            "start_date",
            "end_date",
            "negotiator",
            "include_satellite",
            "include_premium",
            "include_premium_in_commitment",
            "is_rolling",
        )

    def validate(self, data: AgreementCreateSchema) -> AgreementCreateSchema:
        data = super().validate(data)

        if data.start_date > data.end_date:
            raise ValidationError("start_date must be less then end_date")

        return data
