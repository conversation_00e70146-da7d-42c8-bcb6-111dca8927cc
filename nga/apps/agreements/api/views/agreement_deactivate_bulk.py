from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from nga.apps.budget_background_jobs.command_factories import ScheduleBudgetBackgroundJobCommandFactory
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.consts import SWAGGER_AGREEMENTS_TAG
from nga.apps.common.view_utils import get_budget_or_404


class AgreementDeactivateBulkAPIView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = None

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._budget_provider = budget_provider

    @swagger_auto_schema(tags=[SWAGGER_AGREEMENTS_TAG])
    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Activates all agreements in a Budget."""

        budget = get_budget_or_404(
            budget_provider=self._budget_provider,
            budget_id=self.kwargs["budget_id"],
            raise_403_if_master=True,
        )

        cmd = ScheduleBudgetBackgroundJobCommandFactory.bulk_deactivate_agreements(budget.id)

        self._mediator.send(cmd)

        return Response(status=status.HTTP_202_ACCEPTED)
