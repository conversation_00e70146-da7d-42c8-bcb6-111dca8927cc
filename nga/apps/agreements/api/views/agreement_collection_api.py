from typing import Any

from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.filters import Search<PERSON>ilter
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.serializers import AgreementCreateSchemaSerializer, AgreementListQuerySerializer
from nga.apps.common.consts import SWAGGER_AGREEMENTS_TAG
from nga.apps.common.views import BaseManageView

from .agreement_bulk_delete import AgreementBulkDeleteAPIView
from .agreement_create import AgreementCreateAPIView
from .agreement_list import AgreementListAPIView, AgreementListPagination


class AgreementCollectionAPIView(BaseManageView):
    """API View that provides actions to Agreement collection."""

    VIEWS_BY_METHOD = {
        "GET": AgreementListAPIView,
        "POST": AgreementCreateAPIView,
        "DELETE": AgreementBulkDeleteAPIView,
    }

    # HTTP method handler are defined in order to provider Swagger schema.

    @swagger_auto_schema(
        tags=[SWAGGER_AGREEMENTS_TAG],
        responses={status.HTTP_200_OK: VIEWS_BY_METHOD["GET"].serializer_class(many=True)},
        query_serializer=AgreementListQuerySerializer(),
        paginator=AgreementListPagination(),
        filters=[SearchFilter()],
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Returns list of Budget Agreements."""

    @swagger_auto_schema(
        tags=[SWAGGER_AGREEMENTS_TAG],
        responses={status.HTTP_201_CREATED: VIEWS_BY_METHOD["POST"].serializer_class},
        request_body=AgreementCreateSchemaSerializer,
    )
    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Creates agreement in a specified Budget."""

    @swagger_auto_schema(tags=[SWAGGER_AGREEMENTS_TAG], request_body=VIEWS_BY_METHOD["DELETE"].serializer_class)
    def delete(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Removes agreements by provided ids."""
