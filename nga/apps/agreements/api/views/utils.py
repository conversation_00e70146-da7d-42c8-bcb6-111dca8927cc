from rest_framework.exceptions import NotFound

from nga.apps.agreements.domain.exceptions import (
    AgreementDoesNotExist,
    BudgetAgreementDoesNotExist,
    DiscountDoesNotExist,
)
from nga.apps.agreements.domain.models import Agreement, BudgetAgreement, Discount
from nga.apps.agreements.domain.repositories import (
    AbstractAgreementRepository,
    AbstractBudgetAgreementRepository,
    AbstractDiscountRepository,
)


def get_budget_agreement_or_404(repository: AbstractBudgetAgreementRepository, agreement_id: int) -> BudgetAgreement:
    """Returns budget agreement instance if it exists, otherwise Django's 404 exception."""

    try:
        budget_agreement = repository.get_by_id(agreement_id)
    except BudgetAgreementDoesNotExist as e:
        raise NotFound(e.message)

    return budget_agreement


def get_agreement_or_404(agreement_repository: AbstractAgreementRepository, agreement_id: int) -> Agreement:
    """Returns agreement instance if it exists, otherwise Django's 404 exception."""

    try:
        agreement = agreement_repository.get_by_id(agreement_id)
    except AgreementDoesNotExist as e:
        raise NotFound(e.message)

    return agreement


def get_discount_or_404(discount_repository: AbstractDiscountRepository, discount_id: int) -> Discount:
    """Returns discount instance if it exists, otherwise Django 404 exception is raised."""

    try:
        discount = discount_repository.get_by_id(discount_id)
    except DiscountDoesNotExist as e:
        raise NotFound(e.message)

    return discount
