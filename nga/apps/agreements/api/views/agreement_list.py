from typing import Any, Optional, cast

from dependency_injector.wiring import Closing, Provide, inject
from django.conf import settings
from django.core.paginator import InvalidPage
from django.db.models import F, Model, QuerySet
from django.http import HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.exceptions import NotFound
from rest_framework.filters import SearchFilter
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from nga.apps.agreements.api.filters import BudgetAgreementAPIFilterSet, BudgetAgreementFilterSet
from nga.apps.agreements.api.mappers import AbstractBudgetAgreementListSchemaMapper
from nga.apps.agreements.api.serializers.agreement import AgreementsListSchemaSerializer
from nga.apps.agreements.api.serializers.agreement_list import AgreementListXlsxQuerySerializer
from nga.apps.agreements.domain.models import BudgetAgreementQueryOptions
from nga.apps.agreements.domain.repositories import (
    AbstractAgreementNegotiatorRepository,
    AbstractBudgetAgreementRepository,
)
from nga.apps.agreements.enums import AgreementIntersectionTypeEnum
from nga.apps.agreements.infra.orm import models
from nga.apps.agreements.infra.orm.expressions import (
    agreement_home_pmn_codes,
    agreement_partner_countries_names,
    agreement_partner_pmn_codes,
)
from nga.apps.agreements.renderers import AbstractBudgetAgreementRenderer, BudgetAgreementXlsxRenderer
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.common.consts import SWAGGER_AGREEMENTS_TAG
from nga.apps.common.pagination import DefaultNGAPageNumberPagination
from nga.apps.common.view_mixins import QueryParametersMixin
from nga.apps.common.view_utils import get_budget_or_404
from nga.apps.references.providers import AbstractCountryProvider, AbstractOperatorProvider


class AgreementListPagination(DefaultNGAPageNumberPagination):
    def get_page_size(self, request: Request, query_len: int) -> Optional[int]:  # noqa
        if not request.query_params.get(self.page_size_query_param):
            return query_len or self.page_size
        return super().get_page_size(request)

    def paginate_queryset(
        self, queryset: QuerySet[Model], request: Request, view: Optional[APIView] = None
    ) -> Optional[list[Any]]:
        query_len = queryset.count()

        page_size = self.get_page_size(request, query_len)
        if not page_size:
            return None

        paginator = self.django_paginator_class(queryset, page_size)
        page_number = self.get_page_number(request, paginator)

        try:
            self.page = paginator.page(page_number)
        except InvalidPage as exc:
            msg = self.invalid_page_message.format(page_number=page_number, message=str(exc))
            raise NotFound(msg)

        if paginator.num_pages > 1 and self.template is not None:
            # The browsable API should display pagination controls.
            self.display_page_controls = True

        self.request = request
        return list(self.page)


class AgreementListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]

    serializer_class = AgreementsListSchemaSerializer

    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = BudgetAgreementAPIFilterSet
    search_fields = (
        "id__icontains",
        "agreement__name__icontains",
        "agreement__home_operators__pmn_code__icontains",
        "agreement__partner_operators__pmn_code__icontains",
        "agreement__partner_operators__country__name__icontains",
    )

    pagination_class = AgreementListPagination

    @inject
    def __init__(
        self,
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        budget_agreement_mapper: AbstractBudgetAgreementListSchemaMapper = Closing[
            Provide["agreement_list_schema_mapper"]
        ],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._budget_agreement_repository = budget_agreement_repository
        self._budget_agreement_mapper = budget_agreement_mapper

    def get_queryset(self) -> QuerySet:
        qs = (
            models.BudgetAgreement.objects.filter(budget_id=self.kwargs["budget_id"])
            .with_has_intersection()
            .values("pk", "has_intersection")
            .order_by("-is_active", F("applied_at").asc(nulls_first=True), "-agreement__updated_at", "-pk")
        )

        return cast(QuerySet, qs)

    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        filtered_queryset = self.filter_queryset(self.get_queryset())
        page_queryset = self.paginate_queryset(filtered_queryset)

        intersections_map = {
            r["pk"]: [AgreementIntersectionTypeEnum.ONE_OF] if r["has_intersection"] else [] for r in page_queryset
        }

        budget_agreement_ids = list(intersections_map.keys())

        if budget_agreement_ids:
            agreements = self._budget_agreement_repository.get_many(budget_agreement_ids=budget_agreement_ids)
            agreements = tuple(sorted(agreements, key=lambda x: budget_agreement_ids.index(x.id)))
        else:
            agreements = tuple()

        schemas = self._budget_agreement_mapper.map(agreements, intersections_map)
        serializer = self.get_serializer(schemas, many=True)

        paginated_data = self.get_paginated_response(serializer.data)
        return paginated_data

    def filter_queryset(self, queryset: QuerySet) -> QuerySet:
        queryset = self.get_queryset().filter(budget_id=self.kwargs["budget_id"])

        return super().filter_queryset(queryset)


class AgreementListXlsxView(AgreementListAPIView, QueryParametersMixin):
    query_serializer_class = AgreementListXlsxQuerySerializer

    filterset_class = BudgetAgreementFilterSet

    @inject
    def __init__(
        self,
        *args: Any,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        operator_provider: AbstractOperatorProvider = Closing[Provide["operator_provider"]],
        country_provider: AbstractCountryProvider = Closing[Provide["country_provider"]],
        agreement_negotiator_repository: AbstractAgreementNegotiatorRepository = Closing[
            Provide["agreement_negotiator_repository"]
        ],
        **kwargs: Any,
    ):
        super().__init__(*args, **kwargs)

        self._budget_repository = budget_repository

        self._operator_provider = operator_provider
        self._country_provider = country_provider
        self._agreement_negotiator_repository = agreement_negotiator_repository

    def get_queryset(self) -> QuerySet:
        qs = (
            models.BudgetAgreement.objects.filter(budget_id=self.kwargs["budget_id"])
            .annotate(
                reference=F("agreement__name"),
                home_pmn_codes=agreement_home_pmn_codes,
                partner_pmn_codes=agreement_partner_pmn_codes,
                partner_country_names=agreement_partner_countries_names,
                start_date=F("agreement__start_date"),
                end_date=F("agreement__end_date"),
                status=F("agreement__status"),
                negotiator=F("agreement__negotiator__name"),
                updated_at=F("agreement__updated_at"),
                include_premium=F("agreement__include_premium"),
                include_premium_in_commitment=F("agreement__include_premium_in_commitment"),
                include_satellite=F("agreement__include_satellite"),
                is_rolling=F("agreement__is_rolling"),
            )
            .values(
                "pk",
                "reference",
                "home_pmn_codes",
                "partner_pmn_codes",
                "partner_country_names",
                "start_date",
                "end_date",
                "status",
                "negotiator",
                "is_active",
                "updated_at",
                "applied_at",
                "calculation_status",
                "include_premium",
                "include_premium_in_commitment",
                "include_satellite",
                "is_rolling",
            )
            .order_by("-is_active", F("applied_at").asc(nulls_first=True), "-agreement__updated_at", "-pk")
        )

        return cast(QuerySet, qs)

    @swagger_auto_schema(
        tags=[SWAGGER_AGREEMENTS_TAG],
        responses={
            "200": openapi.Response(
                "File Attachment",
                schema=openapi.Schema(type=openapi.TYPE_FILE, format=settings.XLSX_CONTENT_TYPE),
            )
        },
        query_serializer=AgreementListXlsxQuerySerializer(),
        filters=[SearchFilter()],
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> HttpResponse:
        budget = get_budget_or_404(self._budget_repository, kwargs["budget_id"])

        values = self.filter_queryset(self.get_queryset())

        query_data = self.get_query_params()

        query_options = BudgetAgreementQueryOptions(
            search=request.query_params.get("search"),
            budget_name=budget.name,
            **query_data,
        )

        renderer = self.get_file_renderer()

        memo_wb = renderer.render(list(values), query_options)
        memo_wb.seek(0)

        response = HttpResponse(memo_wb.read(), content_type=settings.XLSX_CONTENT_TYPE)
        filename = renderer.generate_filename(budget.id)
        response["Content-Disposition"] = f"attachment; filename={filename}"

        return response

    def get_file_renderer(self) -> AbstractBudgetAgreementRenderer:
        renderer = BudgetAgreementXlsxRenderer(
            country_provider=self._country_provider,
            operator_provider=self._operator_provider,
            agreement_negotiator_repository=self._agreement_negotiator_repository,
        )

        return renderer
