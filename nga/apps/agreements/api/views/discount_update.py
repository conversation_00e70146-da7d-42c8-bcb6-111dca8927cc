import traceback
from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from django.db.transaction import atomic
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.mappers import DiscountSchemaMapper
from nga.apps.agreements.api.serializers import DiscountSchemaSerializer
from nga.apps.agreements.api.serializers.discount_create import DiscountParameterCreateSerializer
from nga.apps.agreements.api.serializers.discount_detail import (
    CommitmentDistributionParameterSerializer,
    DiscountQualifyingRuleSerializer,
)
from nga.apps.agreements.api.serializers.discount_update import DiscountUpdateSerializer
from nga.apps.agreements.api.views.utils import get_budget_agreement_or_404, get_discount_or_404
from nga.apps.agreements.commands import UpdateDiscountCommand
from nga.apps.agreements.domain.dto import DiscountParameterDTO
from nga.apps.agreements.domain.models import BudgetAgreement, Discount
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.core.exceptions import BaseNGAException


class DiscountPatchUpdateAPIView(GenericAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = DiscountUpdateSerializer
    response_serializer_class = DiscountSchemaSerializer

    update_discount_command_class = UpdateDiscountCommand

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
        discount_schema_mapper: DiscountSchemaMapper = Closing[Provide["discount_schema_mapper"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._budget_agreement_repository = budget_agreement_repository
        self._discount_repository = discount_repository
        self._discount_schema_mapper = discount_schema_mapper

    def patch(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        budget_agreement = get_budget_agreement_or_404(self._budget_agreement_repository, kwargs["id"])

        discount = self.perform_patch(budget_agreement)

        schema = self._discount_schema_mapper.map_one(discount)

        serializer = self.response_serializer_class(schema)

        return Response(status=status.HTTP_200_OK, data=serializer.data)

    @atomic
    def perform_patch(self, budget_agreement: BudgetAgreement) -> Discount:
        discount = self.get_discount()

        discount = self.update_discount(discount, budget_agreement)

        return discount

    def update_discount(self, discount: Discount, budget_agreement: BudgetAgreement) -> Discount:
        discount_serializer = self.get_serializer(data=self.request.data, partial=True)
        discount_serializer.is_valid(raise_exception=True)

        data = discount_serializer.validated_data

        parameters_dtos: tuple[DiscountParameterDTO, ...] = tuple()

        if data.get("parameters") is not None:
            parameters_serializer = DiscountParameterCreateSerializer(data=self.request.data["parameters"], many=True)
            parameters_serializer.is_valid(raise_exception=True)

            parameters_dtos = tuple(
                parameters_serializer.create(parameters_serializer.validated_data)  # type: ignore[arg-type]
            )

            # DiscountUpdateSerializer contains parameter field as a marker to know should we modify parameters or not
            data.pop("parameters")

        if data.get("qualifying_rule") is not None:
            qualifying_rule_serializer = DiscountQualifyingRuleSerializer(data=self.request.data["qualifying_rule"])
            qualifying_rule_serializer.is_valid(raise_exception=True)

            data["qualifying_rule"] = qualifying_rule_serializer.create(qualifying_rule_serializer.validated_data)

        if data.get("commitment_distribution_parameters") is not None:
            commitment_distribution_serializer = CommitmentDistributionParameterSerializer(
                many=True, data=self.request.data["commitment_distribution_parameters"]
            )
            commitment_distribution_serializer.is_valid(raise_exception=True)

            data["commitment_distribution_parameters"] = commitment_distribution_serializer.create(
                commitment_distribution_serializer.validated_data
            )

        update_discount_cmd = self.update_discount_command_class(
            discount=discount,
            budget_agreement=budget_agreement,
            data=data,
            parameters_dtos=parameters_dtos,
        )

        try:
            discount = self._mediator.send(update_discount_cmd)
        except BaseNGAException as e:
            raise APIException(e.message)
        except Exception as e:
            traceback.print_exc()
            raise APIException(str(e))

        return discount

    def get_discount(self) -> Discount:
        discount = get_discount_or_404(self._discount_repository, self.kwargs["discount_id"])

        return discount
