from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.mappers import DiscountSchemaMapper
from nga.apps.agreements.api.serializers import DiscountSchemaSerializer
from nga.apps.agreements.api.serializers.discount_list import DiscountListQuerySerializer
from nga.apps.agreements.api.views.utils import get_budget_agreement_or_404
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.agreements.requests import GetDiscountsRequest
from nga.apps.common.view_mixins import QueryParametersMixin


class DiscountListAPIView(QueryParametersMixin, ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = DiscountSchemaSerializer
    query_serializer_class = DiscountListQuerySerializer

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        discount_schema_mapper: DiscountSchemaMapper = Closing[Provide["discount_schema_mapper"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._discount_schema_mapper = discount_schema_mapper
        self._budget_agreement_repository = budget_agreement_repository

    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        query = self.get_query_params()

        budget_agreement = get_budget_agreement_or_404(self._budget_agreement_repository, kwargs["id"])

        get_discounts_request = GetDiscountsRequest(
            agreement_id=budget_agreement.agreement_id,
            home_operators=query["home_operators"],
            partner_operators=query["partner_operators"],
        )

        discounts = self._mediator.send(get_discounts_request)

        discount_schemas = self._discount_schema_mapper.map_many(discounts)

        serializer = self.get_serializer(discount_schemas, many=True)

        return Response(serializer.data)
