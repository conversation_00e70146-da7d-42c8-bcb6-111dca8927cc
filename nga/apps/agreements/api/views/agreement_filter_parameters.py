from typing import Any

from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Max, Min, Q, QuerySet
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.filters import AgreementFilterParametersFilterSet
from nga.apps.agreements.api.schemas import AgreementFilterParameters
from nga.apps.agreements.api.serializers import AgreementFilterParametersSchemaSerializer
from nga.apps.agreements.infra.orm import models
from nga.apps.common.consts import SWAGGER_AGREEMENTS_TAG
from nga.apps.common.serializers import BudgetQuerySerializer


class AgreementFilterParametersAPIView(GenericAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = AgreementFilterParametersSchemaSerializer

    filter_backends = [DjangoFilterBackend]
    filterset_class = AgreementFilterParametersFilterSet

    queryset = models.BudgetAgreement.objects.all()

    @swagger_auto_schema(
        tags=[SWAGGER_AGREEMENTS_TAG],
        responses={status.HTTP_200_OK: serializer_class()},
        query_serializer=BudgetQuerySerializer(),
        filters=[AgreementFilterParametersFilterSet()],
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        queryset = self.filter_queryset(self.get_queryset())

        agreement_filter_parameters = queryset.aggregate(
            start_date_min=Min("agreement__start_date"),
            start_date_max=Max("agreement__start_date"),
            end_date_min=Min("agreement__end_date"),
            end_date_max=Max("agreement__end_date"),
            statuses=ArrayAgg("agreement__status", distinct=True, default=[]),
            calculation_statuses=ArrayAgg("calculation_status", distinct=True, default=[]),
            negotiators=ArrayAgg(
                "agreement__negotiator",
                distinct=True,
                filter=Q(agreement__negotiator__id__isnull=False),
                default=[],
            ),
            is_active=ArrayAgg("is_active", distinct=True, ordering="-is_active", default=[]),
            include_satellite=ArrayAgg(
                "agreement__include_satellite", distinct=True, ordering="-agreement__include_satellite", default=[]
            ),
            include_premium=ArrayAgg(
                "agreement__include_premium", distinct=True, ordering="-agreement__include_premium", default=[]
            ),
            include_premium_in_commitment=ArrayAgg(
                "agreement__include_premium_in_commitment",
                distinct=True,
                ordering="-agreement__include_premium_in_commitment",
                default=[],
            ),
            is_rolling=ArrayAgg(
                "agreement__is_rolling",
                distinct=True,
                ordering="-agreement__is_rolling",
                default=[],
            ),
            updated_at_min=Min("agreement__updated_at"),
            updated_at_max=Max("agreement__updated_at"),
            applied_at_min=Min("applied_at"),
            applied_at_max=Max("applied_at"),
        ) | dict(
            has_intersection=[True, False],  # Temporary hardcoded
        )

        serializer = self.get_serializer(AgreementFilterParameters(**agreement_filter_parameters))

        return Response(serializer.data)

    def filter_queryset(self, queryset: QuerySet) -> QuerySet:
        queryset = self.get_queryset().filter(budget_id=self.kwargs["budget_id"])

        return super().filter_queryset(queryset)
