from typing import Any

from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.common.consts import SWAGGER_DISCOUNTS_TAG
from nga.apps.common.views import BaseManageView

from .sub_discount_create import SubDiscountCreateAPIView


class SubDiscountAPIView(BaseManageView):
    VIEWS_BY_METHOD = {
        "POST": SubDiscountCreateAPIView,
    }

    # HTTP method handlers are defined in order to provider Swagger schema.

    @swagger_auto_schema(
        tags=[SWAGGER_DISCOUNTS_TAG],
        request_body=VIEWS_BY_METHOD["POST"].serializer_class(),
        responses={status.HTTP_201_CREATED: ""},
    )
    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Creates sub-discount."""
