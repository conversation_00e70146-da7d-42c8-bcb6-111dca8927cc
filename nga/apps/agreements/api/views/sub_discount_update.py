from typing import Any

from drf_yasg.utils import swagger_auto_schema
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.serializers import SubDiscountSchemaSerializer
from nga.apps.agreements.api.serializers.discount_update import SubDiscountUpdateSerializer
from nga.apps.agreements.api.views.discount_update import DiscountPatchUpdateAPIView
from nga.apps.agreements.api.views.utils import get_discount_or_404
from nga.apps.agreements.commands import UpdateSubDiscountCommand
from nga.apps.agreements.domain.models import Discount
from nga.apps.common.consts import SWAGGER_DISCOUNTS_TAG


class SubDiscountPatchUpdateAPIView(DiscountPatchUpdateAPIView):
    serializer_class = SubDiscountUpdateSerializer
    response_serializer_class = SubDiscountSchemaSerializer

    update_discount_command_class = UpdateSubDiscountCommand

    @swagger_auto_schema(
        tags=[SWAGGER_DISCOUNTS_TAG],
        request_body=SubDiscountUpdateSerializer(),
    )
    def patch(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        return super().patch(request, *args, **kwargs)

    def get_discount(self) -> Discount:
        discount = get_discount_or_404(self._discount_repository, self.kwargs["sub_discount_id"])

        return discount
