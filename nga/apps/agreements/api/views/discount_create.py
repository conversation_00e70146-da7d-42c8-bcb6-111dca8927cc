from typing import Any, cast

from dependency_injector.wiring import Closing, Provide, inject
from django.db.transaction import atomic
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.mappers import DiscountSchemaMapper
from nga.apps.agreements.api.serializers import DiscountSchemaSerializer
from nga.apps.agreements.api.serializers.discount_create import DiscountCreateSerializer
from nga.apps.agreements.api.views.utils import get_budget_agreement_or_404
from nga.apps.agreements.commands import CreateDiscountCommand
from nga.apps.agreements.domain.dto import DiscountDTO
from nga.apps.agreements.domain.models import BudgetAgreement, Discount
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.core.exceptions import BaseNGAException
from nga.internal.uow import AbstractUnitOfWork


class DiscountCreateAPIView(CreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = DiscountSchemaSerializer

    @inject
    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Provide["mediator"],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        discount_schema_mapper: DiscountSchemaMapper = Closing[Provide["discount_schema_mapper"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._uow = uow
        self._mediator = mediator

        self._budget_agreement_repository = budget_agreement_repository
        self._discount_schema_mapper = discount_schema_mapper

    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        budget_agreement = get_budget_agreement_or_404(self._budget_agreement_repository, self.kwargs["id"])

        discount = self.create_discount(request, budget_agreement)

        schema = self._discount_schema_mapper.map_one(discount)

        serializer = self.get_serializer(schema)

        return Response(status=status.HTTP_201_CREATED, data=serializer.data)

    @atomic
    def create_discount(self, request: Request, budget_agreement: BudgetAgreement) -> Discount:
        serializer = DiscountCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        discount_dto = cast(DiscountDTO, serializer.create(serializer.validated_data))

        create_discount_command = CreateDiscountCommand(budget_agreement=budget_agreement, discount_dto=discount_dto)

        try:
            with self._uow:
                discount: Discount = self._mediator.send(create_discount_command)
        except BaseNGAException as e:
            raise APIException(e.message)

        return discount
