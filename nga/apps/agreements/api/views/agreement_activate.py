import traceback
from typing import Any, Optional, cast

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException, ValidationError
from rest_framework.generics import CreateAP<PERSON>View
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.schemas import AgreementActionSchema
from nga.apps.agreements.api.serializers import AgreementActionResponseSchemaSerializer
from nga.apps.agreements.api.serializers.agreement_action import AgreementActionSchemaSerializer
from nga.apps.agreements.commands import BulkActivateBudgetAgreementCommand
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.consts import SWAGGER_AGREEMENTS_TAG
from nga.apps.common.view_utils import get_budget_or_404


class AgreementActivateAPIView(CreateAPIView):
    serializer_class = AgreementActionSchemaSerializer
    response_serializer_class = AgreementActionResponseSchemaSerializer
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator

        self._budget_provider = budget_provider

    @swagger_auto_schema(
        tags=[SWAGGER_AGREEMENTS_TAG],
        request_body=serializer_class(),
        responses={status.HTTP_201_CREATED: response_serializer_class(many=True)},
    )
    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Activates agreement in a Budget."""

        budget = get_budget_or_404(
            budget_provider=self._budget_provider,
            budget_id=self.kwargs["budget_id"],
            raise_403_if_master=True,
        )

        activated_budget_agreements = self.activate_budget_agreements(request, budget_id=budget.id)

        if not activated_budget_agreements:
            raise ValidationError(detail="No agreements have been activated.")

        activated_agreements_response = self.response_serializer_class(
            activated_budget_agreements,
            many=True,
        ).data

        return Response(status=status.HTTP_201_CREATED, data=activated_agreements_response)

    def activate_budget_agreements(self, request: Request, budget_id: int) -> list[BudgetAgreement]:

        bulk_activate_cmd = BulkActivateBudgetAgreementCommand(
            budget_id=budget_id,
            budget_agreement_ids=self.get_budget_agreement_ids(request),
        )

        try:
            activated_budget_agreements, errors = self._mediator.send(bulk_activate_cmd)
        except Exception as e:
            traceback.print_exc()

            raise APIException(str(e))

        return activated_budget_agreements

    @classmethod
    def get_budget_agreement_ids(cls, request: Request) -> Optional[list[int]]:
        serializer = cls.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        activate_schema = cast(AgreementActionSchema, serializer.save())

        return activate_schema.agreement_ids
