from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.mappers import AbstractBudgetAgreementListSchemaMapper
from nga.apps.agreements.api.serializers import AgreementSchemaSerializer
from nga.apps.agreements.api.views.utils import get_budget_agreement_or_404
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.view_utils import get_budget_or_404


class AgreementParametersAPIView(GenericAPIView):
    serializer_class = AgreementSchemaSerializer
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        agreement_mapper: AbstractBudgetAgreementListSchemaMapper = Closing[Provide["agreement_list_schema_mapper"]],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._agreement_mapper = agreement_mapper
        self._budget_provider = budget_provider
        self._budget_agreement_repository = budget_agreement_repository

    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Agreement Parameters GET API."""

        get_budget_or_404(budget_provider=self._budget_provider, budget_id=self.kwargs["budget_id"])

        budget_agreement = get_budget_agreement_or_404(self._budget_agreement_repository, self.kwargs["agreement_id"])

        schema = self._agreement_mapper.map([budget_agreement])[0]

        agreement_response = self.serializer_class(schema).data

        return Response(agreement_response)
