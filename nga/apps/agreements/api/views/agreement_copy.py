import logging
import traceback
from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.mappers import AbstractBudgetAgreementListSchemaMapper
from nga.apps.agreements.api.schemas import AgreementCopySchema
from nga.apps.agreements.api.serializers import AgreementActionResponseSchemaSerializer
from nga.apps.agreements.api.serializers.agreement_copy import AgreementCopySchemaSerializer
from nga.apps.agreements.commands import BulkCopyBudgetAgreementCommand
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.consts import SWAGGER_AGREEMENTS_TAG
from nga.apps.common.view_utils import get_budget_or_404
from nga.core.exceptions import BaseNGAException


class AgreementCopyAPIView(CreateAPIView):
    serializer_class = AgreementCopySchemaSerializer
    permission_classes = [IsAuthenticated]
    response_serializer_class = AgreementActionResponseSchemaSerializer

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        budget_agreement_mapper: AbstractBudgetAgreementListSchemaMapper = Closing[
            Provide["agreement_list_schema_mapper"]
        ],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator

        self._budget_provider = budget_provider
        self._budget_agreement_mapper = budget_agreement_mapper
        self._budget_agreement_repository = budget_agreement_repository

    @swagger_auto_schema(
        tags=[SWAGGER_AGREEMENTS_TAG],
        request_body=AgreementCopySchemaSerializer(),
        responses={status.HTTP_201_CREATED: response_serializer_class(many=True)},
    )
    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        agreement_copy_schema: AgreementCopySchema = serializer.save()

        target_budget = get_budget_or_404(
            budget_provider=self._budget_provider,
            budget_id=self.kwargs["budget_id"],
            raise_403_if_master=True,
        )

        copied_budget_agreements = self.copy_agreements(target_budget.id, agreement_copy_schema.agreement_ids)

        serializer = self.response_serializer_class(copied_budget_agreements, many=True)

        return Response(data=serializer.data, status=status.HTTP_201_CREATED)

    def copy_agreements(
        self,
        target_budget_id: int,
        budget_agreement_ids: list[int],
    ) -> list[BudgetAgreement]:
        bulk_copy_cmd = BulkCopyBudgetAgreementCommand(
            target_budget_id=target_budget_id,
            budget_agreement_ids=budget_agreement_ids,
            source_budget_id=None,
            only_active=None,
        )

        try:
            copied_budget_agreements = self._mediator.send(bulk_copy_cmd)

        except BaseNGAException as e:
            logging.error(e.message)
            raise APIException(detail=e.message)

        except Exception as e:
            traceback.print_exc()
            raise APIException(detail=str(e))

        return copied_budget_agreements
