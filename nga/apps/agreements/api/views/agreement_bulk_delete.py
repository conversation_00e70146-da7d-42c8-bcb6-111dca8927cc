import traceback
from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException
from rest_framework.generics import DestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.serializers import AgreementBulkDeleteSchemaSerializer
from nga.apps.agreements.commands import BulkDeleteBudgetAgreementCommand
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.view_utils import get_budget_or_404


class AgreementBulkDeleteAPIView(DestroyAPIView):
    serializer_class = AgreementBulkDeleteSchemaSerializer
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._budget_provider = budget_provider

    def delete(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Removes agreements by provided ids."""

        budget = get_budget_or_404(
            budget_provider=self._budget_provider,
            budget_id=self.kwargs["budget_id"],
            raise_403_if_master=True,
        )

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        delete_schema = serializer.save()

        delete_agreements_command = BulkDeleteBudgetAgreementCommand(
            budget_id=budget.id,
            budget_agreement_ids=delete_schema.agreement_ids,
        )

        try:
            self._mediator.send(delete_agreements_command)

        except Exception as e:
            traceback.print_exc()
            raise APIException(str(e))

        return Response(status=status.HTTP_204_NO_CONTENT)
