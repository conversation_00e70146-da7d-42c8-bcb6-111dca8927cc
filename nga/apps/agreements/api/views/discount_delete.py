import logging
import traceback
from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from django.db.transaction import atomic
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException
from rest_framework.generics import DestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.views.utils import get_budget_agreement_or_404, get_discount_or_404
from nga.apps.agreements.commands import DeleteDiscountCommand
from nga.apps.agreements.domain.models import BudgetAgreement, Discount
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository


class DiscountDeleteAPIView(DestroyAPIView):
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._discount_repository = discount_repository
        self._budget_agreement_repository = budget_agreement_repository

    def delete(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        budget_agreement = get_budget_agreement_or_404(self._budget_agreement_repository, kwargs["id"])

        discount = get_discount_or_404(self._discount_repository, kwargs["discount_id"])

        self.delete_discount(budget_agreement, discount)

        return Response(status=status.HTTP_204_NO_CONTENT)

    @atomic
    def delete_discount(self, budget_agreement: BudgetAgreement, discount: Discount) -> None:
        delete_discount_cmd = DeleteDiscountCommand(discount=discount, budget_agreement=budget_agreement)

        try:
            self._mediator.send(delete_discount_cmd)
        except Exception as e:
            traceback.print_exc()
            logging.error(str(e))
            raise APIException(detail=str(e))
