from typing import Any

from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.serializers.discount_create import DiscountCreateSerializer
from nga.apps.common.consts import SWAGGER_DISCOUNTS_TAG
from nga.apps.common.views import BaseManageView

from .discount_create import DiscountCreateAPIView
from .discount_list import DiscountListAPIView


class DiscountCollectionAPIView(BaseManageView):
    """API View that provides actions to Discount collection."""

    VIEWS_BY_METHOD = {
        "GET": DiscountListAPIView,
        "POST": DiscountCreateAPIView,
    }

    # HTTP method handlers are defined in order to provider Swagger schema.

    @swagger_auto_schema(
        tags=[SWAGGER_DISCOUNTS_TAG],
        query_serializer=VIEWS_BY_METHOD["GET"].query_serializer_class,
        responses={status.HTTP_200_OK: VIEWS_BY_METHOD["GET"].serializer_class(many=True)},
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Returns list of Agreement Discounts."""

    @swagger_auto_schema(
        tags=[SWAGGER_DISCOUNTS_TAG],
        responses={status.HTTP_201_CREATED: VIEWS_BY_METHOD["POST"].serializer_class},
        request_body=DiscountCreateSerializer,
    )
    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Creates Discount in a specified Agreement."""
