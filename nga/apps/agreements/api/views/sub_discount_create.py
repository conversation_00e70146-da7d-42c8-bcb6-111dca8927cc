import logging
import traceback
from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from django.db.transaction import atomic
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException, ValidationError
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.serializers.discount_create import SubDiscountCreateSerializer
from nga.apps.agreements.api.views.utils import get_budget_agreement_or_404, get_discount_or_404
from nga.apps.agreements.commands import CreateSubDiscountCommand
from nga.apps.agreements.domain.models import BudgetAgreement, Discount
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.core.exceptions import BaseNGAException


class SubDiscountCreateAPIView(CreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SubDiscountCreateSerializer

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._discount_repository = discount_repository
        self._budget_agreement_repository = budget_agreement_repository

    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        budget_agreement = get_budget_agreement_or_404(self._budget_agreement_repository, kwargs["id"])

        discount = get_discount_or_404(self._discount_repository, kwargs["discount_id"])

        try:
            self.create_sub_discount(discount, budget_agreement)
        except BaseNGAException as e:
            raise ValidationError(detail=e.message)
        except Exception as e:
            traceback.print_exc()
            logging.error(str(e))
            raise APIException(detail=str(e))

        return Response(status=status.HTTP_201_CREATED)

    @atomic
    def create_sub_discount(self, discount: Discount, budget_agreement: BudgetAgreement) -> None:
        serializer = self.get_serializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)
        sub_discount_dto = serializer.save()

        create_sub_discount_cmd = CreateSubDiscountCommand(
            discount=discount,
            budget_agreement=budget_agreement,
            sub_discount_dto=sub_discount_dto,
        )

        self._mediator.send(create_sub_discount_cmd)
