import traceback
from typing import Any, cast

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException, ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from nga.apps.agreements.api.schemas import (
    AgreementBulkStatusChangeSchema,
    AgreementErrorResponseSchema,
)
from nga.apps.agreements.api.serializers import (
    AgreementBulkStatusChangeSchemaSerializer,
    AgreementErrorResponseSchemaSerializer,
)
from nga.apps.agreements.commands import BulkStatusChangeBudgetAgreementCommand
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.consts import SWAGGER_AGREEMENTS_TAG
from nga.apps.common.view_utils import get_budget_or_404


class AgreementBulkStatusChangeAPIView(APIView):
    serializer_class = AgreementBulkStatusChangeSchemaSerializer
    error_serializer_class = AgreementErrorResponseSchemaSerializer

    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._budget_provider = budget_provider

    @swagger_auto_schema(
        tags=[SWAGGER_AGREEMENTS_TAG],
        request_body=serializer_class(),
    )
    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:

        budget = get_budget_or_404(
            budget_provider=self._budget_provider,
            budget_id=self.kwargs["budget_id"],
        )

        schema = self.get_schema(request=request)

        cmd = BulkStatusChangeBudgetAgreementCommand(
            budget.id,
            initial_status=schema.initial_status,
            target_status=schema.target_status,
            budget_agreement_ids=schema.agreement_ids,
            only_active=schema.only_active,
        )

        try:
            status_changed_budget_agreements, errors = self._mediator.send(cmd)
        except Exception as e:
            traceback.print_exc()

            raise APIException(str(e))

        if len(errors) > 0:
            error_response_schema = AgreementErrorResponseSchema(failed_agreements=errors)
            raise ValidationError(self.error_serializer_class(error_response_schema).data)

        return Response(status=status.HTTP_201_CREATED)

    def get_schema(self, request: Request) -> AgreementBulkStatusChangeSchema:

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        schema = cast(AgreementBulkStatusChangeSchema, serializer.save())

        return schema
