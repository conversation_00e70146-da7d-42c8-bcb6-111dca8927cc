from typing import Any

from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.common.consts import SWAGGER_DISCOUNTS_TAG
from nga.apps.common.views import BaseManageView

from .discount_delete import DiscountDeleteAPIView
from .discount_update import DiscountPatchUpdateAPIView


class DiscountAPIView(BaseManageView):
    """API View that provides actions to Discount instance."""

    VIEWS_BY_METHOD = {
        "PATCH": DiscountPatchUpdateAPIView,
        "DELETE": DiscountDeleteAPIView,
    }

    # HTTP method handlers are defined in order to provider Swagger schema.

    @swagger_auto_schema(
        tags=[SWAGGER_DISCOUNTS_TAG],
        request_body=VIEWS_BY_METHOD["PATCH"].serializer_class(),
        responses={status.HTTP_200_OK: VIEWS_BY_METHOD["PATCH"].response_serializer_class()},
    )
    def patch(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Updates Discount along with its Parameters."""

    @swagger_auto_schema(tags=[SWAGGER_DISCOUNTS_TAG])
    def delete(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Deletes Discount from Agreement."""
