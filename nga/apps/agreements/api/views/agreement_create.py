import logging
import traceback
from typing import Any, cast

from dependency_injector.wiring import Closing, Provide, inject
from django.db.transaction import atomic
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException, ValidationError
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.mappers import AbstractBudgetAgreementListSchemaMapper, map_agreement_create_schema_to_dto
from nga.apps.agreements.api.schemas import AgreementCreateSchema
from nga.apps.agreements.api.serializers import AgreementCreateSchemaSerializer, AgreementSchemaSerializer
from nga.apps.agreements.commands import CreateBudgetAgreementCommand
from nga.apps.agreements.domain.exceptions import AgreementValidationError
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.view_utils import get_budget_or_404


class AgreementCreateAPIView(CreateAPIView):
    serializer_class = AgreementSchemaSerializer
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        agreement_mapper: AbstractBudgetAgreementListSchemaMapper = Closing[Provide["agreement_list_schema_mapper"]],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator

        self._agreement_mapper = agreement_mapper
        self._budget_provider = budget_provider

    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        budget = get_budget_or_404(
            budget_provider=self._budget_provider,
            budget_id=self.kwargs["budget_id"],
            raise_403_if_master=True,
        )

        agreement = self.create_agreement(request, budget.id)

        agreement_schema = self._agreement_mapper.map([agreement])

        agreement_response = self.serializer_class(agreement_schema[0]).data

        return Response(status=status.HTTP_201_CREATED, data=agreement_response)

    @atomic
    def create_agreement(self, request: Request, budget_id: int) -> BudgetAgreement:
        serializer = AgreementCreateSchemaSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        schema = cast(AgreementCreateSchema, serializer.save())

        agreement_dto = map_agreement_create_schema_to_dto(schema)

        create_agreement_command = CreateBudgetAgreementCommand(budget_id=budget_id, budget_agreement_dto=agreement_dto)

        try:
            agreement: BudgetAgreement = self._mediator.send(create_agreement_command)

        except AgreementValidationError as e:
            raise ValidationError(detail=e.message)

        except Exception as e:
            traceback.print_exc()
            logging.error(str(e))
            raise APIException(detail=str(e))

        return agreement
