from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.serializers import AgreementNegotiatorSchemaSerializer
from nga.apps.agreements.domain.repositories import AbstractAgreementNegotiatorRepository


class AgreementNegotiatorListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = AgreementNegotiatorSchemaSerializer

    @inject
    def __init__(
        self,
        agreement_negotiator_repository: AbstractAgreementNegotiatorRepository = Closing[
            Provide["agreement_negotiator_repository"]
        ],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._agreement_negotiator_repository = agreement_negotiator_repository

    @swagger_auto_schema(
        responses={status.HTTP_200_OK: serializer_class()},
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        agreement_negotiator_list = self._agreement_negotiator_repository.get_many()

        serializer = self.get_serializer(agreement_negotiator_list, many=True)

        return Response(serializer.data)
