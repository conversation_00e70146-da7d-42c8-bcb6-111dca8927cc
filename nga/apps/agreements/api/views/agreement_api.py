from typing import Any

from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.common.consts import SWAGGER_AGREEMENTS_TAG
from nga.apps.common.views import BaseManageView

from .agreement_parameters import AgreementParametersAPIView
from .agreement_update import AgreementPatchUpdateAPIView


class AgreementAPIView(BaseManageView):
    """API View that provides actions to Agreement instance."""

    VIEWS_BY_METHOD = {
        "GET": AgreementParametersAPIView,
        "PATCH": AgreementPatchUpdateAPIView,
    }

    @swagger_auto_schema(
        tags=[SWAGGER_AGREEMENTS_TAG],
        responses={status.HTTP_200_OK: VIEWS_BY_METHOD["GET"].serializer_class()},
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Returns Agreement parameters."""

    @swagger_auto_schema(
        tags=[SWAGGER_AGREEMENTS_TAG],
        request_body=VIEWS_BY_METHOD["PATCH"].serializer_class(),
    )
    def patch(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Updates Agreement along with its Parameters."""
