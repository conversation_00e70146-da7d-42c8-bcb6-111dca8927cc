from datetime import date
from typing import Any, Optional

import django_filters
from django.db.models import QuerySet

from nga.apps.agreements.enums import AgreementCalculationStatusEnum, AgreementStatusEnum
from nga.apps.agreements.infra.orm.expressions import (
    agreement_home_pmn_codes,
    agreement_partner_countries_names,
    agreement_partner_pmn_codes,
)
from nga.apps.agreements.infra.orm.models import BudgetAgreement
from nga.apps.common.expressions import annotate_enum_names
from nga.apps.common.filter_fields import (
    AnnotationOrderingField,
    MaxDatetimeFilter,
    MaxMonthFilter,
    MinMonthFilter,
    MultipleChoiceEnumFilter,
    MultipleIntValueFilterField,
    PeriodOrderingField,
)
from nga.apps.common.queryset_utils import period_dates_intersection_query

ORDERING_FIELDS: list[tuple[str, str]] = [
    ("id", "id"),
    ("agreement__name", "name"),
    ("home_pmn_codes", "home_operators"),
    ("partner_pmn_codes", "partner_operators"),
    ("partner_countries_names", "partner_countries"),
    ("agreement__start_date", "period"),
    ("calculation_status_name", "calculation_status"),
    ("status_name", "status"),
    ("agreement__negotiator__name", "negotiator"),
    ("is_active", "is_active"),
    ("agreement__updated_at", "updated_at"),
    ("applied_at", "applied_at"),
]

ORDERING_FIELDS_WITH_INTERSECTION: list[tuple[str, str]] = ORDERING_FIELDS + [
    ("has_intersection", "has_intersection"),
]


class AgreementOperatorsAndCountriesFilterSet(django_filters.FilterSet):
    home_operators = MultipleIntValueFilterField(field_name="agreement__home_operators__id", lookup_expr="in")
    partner_operators = MultipleIntValueFilterField(field_name="agreement__partner_operators__id", lookup_expr="in")
    partner_countries = MultipleIntValueFilterField(
        field_name="agreement__partner_operators__country_id", lookup_expr="in"
    )

    def filter_queryset(self, queryset: QuerySet) -> QuerySet:
        if self.form.cleaned_data["partner_countries"]:
            self.form.cleaned_data["partner_operators"] = []

        return super().filter_queryset(queryset).distinct()


class BudgetAgreementOrderingFilter(PeriodOrderingField, AnnotationOrderingField):

    SORTING_FIELD_MAP = {
        "home_operators": agreement_home_pmn_codes,
        "partner_operators": agreement_partner_pmn_codes,
        "partner_countries": agreement_partner_countries_names,
        "status": annotate_enum_names("agreement__status", AgreementStatusEnum),
        "calculation_status": annotate_enum_names("calculation_status", AgreementCalculationStatusEnum),
    }


class BudgetAgreementFilterSet(AgreementOperatorsAndCountriesFilterSet):
    start_date_min = MinMonthFilter(field_name="agreement__start_date", lookup_expr="gte")
    start_date_max = MaxMonthFilter(field_name="agreement__start_date", lookup_expr="lte")

    end_date_min = MinMonthFilter(field_name="agreement__end_date", lookup_expr="gte")
    end_date_max = MaxMonthFilter(field_name="agreement__end_date", lookup_expr="lte")

    is_active = django_filters.BooleanFilter(field_name="is_active", lookup_expr="exact")

    include_satellite = django_filters.BooleanFilter(field_name="agreement__include_satellite", lookup_expr="exact")
    include_premium = django_filters.BooleanFilter(field_name="agreement__include_premium", lookup_expr="exact")
    include_premium_in_commitment = django_filters.BooleanFilter(
        field_name="agreement__include_premium_in_commitment", lookup_expr="exact"
    )

    is_rolling = django_filters.BooleanFilter(field_name="agreement__is_rolling", lookup_expr="exact")

    calculation_statuses = MultipleChoiceEnumFilter(
        enum=AgreementCalculationStatusEnum,
        field_name="calculation_status",
        lookup_expr="exact",
    )

    statuses = MultipleChoiceEnumFilter(enum=AgreementStatusEnum, field_name="agreement__status", lookup_expr="exact")
    negotiators = MultipleIntValueFilterField(field_name="agreement__negotiator__id", lookup_expr="in")

    updated_at_min = django_filters.DateTimeFilter(field_name="agreement__updated_at", lookup_expr="gte")
    updated_at_max = MaxDatetimeFilter(field_name="agreement__updated_at", lookup_expr="lte")

    applied_at_min = django_filters.DateTimeFilter(field_name="applied_at", lookup_expr="gte")
    applied_at_max = MaxDatetimeFilter(field_name="applied_at", lookup_expr="lte")

    sort_field = BudgetAgreementOrderingFilter(fields=ORDERING_FIELDS)

    class Meta:
        model = BudgetAgreement
        fields = [
            "home_operators",
            "partner_operators",
            "partner_countries",
            "start_date_min",
            "start_date_max",
            "end_date_min",
            "end_date_max",
            "is_active",
            "calculation_statuses",
            "statuses",
            "negotiators",
            "updated_at_min",
            "updated_at_max",
            "applied_at_min",
            "applied_at_max",
            "sort_field",
        ]


class BudgetAgreementAPIFilterSet(BudgetAgreementFilterSet):

    has_intersection = django_filters.BooleanFilter(field_name="has_intersection", lookup_expr="exact")

    sort_field = BudgetAgreementOrderingFilter(fields=ORDERING_FIELDS_WITH_INTERSECTION)


class AgreementFilterParametersFilterSet(AgreementOperatorsAndCountriesFilterSet):

    start_date = MinMonthFilter(method="filter_period")
    end_date = MaxMonthFilter(method="filter_period")

    class Meta:
        model = BudgetAgreement
        fields = "__all__"

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

        self.start_date_value: Optional[date] = None
        self.end_date_value: Optional[date] = None

    def filter_queryset(self, queryset: QuerySet) -> QuerySet:
        queryset = super().filter_queryset(queryset)

        if self.start_date_value is not None and self.end_date_value is not None:
            queryset = queryset.filter(
                period_dates_intersection_query(self.start_date_value, self.end_date_value, prefix="agreement__")
            )

        elif self.start_date_value is not None:
            queryset = queryset.filter(agreement__end_date__gte=self.start_date_value)

        elif self.end_date_value is not None:
            queryset = queryset.filter(agreement__start_date__lte=self.end_date_value)

        return queryset

    def filter_period(self, queryset: QuerySet, name: str, value: date) -> QuerySet:
        setattr(self, f"{name}_value", value)

        return queryset
