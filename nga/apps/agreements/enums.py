from enum import IntEnum

__all__ = [
    "AgreementStatusEnum",
    "AgreementCalculationStatusEnum",
    "AgreementIntersectionTypeEnum",
    "AgreementNoteTypeEnum",
    "DiscountBasisEnum",
    "DiscountBalancingEnum",
    "DiscountBoundTypeEnum",
    "DiscountCalculationTypeEnum",
    "DiscountDirectionEnum",
    "DiscountModelTypeEnum",
    "DiscountQualifyingBasisEnum",
    "DiscountSettlementMethodEnum",
    "TaxTypeEnum",
]


class AgreementStatusEnum(IntEnum):
    DRAFT = 1
    LIVE = 2
    IN_REVIEW = 3
    APPROVED = 4
    CLOSED = 5
    REJECTED = 6
    SUBMITTED = 7
    SUBMIT_FAILED = 8
    BUDGETING = 9
    AUTO_RENEWED = 10

    @classmethod
    def get_non_confirmed_statuses(cls) -> tuple["AgreementStatusEnum", ...]:
        return cls.DRAFT, cls.IN_REVIEW, cls.REJECTED, cls.SUBMIT_FAILED

    @classmethod
    def get_confirmed_statuses(cls) -> tuple["AgreementStatusEnum", ...]:
        """Submitted statuses represent agreements that are going to be / already live or closed."""

        return cls.APPROVED, cls.LIVE, cls.CLOSED, cls.SUBMITTED, cls.BUDGETING, cls.AUTO_RENEWED

    @classmethod
    def get_unchangeable_statuses(cls) -> tuple["AgreementStatusEnum", ...]:
        """Submitted statuses represent agreements that cannot change their status."""

        return (
            cls.APPROVED,
            cls.SUBMITTED,
            cls.LIVE,
            cls.CLOSED,
        )


class AgreementCalculationStatusEnum(IntEnum):
    NOT_APPLIED = 1
    APPLIED = 2
    FAILED = 3
    OUTDATED = 4


class AgreementIntersectionTypeEnum(IntEnum):
    CONFIRMED = 1
    ACTIVE = 2
    ONE_OF = 3


class AgreementNoteTypeEnum(IntEnum):
    SYSTEM = 1
    USER = 2


class DiscountSettlementMethodEnum(IntEnum):
    CREDIT_NOTE_EOA = 1
    TAP_LVL_DCH = 2
    CREDIT_NOTE_MONTHLY = 3


class DiscountCalculationTypeEnum(IntEnum):
    SINGLE_RATE_EFFECTIVE = 1
    ALL_YOU_CAN_EAT = 2
    BACK_TO_FIRST = 3
    CONTRIBUTION_TO_GROUP_SHORTFALL = 4

    SEND_OR_PAY_FINANCIAL = 5
    SEND_OR_PAY_TRAFFIC = 6

    STEPPED_TIERED = 7
    ALL_YOU_CAN_EAT_MONTHLY = 8
    STEPPED_TIERED_MONTHLY = 9

    PER_MONTH_PER_IMSI_ABOVE_THRESHOLD = 10
    PER_MONTH_PER_IMSI_STEPPED_TIERED = 11
    PER_MONTH_PER_IMSI = 12
    PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING = 13

    ALL_YOU_CAN_EAT_DAILY = 14

    PER_DAY_PER_IMSI_ALL_YOU_CAN_EAT = 15
    PER_DAY_PER_IMSI_CAPPED_CHARGE = 16
    PER_DAY_PER_IMSI_CAPPED_CHARGE_HIGHEST = 17
    PER_MONTH_PER_IMSI_BACK_TO_FIRST = 18

    FINANCIAL_THRESHOLD = 19


class DiscountBasisEnum(IntEnum):
    VALUE = 1
    PERCENTAGE = 2
    NET_TRAFFIC_SENDER_TAP_RATE = 3
    NET_TRAFFIC_RECEIVER_TAP_RATE = 4
    DEDUCTION = 5
    AVERAGE_IOT_RATE = 6


class DiscountQualifyingBasisEnum(IntEnum):
    VOLUME = 1
    MARKET_SHARE_PERCENTAGE = 2
    UNIQUE_IMSI_COUNT_PER_MONTH = 4
    AVERAGE_MONTHLY_USAGE_PER_IMSI = 5


class DiscountBalancingEnum(IntEnum):
    BALANCED = 1
    BALANCED_WITH_GROUP_COMPENSATION = 2
    BALANCING_FOR_SOP_TRAFFIC = 3
    INCREMENTAL_TRAFFIC = 4
    NO_BALANCING = 5
    UNBALANCED = 6
    UNBALANCED_WITH_GROUP_COMPENSATION = 7


class DiscountBoundTypeEnum(IntEnum):
    VOLUME = 1
    FINANCIAL_COMMITMENT = 2
    MARKET_SHARE = 3
    UNIQUE_IMSI_COUNT_PER_MONTH = 5
    VOLUME_INCLUDED_IN_ACCESS_FEE = 6
    FINANCIAL_THRESHOLD = 7


class TaxTypeEnum(IntEnum):
    NET = 1
    GROSS = 2


class DiscountDirectionEnum(IntEnum):
    INBOUND = 1
    OUTBOUND = 2
    BIDIRECTIONAL = 3


class DiscountModelTypeEnum(IntEnum):
    SINGLE_RATE_EFFECTIVE = 1
    STEPPED_TIERED = 2

    SEND_OR_PAY_TRAFFIC_SINGLE_RATE_EFFECTIVE = 3
    SEND_OR_PAY_TRAFFIC_STEPPED_TIERED = 4

    SEND_OR_PAY_FINANCIAL = 5

    BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE = 6
    BALANCED_UNBALANCED_STEPPED_TIERED = 7

    BACK_TO_FIRST = 8

    PER_MONTH_PER_IMSI = 9
    PER_MONTH_PER_IMSI_ABOVE_THRESHOLD = 10
    PER_MONTH_PER_IMSI_STEPPED_TIERED = 11
    PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING = 12
    PER_MONTH_PER_IMSI_BACK_TO_FIRST = 13

    ALL_YOU_CAN_EAT = 14
