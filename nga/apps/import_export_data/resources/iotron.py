from copy import deepcopy
from typing import Any, Optional

from django.db.models import Model
from import_export.fields import Field
from import_export.instance_loaders import ModelInstanceLoader
from import_export.resources import ModelResource
from import_export.widgets import DateTimeWidget, DateWidget
from tablib import Dataset

from nga.apps.agreements.consts import FULL_DATE_FORMAT
from nga.apps.agreements.enums import DiscountBasisEnum, DiscountCalculationTypeEnum, TaxTypeEnum
from nga.apps.agreements.infra.orm.models import AgreementNegotiator
from nga.apps.import_export_data.consts import (
    AGREEMENT_PARTNER_OPERATORS_COLUMN_NAME,
    DISCOUNT_EVENT_TYPE_COLUMN_NAME,
    DISCOUNT_QUALIFYING_EVENT_TYPE_COLUMN_NAME,
    IMPORT_DATE_FORMAT,
)
from nga.apps.import_export_data.utils import stringify_decimal, validate_unknown_call_destination
from nga.apps.import_export_data.widgets import (
    ChoicesListWidget,
    DecimalWidget,
    DiscountPartnerOperatorsStringWidget,
    EnumWidget,
    ExtendedBooleanWidget,
    FieldGetterMixin,
    IntegerWidget,
    MixedServiceTypeWidget,
    NonPKManyToManyStringWidget,
    NonPKStringWidget,
    SegmentFieldManyToManyStringWidget,
    ServiceTypeListWidget,
    StringWidget,
)
from nga.apps.iotron.domain.dto import DiscountImportDict, DiscountParameterDict
from nga.apps.iotron.domain.mappers import (
    DISCOUNT_CALL_DESTINATION_MAP,
    DISCOUNT_DIRECTION_MAP,
    DISCOUNT_IMSI_COUNT_TYPE_MAP,
    DISCOUNT_PARAMETER_BOUND_TYPE_MAP,
    DISCOUNT_PARAMETER_QUALIFYING_BASIS_MAP,
    DISCOUNT_SETTLEMENT_METHOD_MAP,
    DISCOUNT_TAX_MAP,
    DISCOUNT_VOLUME_TYPE_MAP,
)
from nga.apps.iotron.domain.utils import to_decimal
from nga.apps.iotron.infra.orm.models import ExternalAgreement
from nga.apps.iotron.submit.mappings import (
    IOTRON_TO_NGA_DISCOUNT_PARAMETER_BALANCING_MAP,
    IOTRON_TO_NGA_DISCOUNT_PARAMETER_BASIS_MAP,
    IOTRON_TO_NGA_DISCOUNT_PARAMETER_CALCULATION_TYPE_MAP,
    IOTRON_TO_NGA_SERVICE_TYPE_MAP,
)
from nga.apps.references.infra.orm.models import Country, Operator, TrafficSegment


class ExternalAgreementResource(FieldGetterMixin, ModelResource):
    new_external_agreement_ids: set[int]

    class Meta:
        model = ExternalAgreement
        import_id_fields = ("external_id",)
        use_transactions = False

    # agreement fields
    external_id = Field(
        column_name="AGREEMENT_ID",
        attribute="external_id",
        widget=IntegerWidget(),
    )

    reference = Field(
        column_name="AGREEMENT_REFERENCE",
        attribute="name",
        widget=StringWidget(),
    )

    start_date = Field(
        column_name="AGREEMENT_START_DATE", attribute="start_date", widget=DateWidget(format=IMPORT_DATE_FORMAT)
    )

    end_date = Field(
        column_name="AGREEMENT_END_DATE", attribute="end_date", widget=DateWidget(format=IMPORT_DATE_FORMAT)
    )

    home_operators = Field(
        column_name="CLIENT_PMN",
        attribute="home_operators",
        widget=NonPKManyToManyStringWidget(Operator, field="pmn_code"),
    )

    partner_operators = Field(
        column_name=AGREEMENT_PARTNER_OPERATORS_COLUMN_NAME,
        attribute="partner_operators",
        widget=NonPKManyToManyStringWidget(Operator, field="pmn_code"),
    )

    negotiator = Field(
        column_name="NEGOTIATOR",
        attribute="negotiator",
        widget=NonPKStringWidget(AgreementNegotiator, field="name"),
    )

    do_not_calculate = Field(
        column_name="DO_NOT_CALCULATE",
        attribute="do_not_calculate",
        widget=ExtendedBooleanWidget(),
    )

    include_satellite = Field(
        column_name="INCLUDE_SATELLITE",
        attribute="include_satellite",
        widget=ExtendedBooleanWidget(),
    )

    include_premium = Field(
        column_name="PREMIUM_NUMBERS_AT_DISCOUNT_RATE",
        attribute="include_premium",
        widget=ExtendedBooleanWidget(),
    )

    include_premium_in_commitment = Field(
        column_name="PREMIUM_NUMBERS_INCLUDED_IN_COMMITMENT",
        attribute="include_premium_in_commitment",
        widget=ExtendedBooleanWidget(),
    )

    is_rolling = Field(
        column_name="ROLLING_AGREEMENT",
        attribute="is_rolling",
        widget=ExtendedBooleanWidget(),
    )

    include_access_fee_in_sop_financial_inbound = Field(
        column_name="INCLUDE_ACCESS_FEE_IN_SOP_FINANCIAL_INBOUND",
        attribute="include_access_fee_in_sop_financial_inbound",
        widget=ExtendedBooleanWidget(),
    )

    include_access_fee_in_sop_financial_outbound = Field(
        column_name="INCLUDE_ACCESS_FEE_IN_SOP_FINANCIAL_OUTBOUND",
        attribute="include_access_fee_in_sop_financial_outbound",
        widget=ExtendedBooleanWidget(),
    )

    terminate_date = Field(
        column_name="TERMINATION_DATE",
        attribute="terminated_at",
        widget=DateWidget(),
    )

    create_date = Field(
        column_name="CREATE_DATE",
        attribute="created_at",
        widget=DateTimeWidget(format="%d.%m.%Y %H:%M"),
    )

    update_date = Field(
        column_name="UPDATE_DATE", attribute="updated_at", widget=DateTimeWidget(format="%d.%m.%Y %H:%M")
    )

    # discount fields

    # is added automatically field during parsing
    discounts = Field(column_name="DISCOUNTS", attribute="discounts")

    discount_settlement_method = Field(
        column_name="DISCOUNT_METHOD",
        readonly=True,
        widget=EnumWidget(values_map=DISCOUNT_SETTLEMENT_METHOD_MAP),
    )

    discount_direction = Field(
        column_name="TRAFFIC_DIRECTION",
        readonly=True,
        widget=EnumWidget(values_map=DISCOUNT_DIRECTION_MAP),
    )

    discount_service_types = Field(
        column_name="SERVICE_TYPE",
        readonly=True,
        widget=ServiceTypeListWidget(
            event_type_field=DISCOUNT_EVENT_TYPE_COLUMN_NAME,
            child_item_widget=MixedServiceTypeWidget(
                event_type_field=DISCOUNT_EVENT_TYPE_COLUMN_NAME,
                values_map=IOTRON_TO_NGA_SERVICE_TYPE_MAP,
            ),
        ),
    )

    discount_home_operators = Field(
        column_name="DISCOUNT_CLIENT_PMNS",
        readonly=True,
        widget=NonPKManyToManyStringWidget(Operator, field="pmn_code"),
    )

    discount_partner_operators = Field(
        column_name="DISCOUNT_PARTNER_PMNS",
        readonly=True,
        widget=DiscountPartnerOperatorsStringWidget(Operator, field="pmn_code"),
    )

    discount_incl_tax = Field(
        column_name="DISCOUNT_TAX",
        widget=EnumWidget(values_map=DISCOUNT_TAX_MAP),
    )

    discount_currency_code = Field(
        column_name="DISCOUNT_CURRENCY",
        widget=StringWidget(),
    )

    discount_traffic_segments = Field(
        column_name="SEGMENT",
        readonly=True,
        widget=SegmentFieldManyToManyStringWidget(TrafficSegment, field="name"),
    )

    discount_volume_type = Field(
        column_name="CHARGING_INCREMENT",
        readonly=True,
        widget=EnumWidget(values_map=DISCOUNT_VOLUME_TYPE_MAP),
    )

    discount_valid_from = Field(
        column_name="VALID_FROM",
        readonly=True,
        widget=DateWidget(format=IMPORT_DATE_FORMAT),
    )

    discount_valid_to = Field(
        column_name="VALID_TO",
        readonly=True,
        widget=DateWidget(format=IMPORT_DATE_FORMAT),
    )

    discount_qualifying_direction = Field(
        column_name="QUALIFYING_DIRECTION",
        readonly=True,
        widget=EnumWidget(
            values_map=DISCOUNT_DIRECTION_MAP,
            optional=True,
        ),
    )

    discount_qualifying_service_type = Field(
        column_name="QUALIFYING_SERVICE_TYPE",
        readonly=True,
        widget=ServiceTypeListWidget(
            event_type_field=DISCOUNT_QUALIFYING_EVENT_TYPE_COLUMN_NAME,
            child_item_widget=MixedServiceTypeWidget(
                event_type_field=DISCOUNT_QUALIFYING_EVENT_TYPE_COLUMN_NAME,
                values_map=IOTRON_TO_NGA_SERVICE_TYPE_MAP,
                optional=True,
            ),
        ),
    )

    discount_qualifying_basis = Field(
        column_name="QUALIFYING_BASIS",
        readonly=True,
        widget=EnumWidget(
            values_map=DISCOUNT_PARAMETER_QUALIFYING_BASIS_MAP,
            optional=True,
        ),
    )

    discount_qualifying_lower_bound = Field(
        column_name="QUALIFYING_VOLUME",
        readonly=True,
        widget=DecimalWidget(decimal_places=10, optional=True),
    )

    discount_call_destinations = Field(
        column_name="DESTN_FLAG",
        readonly=True,
        widget=ChoicesListWidget(
            child_item_widget=EnumWidget(
                values_map=DISCOUNT_CALL_DESTINATION_MAP,
                optional=True,
            )
        ),
    )

    discount_called_countries = Field(
        column_name="COUNTRY_ID",
        readonly=True,
        widget=NonPKManyToManyStringWidget(Country, field="code"),
    )

    discount_imsi_count_type = Field(
        column_name="IMSI_COUNT_TYPE",
        readonly=True,
        widget=EnumWidget(values_map=DISCOUNT_IMSI_COUNT_TYPE_MAP, optional=True),
    )

    # Discount Parameter fields

    discount_parameter_calculation_type = Field(
        column_name="CALCULATION_TYPE",
        readonly=True,
        widget=EnumWidget(values_map=IOTRON_TO_NGA_DISCOUNT_PARAMETER_CALCULATION_TYPE_MAP),
    )

    discount_parameter_basis_value = Field(
        column_name="DISCOUNT_RATE",
        readonly=True,
        widget=DecimalWidget(decimal_places=10, optional=True),
    )

    discount_parameter_basis = Field(
        column_name="DISCOUNT_TYPE",
        readonly=True,
        widget=EnumWidget(
            values_map=IOTRON_TO_NGA_DISCOUNT_PARAMETER_BASIS_MAP,
            optional=True,
            default=DiscountBasisEnum.VALUE,
        ),
    )

    discount_parameter_balancing = Field(
        column_name="BALANCE_TYPE",
        readonly=True,
        widget=EnumWidget(values_map=IOTRON_TO_NGA_DISCOUNT_PARAMETER_BALANCING_MAP, optional=True),
    )

    discount_parameter_lower_bound = Field(
        column_name="LOWER_BOUND",
        readonly=True,
        widget=DecimalWidget(decimal_places=2, optional=True),
    )

    discount_parameter_upper_bound = Field(
        column_name="UPPER_BOUND",
        readonly=True,
        widget=DecimalWidget(decimal_places=2, optional=True),
    )

    discount_parameter_bound_type = Field(
        column_name="BOUND_TYPE",
        readonly=True,
        widget=EnumWidget(
            values_map=DISCOUNT_PARAMETER_BOUND_TYPE_MAP,
            optional=True,
        ),
    )

    discount_parameter_fair_usage_rate = Field(
        column_name="FAIR_USAGE_RATE",
        readonly=True,
        widget=DecimalWidget(decimal_places=10, optional=True),
    )

    discount_parameter_fair_usage_threshold = Field(
        column_name="FAIR_USAGE_THRESHOLD",
        readonly=True,
        widget=DecimalWidget(decimal_places=2, optional=True),
    )

    discount_parameter_toll_rate = Field(
        column_name="DOMESTIC_TOLL_RATE",
        readonly=True,
        widget=DecimalWidget(decimal_places=10, optional=True),
    )

    discount_parameter_airtime_rate = Field(
        column_name="AIRTIME_RATE",
        readonly=True,
        widget=DecimalWidget(decimal_places=10, optional=True),
    )

    discount_parameter_access_fee_rate = Field(
        column_name="ACCESS_FEE_RATE",
        readonly=True,
        widget=DecimalWidget(decimal_places=10, optional=True),
    )

    discount_parameter_incremental_rate = Field(
        column_name="INCREMENTAL_RATE",
        readonly=True,
        widget=DecimalWidget(decimal_places=10, optional=True),
    )

    def before_import(self, dataset: Dataset, **kwargs: dict[str, Any]) -> None:
        self.new_external_agreement_ids = set()

    def _validate_required_m2m_fields(self, row: dict[str, Any]) -> None:
        for field in ["home_operators", "partner_operators", "discount_home_operators", "discount_partner_operators"]:
            operators_data = self.clean_field(field, row)

            if not operators_data:
                raise Exception(f"{self.fields[field].column_name} are not found", operators_data)

    def before_import_row(self, row: dict[str, Any], row_number: Optional[int] = None, **kwargs: Any) -> None:
        row[self.f("discount_call_destinations")] = validate_unknown_call_destination(
            row[self.f("discount_call_destinations")],
            service_types=self.clean_field("discount_service_types", row),
        )

        self._validate_required_m2m_fields(row)

        include_premium = self.clean_field("include_premium", row)
        include_premium_in_commitment = self.clean_field("include_premium_in_commitment", row)

        if include_premium and not include_premium_in_commitment:
            raise Exception("Premium Commitment cannot be excluded if Discount Rate Premium is included")

    def get_instance(self, instance_loader: ModelInstanceLoader, row: dict[str, Any]) -> Optional[Model]:
        instance = super().get_instance(instance_loader, row)

        if instance and instance.id not in self.new_external_agreement_ids:
            instance.delete()

            return None

        return instance

    def import_instance(self, instance: ExternalAgreement, row: dict[str, Any], **kwargs: dict[str, Any]) -> None:
        discount_parameter = self._generate_discount_parameter_data(row)

        force_create = discount_parameter["calculation_type"] == DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL.name

        discount_index, discount = self.get_or_create_discount(row, instance, force_create=force_create)

        discount["discount_parameters"].append(discount_parameter)

        discounts = deepcopy(instance.discounts)

        if discount_index is not None:
            discounts[discount_index] = discount
        else:
            discounts.append(discount)

        row[self.f("discounts")] = discounts

        super().import_instance(instance, row, **kwargs)

    def _generate_discount_parameter_data(self, row: dict[str, Any]) -> DiscountParameterDict:
        basis = self.clean_field("discount_parameter_basis", row)
        basis_value = self.clean_field("discount_parameter_basis_value", row)
        balancing = self.clean_field("discount_parameter_balancing", row)
        bound_type = self.clean_field("discount_parameter_bound_type", row)
        lower_bound = self.clean_field("discount_parameter_lower_bound", row)
        upper_bound = self.clean_field("discount_parameter_upper_bound", row)
        toll_rate = self.clean_field("discount_parameter_toll_rate", row)
        airtime_rate = self.clean_field("discount_parameter_airtime_rate", row)
        fair_usage_rate = self.clean_field("discount_parameter_fair_usage_rate", row)
        fair_usage_threshold = self.clean_field("discount_parameter_fair_usage_threshold", row)
        access_fee_rate = self.clean_field("discount_parameter_access_fee_rate", row)
        incremental_rate = self.clean_field("discount_parameter_incremental_rate", row)

        return DiscountParameterDict(
            calculation_type=self.clean_field("discount_parameter_calculation_type", row).name,
            basis=basis.name if basis else None,
            basis_value=stringify_decimal(basis_value),
            balancing=balancing.name if balancing else None,
            bound_type=bound_type.name if bound_type else None,
            lower_bound=stringify_decimal(lower_bound),
            upper_bound=stringify_decimal(upper_bound),
            toll_rate=stringify_decimal(toll_rate),
            airtime_rate=stringify_decimal(airtime_rate),
            fair_usage_rate=stringify_decimal(fair_usage_rate),
            fair_usage_threshold=stringify_decimal(fair_usage_threshold),
            access_fee_rate=stringify_decimal(access_fee_rate),
            incremental_rate=stringify_decimal(incremental_rate),
        )

    def get_or_create_discount(
        self,
        row: dict[str, str],
        external_agreement: ExternalAgreement,
        force_create: bool = False,
    ) -> tuple[Optional[int], DiscountImportDict]:
        tax_type = TaxTypeEnum.GROSS if self.clean_field("discount_incl_tax", row) else TaxTypeEnum.NET
        start_date = self.clean_field("discount_valid_from", row) or external_agreement.start_date
        end_date = self.clean_field("discount_valid_to", row) or external_agreement.end_date

        home_operators = self.clean_field("discount_home_operators", row)
        partner_operators = self.clean_field("discount_partner_operators", row)

        traffic_segments = self.clean_field("discount_traffic_segments", row)
        called_countries = self.clean_field("discount_called_countries", row)

        direction = self.clean_field("discount_direction", row)
        service_types = self.clean_field("discount_service_types", row)
        currency_code = self.clean_field("discount_currency_code", row)
        volume_type = self.clean_field("discount_volume_type", row)
        settlement_method = self.clean_field("discount_settlement_method", row)
        call_destinations = self.clean_field("discount_call_destinations", row)
        qualifying_direction = self.clean_field("discount_qualifying_direction", row)
        qualifying_service_types = self.clean_field("discount_qualifying_service_type", row)
        qualifying_basis = self.clean_field("discount_qualifying_basis", row)
        qualifying_lower_bound = self.clean_field("discount_qualifying_lower_bound", row)
        imsi_count_type = self.clean_field("discount_imsi_count_type", row)

        new_discount = DiscountImportDict(
            home_operators=home_operators,
            partner_operators=partner_operators,
            direction=direction.name,
            service_types=sorted([s.name for s in service_types]),
            start_date=start_date.strftime(FULL_DATE_FORMAT),
            end_date=end_date.strftime(FULL_DATE_FORMAT),
            call_destinations=sorted([c.name for c in call_destinations]),
            called_countries=called_countries,
            traffic_segments=traffic_segments,
            currency_code=currency_code,
            tax_type=tax_type.name,
            volume_type=volume_type.name,
            settlement_method=settlement_method.name,
            qualifying_direction=qualifying_direction.name if qualifying_direction is not None else None,
            qualifying_service_types=(
                sorted([s.name for s in qualifying_service_types]) if qualifying_service_types is not None else None
            ),
            qualifying_basis=qualifying_basis.name if qualifying_basis is not None else None,
            qualifying_lower_bound=stringify_decimal(qualifying_lower_bound),
            imsi_count_type=imsi_count_type.name if imsi_count_type else None,
            discount_parameters=[],
        )

        if force_create:
            return None, new_discount

        index, actual_discount = self._filter_discounts(
            external_agreement=external_agreement,
            new_discount=new_discount,
        )

        if actual_discount is None:
            return None, new_discount

        return index, actual_discount

    @staticmethod
    def _filter_discounts(
        external_agreement: ExternalAgreement,
        new_discount: DiscountImportDict,
    ) -> tuple[Optional[int], Optional[DiscountImportDict]]:
        for index, discount in enumerate(external_agreement.discounts):

            if (
                discount["home_operators"] == new_discount["home_operators"]
                and discount["partner_operators"] == new_discount["partner_operators"]
                and discount["direction"] == new_discount["direction"]
                and discount["service_types"] == new_discount["service_types"]
                and discount["start_date"] == new_discount["start_date"]
                and discount["end_date"] == new_discount["end_date"]
                and discount["call_destinations"] == new_discount["call_destinations"]
                and discount["called_countries"] == new_discount["called_countries"]
                and discount["traffic_segments"] == new_discount["traffic_segments"]
                and discount["imsi_count_type"] == new_discount["imsi_count_type"]
                and discount["qualifying_direction"] == new_discount["qualifying_direction"]
                and discount["qualifying_service_types"] == new_discount["qualifying_service_types"]
                and discount["qualifying_basis"] == new_discount["qualifying_basis"]
                and to_decimal(discount["qualifying_lower_bound"]) == to_decimal(new_discount["qualifying_lower_bound"])
                and not any(
                    param
                    for param in discount["discount_parameters"]
                    if param["calculation_type"] == DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL.name
                )
            ):
                return index, discount

        return None, None

    def after_save_instance(self, instance: ExternalAgreement, row: dict[str, Any], **kwargs: dict[str, Any]) -> None:
        self.new_external_agreement_ids.add(instance.id)
