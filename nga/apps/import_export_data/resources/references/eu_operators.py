from import_export import resources
from import_export.fields import Field
from import_export.widgets import ForeignKeyWidget

from nga.apps.references.infra.orm.models import EUOperator, Operator


class EUOperatorResource(resources.ModelResource):
    operator_pmn = Field(
        column_name="TADIG",
        attribute="operator",
        widget=ForeignKeyWidget(Operator, field="pmn_code"),
    )

    class Meta:
        model = EUOperator
        fields = ("operator_pmn",)
        import_id_fields = ("operator_pmn",)
        skip_unchanged = True
        use_transactions = False
