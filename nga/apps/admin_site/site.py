import logging

from django.contrib import admin
from django.core.exceptions import PermissionDenied, SuspiciousOperation
from django.http import HttpRequest
from django.urls import URLPattern, URLResolver, path
from mozilla_django_oidc.contrib.drf import get_oidc_backend

from nga.apps.admin_site.views import ExchangeRatesImportFormView

logger = logging.getLogger(__name__)


class OIDCAdminSite(admin.AdminSite):
    site_title = "Simulation and Budget"

    site_header = site_title

    index_title = "S&B Administration"

    def has_permission(self, request: HttpRequest) -> bool:
        """
        Return True if the given HttpRequest has permission to view
        *at least one* page in the admin site.
        """

        user = request.user

        if user.is_anonymous:
            return False

        access_token = request.session.get("oidc_access_token")
        if access_token is None:
            return False

        access_token_is_active = self.token_is_active(access_token)
        if not access_token_is_active:
            return False

        if super().has_permission(request):
            return True

        raise PermissionDenied()

    @classmethod
    def token_is_active(cls, token: str) -> bool:
        backend = get_oidc_backend()
        try:
            backend.introspect_token(token)
        except SuspiciousOperation:
            return False
        return True

    def get_urls(self) -> list[URLResolver | URLPattern]:
        extra_urls = [
            path(
                "references/exchangerate/import",
                ExchangeRatesImportFormView.as_view(),
                name="exchange_rates_import_view",
            ),
        ]
        urls = [*extra_urls, *super().get_urls()]
        return urls
