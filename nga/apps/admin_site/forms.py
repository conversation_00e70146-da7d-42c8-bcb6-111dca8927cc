from django import forms
from django.core.exceptions import ValidationError

from nga.apps.budgets.infra.orm.models import BudgetSnapshot


class ImportFileForm(forms.Form):
    file = forms.FileField()


class BudgetAdminForm(forms.ModelForm):
    def clean_active_snapshot(self) -> BudgetSnapshot:
        budget_id = self.instance.pk
        active_snapshot = self.cleaned_data["active_snapshot"]

        if active_snapshot is not None and budget_id != active_snapshot.budget_id:
            raise ValidationError("Invalid snapshot provided for the Budget. Budget ID must be the same")

        return active_snapshot
