from typing import Any, Type

import httpx
from admin_auto_filters.views import AutocompleteJsonView
from django.contrib import admin
from django.contrib.admin.views.decorators import staff_member_required
from django.core.paginator import Paginator
from django.db.models import Model, Q, QuerySet
from django.urls import reverse, reverse_lazy
from django.utils.decorators import method_decorator
from mozilla_django_oidc.views import OIDCLogoutView

from nga.apps.admin_site.base_views import ImportFileFormView
from nga.apps.admin_site.file_processors import ExchangeRatesGSMAFileProcessor
from nga.apps.agreements.infra.orm import models


class AdminOIDCLogoutView(OIDCLogoutView):
    @property
    def redirect_url(self) -> str:
        redirect_to = self.request.headers["Origin"] + reverse("admin:index")
        oidc_logout_url = httpx.URL(self.get_settings("OIDC_OP_LOGOUT_URL"))
        oidc_logout_url = oidc_logout_url.copy_add_param(
            key="redirect_uri",
            value=redirect_to,
        )
        return str(oidc_logout_url)


@method_decorator(staff_member_required, name="dispatch")
class ExchangeRatesImportFormView(ImportFileFormView):
    template_name = "admin/exchange_rates/import_form.html"
    file_processor_class = ExchangeRatesGSMAFileProcessor
    success_url = reverse_lazy("admin:references_exchangerate_changelist")
    success_message = "Exchange Rates have been imported!"


class FilterView(AutocompleteJsonView):
    model_admin = admin.ModelAdmin
    queryset_model: Type[Model]
    search_fields: list[str] = ["id"]

    def get_paginator(self, *args: tuple[Any], **kwargs: dict[str, Any]) -> Paginator:
        paginator: Paginator = self.paginator_class(*args, **kwargs)

        return paginator

    def get_queryset(self) -> QuerySet[Model]:
        queryset: QuerySet[Model] = self.queryset_model.objects.order_by("pk")

        if self.term:
            search_conditions = Q()

            for search_field in self.search_fields:
                search_conditions |= Q(**{f"{search_field}__icontains": self.term})

            queryset = queryset.filter(search_conditions).distinct()

        return queryset


class AgreementFilterView(FilterView):
    queryset_model = models.Agreement
    search_fields: list[str] = ["id", "name"]


class DiscountFilterView(FilterView):
    queryset_model = models.Discount
    search_fields: list[str] = ["id", "agreement__id", "agreement__name"]
