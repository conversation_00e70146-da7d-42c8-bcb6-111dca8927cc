from typing import Any

from django.contrib import admin
from django.db.models import QuerySet
from django.http import HttpRequest
from django.urls import URLPattern, path

from nga.apps.admin_site.views import AgreementFilterView, DiscountFilterView
from nga.apps.common.expressions import home_pmn_codes, partner_pmn_codes
from nga.apps.references.infra.orm.models import Operator


class HomeOperatorsObj:
    home_operators: QuerySet[Operator]


class PartnerOperatorsObj:
    partner_operators: QuerySet[Operator]


class HomeOperatorsFieldMixin:
    def get_queryset(self, request: HttpRequest) -> QuerySet[Any]:
        qs: QuerySet[Any] = super().get_queryset(request)  # type: ignore[misc] # "get_queryset" undefined in superclass
        qs = qs.annotate(home_pmn_codes=home_pmn_codes)

        return qs

    @admin.display(ordering="home_pmn_codes")
    def home_operator_list(self, obj: HomeOperatorsObj) -> str:
        home_op_list: str = obj.home_pmn_codes  # type: ignore[attr-defined] # field added as annotate

        return home_op_list


class PartnerOperatorsFieldMixin:
    def get_queryset(self, request: HttpRequest) -> QuerySet[Any]:
        qs: QuerySet[Any] = super().get_queryset(request)  # type: ignore[misc] # "get_queryset" undefined in superclass
        qs = qs.annotate(partner_pmn_codes=partner_pmn_codes)

        return qs

    @admin.display(ordering="partner_pmn_codes")
    def partner_operator_list(self, obj: PartnerOperatorsObj) -> str:
        partner_op_list: str = obj.partner_pmn_codes  # type: ignore[attr-defined] # field added as annotate

        return partner_op_list


class BothOperatorsFieldsMixin(HomeOperatorsFieldMixin, PartnerOperatorsFieldMixin):
    """
    This mixin combine the home operators and partner operators
    """


class AgreementSearchURL(admin.ModelAdmin):
    def get_urls(self) -> list[URLPattern]:
        urls = super().get_urls()
        agreement_filter_url = path(
            "agreement_filter/", self.admin_site.admin_view(AgreementFilterView.as_view()), name="agreement_filter"
        )

        return [agreement_filter_url] + urls


class DiscountSearchURL(admin.ModelAdmin):
    def get_urls(self) -> list[URLPattern]:
        urls = super().get_urls()
        discount_filter_url = path(
            "discount_filter/", self.admin_site.admin_view(DiscountFilterView.as_view()), name="discount_filter"
        )

        return [discount_filter_url] + urls
