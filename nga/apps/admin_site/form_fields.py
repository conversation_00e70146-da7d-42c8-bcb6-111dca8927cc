from enum import Enum
from typing import Any, Type

from django import forms
from django.core.exceptions import ValidationError
from django.forms.widgets import Widget

from nga.core.enums import get_choices


class EnumMultipleChoiceField(forms.MultipleChoiceField):
    """ChoiceField that uses for choices Enum values."""

    def __init__(
        self,
        *,
        enum_class: Type[Enum],
        widget: Type[Widget] = forms.CheckboxSelectMultiple,
        required: bool = True,
        **kwargs: Any
    ) -> None:

        choices = get_choices(enum_class)

        super().__init__(choices=choices, widget=widget, required=required, **kwargs)

    def to_python(self, value: str) -> list[Enum]:
        if not value:
            return []

        elif not isinstance(value, (list, tuple)):
            raise ValidationError(self.error_messages["invalid_list"], code="invalid_list")

        return [int(val) for val in value]
