from django.db.models import Model, QuerySet

from nga.apps.references.infra.orm.models import Country, Operator

_DEFAULT_TRUNCATE_AFTER = 3


def truncate_operators_to_str(operators: QuerySet[Operator], truncate_after: int = _DEFAULT_TRUNCATE_AFTER) -> str:
    return truncate_related_field_to_str(operators, "pmn_code", truncate_after)


def truncate_countries_to_str(countries: QuerySet[Country], truncate_after: int = _DEFAULT_TRUNCATE_AFTER) -> str:
    return truncate_related_field_to_str(countries, "name", truncate_after)


def truncate_related_field_to_str(
    queryset: QuerySet[Model], field_name: str, truncate_after: int = _DEFAULT_TRUNCATE_AFTER
) -> str:
    """Returns string with truncated model values"""

    names = queryset.values_list(field_name, flat=True)
    display_value = ", ".join(names[:truncate_after])

    if len(names) > truncate_after:
        display_value += " and ..."

    return display_value


def truncate_sequence_of_string(string: str, truncate_after: int = _DEFAULT_TRUNCATE_AFTER) -> str:
    """Returns string with truncated input sequence as strings"""

    all_values = string.split(", ")
    display_value = ", ".join(all_values[:truncate_after])

    if len(all_values) > truncate_after:
        display_value += " and ..."

    return display_value
