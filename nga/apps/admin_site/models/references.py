from typing import Any

from django.contrib import admin
from django.contrib.postgres.aggregates import StringAgg
from django.db.models import F, Model, QuerySet
from django.http import HttpRequest
from import_export.formats.base_formats import CSV
from more_admin_filters import DropdownFilter
from rangefilter.filters import DateRangeFilter

from nga.apps.admin_site.filters import (
    HomeOperatorAutocompleteFilter,
    HomeOperatorMultiSelectDropdownFilter,
    PartnerCountryCodeMultiSelectDropdownFilter,
    PartnerOperatorAutocompleteFilter,
    PartnerOperatorMultiSelectDropdownFilter,
    TrafficSegmentAutocompleteFilter,
)
from nga.apps.admin_site.import_mixins import S3MediaImportMixin
from nga.apps.admin_site.mixins import BothOperatorsFieldsMixin
from nga.apps.admin_site.models.references_traffic import TrafficRecordWithTrafficSegmentAdminMixin
from nga.apps.admin_site.utils import truncate_sequence_of_string
from nga.apps.common.expressions import partner_country_names
from nga.apps.import_export_data.resources.references import CountryResource, OperatorResource
from nga.apps.references.infra.orm import models


@admin.register(models.Operator)
class OperatorAdmin(S3MediaImportMixin, admin.ModelAdmin):
    autocomplete_fields = ("country",)
    list_display = ("id", "pmn_code", "name", "country")
    sortable_by = list_display
    list_display_links = ("pmn_code",)
    list_filter = (("country__code", DropdownFilter),)
    search_fields = ("id", "pmn_code", "name", "country__name", "country__code")
    ordering = ("pmn_code",)
    readonly_fields = ("id",)

    # Import settings
    resource_classes = [OperatorResource]
    formats = [CSV]


@admin.register(models.HomeOperator)
class HomeOperatorAdmin(admin.ModelAdmin):
    autocomplete_fields = ("operator",)
    list_display = ("id", "operator")
    sortable_by = list_display
    list_display_links = ("operator",)
    search_fields = ("id", "operator__pmn_code", "operator__name")
    ordering = ("operator__pmn_code",)
    readonly_fields = ("id",)

    show_facets = admin.ShowFacets.NEVER


@admin.register(models.ExchangeRate)
class ExchangeRateAdmin(admin.ModelAdmin):
    list_display = ("currency_code", "value", "date", "is_mapped")
    sortable_by = list_display
    search_fields = ("currency_code",)
    list_filter = (
        ("date", DateRangeFilter),
        "is_mapped",
        ("currency_code", DropdownFilter),
    )
    change_list_template = "admin/exchange_rates/change_list.html"


@admin.register(models.Country)
class CountryAdmin(S3MediaImportMixin, admin.ModelAdmin):
    list_display = ("id", "code", "name")
    sortable_by = list_display
    list_display_links = ("code",)
    ordering = ("code",)
    readonly_fields = ("id",)
    search_fields = ("id", "code", "name")

    # Import settings
    resource_classes = [CountryResource]
    formats = [CSV]


@admin.register(models.CountryGroup)
class CountryGroupAdmin(admin.ModelAdmin):
    autocomplete_fields = ("countries",)
    list_display = ("id", "name", "country_list")
    sortable_by = list_display
    search_fields = ("id", "name", "countries__name")
    ordering = ("name",)
    readonly_fields = ("id",)

    def get_queryset(self, request: HttpRequest) -> QuerySet[Any]:
        qs: QuerySet[Any] = super().get_queryset(request)
        qs = qs.annotate(country_names=StringAgg(F("countries__name"), ", ", distinct=True, default=""))

        return qs

    @admin.display(ordering="country_names")
    def country_list(self, obj: models.CountryGroup) -> str:
        return obj.country_names


@admin.register(models.CountryPhoneCode)
class CountryPhoneCodeAdmin(admin.ModelAdmin):
    autocomplete_fields = ("country",)
    list_display = ("id", "country", "phone_code")
    sortable_by = list_display
    list_filter = (("country__code", DropdownFilter),)
    search_fields = ("id", "phone_code", "country__code", "country__name")
    ordering = ("phone_code",)
    readonly_fields = ("id",)

    show_facets = admin.ShowFacets.NEVER


@admin.register(models.TrafficSegment)
class TrafficSegmentAdmin(admin.ModelAdmin):
    autocomplete_fields = ("home_operator",)
    list_display = ("id", "home_operator", "name", "description")
    sortable_by = list_display
    list_filter = (HomeOperatorAutocompleteFilter,)
    search_fields = ("id", "name", "description", "home_operator__pmn_code", "home_operator__name")
    ordering = ("name",)
    readonly_fields = ("id",)

    show_facets = admin.ShowFacets.NEVER


@admin.register(models.ClientCurrency)
class ClientCurrencyAdminModel(admin.ModelAdmin):
    list_display = (
        "id",
        "code",
    )


@admin.register(models.ExternalCalculatedTrafficRecord)
class ExternalCalculatedTrafficRecordAdmin(TrafficRecordWithTrafficSegmentAdminMixin, admin.ModelAdmin):
    list_display = (
        "pk",
        "home_operator_pmn",
        "partner_operator_pmn",
        "traffic_month",
        "traffic_type",
        "traffic_direction",
        "service_type",
        "call_destination",
        "traffic_segment",
        "volume_actual",
        "volume_billed",
        "tap_charge_net",
        "tap_charge_gross",
        "charge_net",
        "charge_gross",
        "created_at",
    )

    sortable_by = list_display

    list_filter = (
        HomeOperatorAutocompleteFilter,
        PartnerOperatorAutocompleteFilter,
        "traffic_type",
        "traffic_direction",
        "service_type",
        "call_destination",
        ("traffic_month", DateRangeFilter),
        ("created_at", DateRangeFilter),
        TrafficSegmentAutocompleteFilter,
    )

    autocomplete_fields = (
        "home_operator",
        "partner_operator",
        "traffic_segment",
    )

    actions = ("delete_all_records",)

    search_fields = ("pk",)

    show_facets = admin.ShowFacets.NEVER

    @classmethod
    @admin.display(ordering="home_operator__pmn_code")
    def home_operator_pmn(cls, obj: Model) -> str:
        return obj.home_operator_pmn

    @classmethod
    @admin.display(ordering="partner_operator__pmn_code")
    def partner_operator_pmn(cls, obj: Model) -> str:
        return obj.partner_operator_pmn

    @admin.action(description="Delete all records at once (requires to select at least one record)")
    def delete_all_records(self, request: HttpRequest, queryset: QuerySet) -> None:
        models.ExternalCalculatedTrafficRecord.objects.all().delete()


@admin.register(models.PredefinedFilter)
class PredefinedFilterAdmin(BothOperatorsFieldsMixin, admin.ModelAdmin):
    autocomplete_fields = (
        "home_operators",
        "partner_operators",
        "partner_countries",
    )
    list_display = (
        "id",
        "name",
        "home_operator_list",
        "partner_operator_list",
        "partner_country_list",
        "start_date",
        "end_date",
    )
    sortable_by = list_display
    list_filter = (
        ("home_operators__pmn_code", HomeOperatorMultiSelectDropdownFilter),
        ("partner_operators__pmn_code", PartnerOperatorMultiSelectDropdownFilter),
        ("partner_countries__code", PartnerCountryCodeMultiSelectDropdownFilter),
        ("start_date", DateRangeFilter),
        ("end_date", DateRangeFilter),
    )
    search_fields = (
        "id",
        "name",
    )
    ordering = ("name",)
    readonly_fields = ("id",)

    show_facets = admin.ShowFacets.NEVER

    def get_queryset(self, request: HttpRequest) -> QuerySet[models.PredefinedFilter]:
        qs = super().get_queryset(request)

        qs = qs.annotate(partner_country_names=partner_country_names)

        return qs

    @admin.display(ordering="partner_country_names")
    def partner_country_list(self, obj: models.CountryGroup) -> str:
        partner_countries: str = obj.partner_country_names

        return truncate_sequence_of_string(partner_countries)


@admin.register(models.EUOperator)
class EUOperatorAdmin(admin.ModelAdmin):
    autocomplete_fields = ("operator",)
    list_display = ("id", "operator")
    sortable_by = list_display
    list_display_links = ("operator",)
    search_fields = ("id", "operator__pmn_code", "operator__name")
    ordering = ("operator__pmn_code",)
    readonly_fields = ("id",)

    show_facets = admin.ShowFacets.NEVER
