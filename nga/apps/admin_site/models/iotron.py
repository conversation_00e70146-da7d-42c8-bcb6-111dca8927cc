from typing import Any

from django.contrib import admin
from django.db.models import QuerySet
from django.http import HttpRequest
from rangefilter.filters import DateRangeFilter

from nga.apps.import_export_data.tasks.processing import process_external_agreement
from nga.apps.iotron.infra.orm.models import ExternalAgreement, Notification
from nga.utils.collections import to_id_list


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    fields = (
        "type",
        "received_at",
        "processed_at",
        "payload",
    )

    list_display = (
        "id",
        "type",
        "received_at",
        "processed_at",
        "payload",
    )

    list_filter = (
        "type",
        ("received_at", DateRangeFilter),
    )

    ordering = ("-received_at",)

    def has_add_permission(self, request: HttpRequest) -> bool:  # pragma: no cover
        return False

    def has_change_permission(self, *args: Any) -> bool:  # pragma: no cover
        return False

    def has_delete_permission(self, *args: Any) -> bool:  # pragma: no cover
        return False


@admin.register(ExternalAgreement)
class ExternalAgreementAdmin(admin.ModelAdmin):
    list_display = (
        "pk",
        "processing_status",
        "external_id",
        "name",
        "home_operators",
        "partner_operators",
        "start_date",
        "end_date",
        "do_not_calculate",
        "include_satellite",
        "include_premium",
        "include_premium_in_commitment",
        "is_rolling",
        "include_access_fee_in_sop_financial_inbound",
        "include_access_fee_in_sop_financial_outbound",
        "terminated_at",
        "created_at",
        "updated_at",
        "processed_at",
    )
    sortable_by = list_display
    list_filter = (
        ("start_date", DateRangeFilter),
        ("end_date", DateRangeFilter),
        "processing_status",
        "do_not_calculate",
        "include_satellite",
        "include_premium",
        "include_premium_in_commitment",
        "is_rolling",
        "include_access_fee_in_sop_financial_inbound",
        "include_access_fee_in_sop_financial_outbound",
    )
    search_fields = (
        "pk",
        "external_id",
        "name",
        "home_operators",
        "partner_operators",
    )

    actions = ["process_to_agreement"]

    show_facets = admin.ShowFacets.NEVER

    def process_to_agreement(self, request: HttpRequest, queryset: QuerySet[ExternalAgreement]) -> None:
        process_external_agreement(external_agreement_ids=to_id_list(queryset), processing_status=None)
