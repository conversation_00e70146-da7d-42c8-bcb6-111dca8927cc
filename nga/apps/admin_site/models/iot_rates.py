from django.contrib import admin
from django.contrib.postgres.aggregates import StringAgg
from django.db.models import F, QuerySet
from django.http import HttpRequest
from more_admin_filters import MultiSelectDropdownFilter
from rangefilter.filters import DateRangeFilter

from nga.apps.admin_site.filters import (
    CalledCountryCodeMultiSelectDropdownFilter,
    HomeOperatorMultiSelectDropdownFilter,
    PartnerOperatorMultiSelectDropdownFilter,
)
from nga.apps.admin_site.utils import truncate_sequence_of_string
from nga.apps.iot_rates.models import IOTRate


@admin.register(IOTRate)
class IOTRateAdminModel(admin.ModelAdmin):
    autocomplete_fields = (
        "home_operator",
        "partner_operator",
        "called_countries",
    )
    list_display = (
        "pk",
        "hpmn",
        "ppmn",
        "traffic_direction",
        "service_type",
        "start_date",
        "end_date",
        "type",
        "value",
        "currency_code",
        "is_premium",
        "called_country_codes",
    )
    list_filter = (
        ("home_operator__pmn_code", HomeOperatorMultiSelectDropdownFilter),
        ("partner_operator__pmn_code", PartnerOperatorMultiSelectDropdownFilter),
        "traffic_direction",
        "service_type",
        "type",
        ("currency_code", MultiSelectDropdownFilter),
        ("start_date", DateRangeFilter),
        ("end_date", DateRangeFilter),
        "is_premium",
        ("called_countries__code", CalledCountryCodeMultiSelectDropdownFilter),
    )

    search_fields = (
        "currency_code",
        "value",
    )

    show_facets = admin.ShowFacets.NEVER

    def get_queryset(self, request: HttpRequest) -> QuerySet[IOTRate]:
        qs = super().get_queryset(request)

        qs = qs.annotate(
            home_operator_pmn=F("home_operator__pmn_code"),
            partner_operator_pmn=F("partner_operator__pmn_code"),
            called_country_codes=StringAgg(F("called_countries__code"), distinct=True, delimiter=", "),
        )

        return qs

    @classmethod
    @admin.display(ordering="home_operator__pmn_code")
    def hpmn(cls, obj: IOTRate) -> str:
        return obj.home_operator_pmn

    @classmethod
    @admin.display(ordering="partner_operator__pmn_code")
    def ppmn(cls, obj: IOTRate) -> str:
        return obj.partner_operator_pmn

    @classmethod
    def called_country_codes(cls, obj: IOTRate) -> str:
        if obj.called_country_codes is not None:
            return truncate_sequence_of_string(obj.called_country_codes)

        return ""
