from django.contrib import admin
from django.db.models import F, Model, QuerySet
from django.http import HttpRequest
from import_export.admin import ExportMixin
from rangefilter.filters import DateRangeFilter

from nga.apps.admin_site.filters import (
    CalledCountryCodeMultiSelectDropdownFilter,
    HomeOperatorAutocompleteFilter,
    HomeOperatorMultiSelectDropdownFilter,
    MultiChoicesSelectFilter,
    PartnerOperatorAutocompleteFilter,
    PartnerOperatorMultiSelectDropdownFilter,
    TrafficSegmentAutocompleteFilter,
)
from nga.apps.import_export_data.resources.traffic import ExportMonthlyAggregatedTrafficRecordResource
from nga.apps.references.infra.orm.models import IMSICountRecord, MonthlyAggregatedTrafficRecord
from nga.core.enums import CallDestinationEnum, ServiceTypeEnum, get_rev_choices_map


class TrafficRecordWithHomeAndPartnerOperatorsAdminMixin:
    def get_queryset(self, request: HttpRequest) -> QuerySet:
        """
        Overriden due to solve n+1 query problem. Adds to objects related
        fields needed to be displayed in a table.
        """

        queryset: QuerySet[Model] = super().get_queryset(request)  # type: ignore[misc]

        queryset = queryset.annotate(
            home_operator_pmn=F("home_operator__pmn_code"),
            partner_operator_pmn=F("partner_operator__pmn_code"),
        ).select_related(
            "home_operator",
            "partner_operator",
        )

        return queryset

    @classmethod
    @admin.display(ordering="home_operator__pmn_code")
    def home_operator_pmn(cls, obj: Model) -> str:
        return obj.home_operator_pmn

    @classmethod
    @admin.display(ordering="partner_operator__pmn_code")
    def partner_operator_pmn(cls, obj: Model) -> str:
        return obj.partner_operator_pmn


class TrafficRecordWithTrafficSegmentAdminMixin(TrafficRecordWithHomeAndPartnerOperatorsAdminMixin):
    def get_queryset(self, request: HttpRequest) -> QuerySet:

        queryset = super().get_queryset(request)

        queryset = queryset.annotate(
            traffic_segment_name=F("traffic_segment__name"),
        ).select_related("traffic_segment")

        return queryset

    @classmethod
    @admin.display(ordering="traffic_segment__name")
    def traffic_segment_name(cls, obj: Model) -> str:
        return obj.traffic_segment_name


class TrafficRecordWithCalledCountryAdminMixin(TrafficRecordWithTrafficSegmentAdminMixin):
    def get_queryset(self, request: HttpRequest) -> QuerySet:

        queryset = super().get_queryset(request)

        queryset = queryset.annotate(
            called_country_code=F("called_country__code"),
        ).select_related("called_country")

        return queryset

    @classmethod
    @admin.display(ordering="called_country__code")
    def called_country_code(cls, obj: Model) -> str:
        return obj.called_country_code


@admin.register(MonthlyAggregatedTrafficRecord)
class MonthlyAggregatedTrafficRecordAdmin(
    ExportMixin,
    TrafficRecordWithCalledCountryAdminMixin,
    admin.ModelAdmin,
):
    autocomplete_fields = ("home_operator", "partner_operator", "called_country", "traffic_segment")
    list_display = (
        "pk",
        "home_operator_pmn",
        "partner_operator_pmn",
        "traffic_month",
        "traffic_type",
        "traffic_direction",
        "service_type",
        "call_destination",
        "called_country_code",
        "is_premium",
        "traffic_segment_name",
        "volume_actual",
        "volume_billed",
        "tap_charge_net",
        "tap_charge_gross",
        "charge_net",
        "charge_gross",
    )
    sortable_by = list_display
    list_filter = (
        ("home_operator__pmn_code", HomeOperatorMultiSelectDropdownFilter),
        ("partner_operator__pmn_code", PartnerOperatorMultiSelectDropdownFilter),
        ("traffic_month", DateRangeFilter),
        "traffic_type",
        "traffic_direction",
        ("service_type", MultiChoicesSelectFilter),
        ("call_destination", MultiChoicesSelectFilter),
        ("called_country__code", CalledCountryCodeMultiSelectDropdownFilter),
        "is_premium",
        TrafficSegmentAutocompleteFilter,
    )
    search_fields = (
        "pk",
        "home_operator__pmn_code",
        "partner_operator__pmn_code",
        "called_country__code",
    )
    readonly_fields = (
        "created_at",
        "updated_at",
    )
    ordering = (
        "home_operator__pmn_code",
        "partner_operator__pmn_code",
        "traffic_month",
    )
    list_per_page = 22
    resource_classes = [ExportMonthlyAggregatedTrafficRecordResource]

    show_facets = admin.ShowFacets.NEVER

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        """
        Overriden due to solve n+1 query problem. Adds to objects related
        fields needed to be displayed in a table.
        """

        queryset = super().get_queryset(request)

        service_type_str = request.GET.get("service_type__in")
        service_type_vals = service_type_str.split(",") if service_type_str else list()

        if service_type_vals:
            service_type_mapper = get_rev_choices_map(ServiceTypeEnum)
            service_type_list = [service_type_mapper[service_type_val] for service_type_val in service_type_vals]
            queryset = queryset.filter(service_type__in=service_type_list)

        call_destination_str = request.GET.get("call_destination__in")
        call_destination_vals = call_destination_str.split(",") if call_destination_str else list()

        if call_destination_vals:
            call_destination_mapper = get_rev_choices_map(CallDestinationEnum)
            call_destination_list = [
                call_destination_mapper[call_destination_val] for call_destination_val in call_destination_vals
            ]
            queryset = queryset.filter(call_destination__in=call_destination_list)

        return queryset


@admin.register(IMSICountRecord)
class IMSICountRecordAdmin(TrafficRecordWithHomeAndPartnerOperatorsAdminMixin, admin.ModelAdmin):
    list_display = (
        "pk",
        "home_operator_pmn",
        "partner_operator_pmn",
        "traffic_month",
        "traffic_direction",
        "traffic_segment",
        "count",
        "data_count",
    )
    sortable_by = list_display

    list_filter = (
        ("traffic_month", DateRangeFilter),
        "traffic_direction",
        HomeOperatorAutocompleteFilter,
        PartnerOperatorAutocompleteFilter,
    )
    readonly_fields = (
        "created_at",
        "updated_at",
    )
    search_fields = ("pk", "home_operator_pmn", "partner_operator_pmn")

    autocomplete_fields = (
        "home_operator",
        "partner_operator",
    )

    show_facets = admin.ShowFacets.NEVER
