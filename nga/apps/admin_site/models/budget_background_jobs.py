from typing import Optional

from django.contrib import admin
from django.http import HttpRequest

from nga.apps.admin_site.filters import BudgetAutocompleteFilter
from nga.apps.budget_background_jobs.orm_models import BudgetBackgroundJob


@admin.register(BudgetBackgroundJob)
class BudgetBackgroundJobAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "budget_id",
        "type",
        "status",
        "created_at",
        "started_at",
        "finished_at",
    )
    list_filter = (
        BudgetAutocompleteFilter,
        "type",
        "status",
    )
    readonly_fields = (
        "id",
        "budget",
        "type",
        "task_id",
        "created_at",
        "started_at",
        "finished_at",
        "error_message",
        "error_traceback",
        "params",
    )
    ordering = ("-created_at",)

    def has_add_permission(
        self,
        request: HttpRequest,
        obj: Optional[BudgetBackgroundJob] = None,
    ) -> bool:  # pragma: no cover
        return False

    def has_delete_permission(
        self,
        request: HttpRequest,
        obj: Optional[BudgetBackgroundJob] = None,
    ) -> bool:  # pragma: no cover
        return False
