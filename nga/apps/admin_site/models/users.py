from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin

from nga.apps.users.forms import UserChangeForm, UserCreationForm
from nga.apps.users.models import User


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    add_form = UserCreationForm
    form = UserChangeForm
    model = User
    list_display = (
        "email",
        "is_active",
        "is_staff",
        "is_superuser",
    )
    list_filter = (
        "email",
        "is_active",
        "is_staff",
        "is_superuser",
    )
    fieldsets = (
        (None, {"fields": ("email",)}),
        ("Permissions", {"fields": ("is_active", "is_staff", "is_superuser")}),
    )
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "email",
                    "is_active",
                    "is_staff",
                    "is_superuser",
                ),
            },
        ),
    )
    search_fields = ("email",)
    ordering = ("email",)
