import traceback
from typing import Any, Type

from django.contrib import messages
from django.http import HttpRequest, HttpResponse
from django.views.generic import FormView

from nga.apps.admin_site.file_processors import File, FileProcessor, FileValidationError
from nga.apps.admin_site.forms import ImportFileForm


class ImportFileFormView(FormView):
    form_class: Type[ImportFileForm] = ImportFileForm
    file_processor_class: Type[FileProcessor]
    success_message: str

    def post(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        form = self.get_form()

        if not form.is_valid():
            return self.form_invalid(form)

        uploaded_file = form.cleaned_data["file"]
        file = File(file=uploaded_file.file, name=uploaded_file.name)
        file_processor = self.create_file_processor(file)

        try:
            file_processor.validate()
        except FileValidationError as e:
            form.add_error("file", str(e))
            return self.form_invalid(form)

        self.process_file(file_processor)

        return self.form_valid(form)

    def process_file(self, file_processor: FileProcessor) -> None:
        try:
            file_processor.process()
            messages.success(self.request, self.success_message)
        except Exception as e:
            msg = f"File processing ended with error: {str(e)}"
            messages.error(self.request, msg)
            traceback.print_exc()

    def create_file_processor(self, file: File) -> FileProcessor:
        return self.file_processor_class(file)
