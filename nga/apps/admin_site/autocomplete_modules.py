import json
from types import NoneType
from typing import Any, Callable, Iterable, Optional, Type

from admin_auto_filters.filters import AutocompleteFilt<PERSON>, AutocompleteSelect
from django.contrib import admin
from django.contrib.admin.widgets import AutocompleteMixin
from django.core.handlers.wsgi import WSGIRequest
from django.db.models import Model, QuerySet


class ModelAutocompleteSelect(AutocompleteSelect):
    def build_attrs(self, base_attrs: dict[str, Any], extra_attrs: Optional[dict[str, Any]] = None) -> dict[str, Any]:
        attrs: dict[str, Any] = super(AutocompleteMixin, self).build_attrs(base_attrs, extra_attrs=extra_attrs)
        attrs.setdefault("class", "")

        attrs.update(
            {
                "data-ajax--cache": "true",
                "data-ajax--delay": 250,
                "data-ajax--type": "GET",
                "data-ajax--url": self.get_url(),
                "data-app-label": self.field._meta.app_label,
                "data-model-name": self.field._meta.model_name,
                "data-field-name": self.field.__name__,
                "data-theme": "admin-autocomplete",
                "data-allow-clear": json.dumps(not self.is_required),
                "data-placeholder": "",
                "lang": self.i18n_name,
                "class": attrs["class"] + (" " if attrs["class"] else "") + "admin-autocomplete",
            }
        )

        return attrs

    def optgroups(self, name: str, value: list[str], attr: Optional[dict[str, Any]] = None) -> list[Any]:
        default: tuple[None, list[Any], int] = (None, [], 0)
        groups = [default]
        has_selected = False

        selected_choices = {str(v) for v in value if str(v) not in self.choices.field.empty_values}
        if not self.is_required and not self.allow_multiple_selected:
            default[1].append(self.create_option(name, "", "", False, 0))

        remote_model_opts = self.field._meta
        to_field_name = getattr(self.field, "field_name", remote_model_opts.pk.attname)
        to_field_name = remote_model_opts.get_field(to_field_name).attname

        choices = (
            (getattr(obj, to_field_name), self.choices.field.label_from_instance(obj))
            for obj in self.choices.queryset.using(self.db).filter(**{"%s__in" % to_field_name: selected_choices})
        )

        for option_value, option_label in choices:
            selected = str(option_value) in value and (has_selected is False or self.allow_multiple_selected)
            has_selected |= selected
            index = len(default[1])
            subgroup = default[1]
            subgroup.append(self.create_option(name, option_value, option_label, selected_choices, index))

        return groups


class ModelAutocompleteFilter(AutocompleteFilter):
    parameter_name: Optional[str]
    use_pk_exact = False
    field_name = ""
    mapper_fields: list[tuple[str, Callable[[Model], Any]]]

    def __init__(self, request: WSGIRequest, params: dict[str, Any], model: Type[Model], model_admin: admin.ModelAdmin):
        if self.parameter_name is None:
            self.parameter_name = self.field_name
            if self.use_pk_exact:
                self.parameter_name += "__{}__exact".format(self.field_pk)
        super(AutocompleteFilter, self).__init__(request, params, model, model_admin)

        if self.rel_model:
            model = self.rel_model

        widget = ModelAutocompleteSelect(
            model,
            model_admin.admin_site,
            custom_url=self.get_autocomplete_url(request, model_admin),
        )

        form_field = self.get_form_field()
        field = form_field(
            queryset=model.objects.order_by("pk").all(),
            widget=widget,
            required=False,
        )

        self._add_media(model_admin, widget)

        attrs = self.widget_attrs.copy()
        attrs["id"] = "id-%s-dal-filter" % self.parameter_name
        if self.is_placeholder_title:
            attrs["data-Placeholder"] = self.title
        self.rendered_widget = field.widget.render(
            name=self.parameter_name, value=self.used_parameters.get(self.parameter_name, ""), attrs=attrs
        )

    def queryset(self, request: WSGIRequest, queryset: QuerySet[Model]) -> QuerySet[Model]:
        if self.value():
            try:
                instance = self.rel_model.objects.get(pk=self.value())
                filter_conditions = {}
                for key, func in self.mapper_fields:
                    value = func(instance)

                    if value or not isinstance(value, (NoneType, Iterable)):
                        filter_conditions[key] = value

                return queryset.filter(**filter_conditions)

            except self.rel_model.DoesNotExist:
                return queryset.none()
        else:
            return queryset
