import abc
import codecs
import csv
from abc import ABC
from datetime import date, datetime
from enum import Enum
from pathlib import Path
from typing import IO, ClassVar, Iterator

from dateutil.relativedelta import relativedelta
from django.db.transaction import atomic

from nga.apps.references.infra.orm.models import ExchangeRate


class FileError(Exception):
    ...  # pragma: nocover


class FileValidationError(FileError):
    ...  # pragma: nocover


class File:
    def __init__(self, file: IO[bytes], name: str) -> None:
        self.file = file
        self.name = name


class FileProcessor(abc.ABC):
    FILE_EXTENSION: ClassVar[str]
    FILE_EXTENSION_ERROR_MESSAGE = (
        "File extension “{extension}” is not allowed. Allowed extension is: {allowed_extension}."
    )

    class FieldNames(Enum):
        ...

    def __init__(self, file: File):
        self._file = file

    def validate(self) -> None:
        self._validate_file_extension()
        self._validate()

    @abc.abstractmethod
    def _validate(self) -> None:
        ...  # pragma: nocover

    @abc.abstractmethod
    def process(self) -> None:
        ...  # pragma: nocover

    def _validate_file_extension(self) -> None:
        extension = Path(self._file.name).suffix[1:].lower()
        if self.FILE_EXTENSION is not None and extension != self.FILE_EXTENSION:
            err = self.FILE_EXTENSION_ERROR_MESSAGE.format(extension=extension, allowed_extension=self.FILE_EXTENSION)
            raise FileValidationError(err)


class CSVFileProcessor(FileProcessor, ABC):
    FILE_EXTENSION = "csv"

    @classmethod
    def field_name_values(cls) -> list[str]:
        return [str(f.value) for f in cls.FieldNames]

    def get_dict_file_reader(self) -> csv.DictReader:  # type: ignore[type-arg]
        return csv.DictReader(self._get_decoded_file_content())

    def _get_decoded_file_content(self) -> Iterator[str]:
        self._file.file.seek(0)
        return codecs.iterdecode(self._file.file, "utf-8")

    def get_file_reader(self) -> Iterator[list[str]]:
        return csv.reader(self._get_decoded_file_content())

    def _validate(self) -> None:
        self._validate_field_names()

    def _validate_field_names(self) -> None:
        reader = self.get_dict_file_reader()
        missed_fields = set(self.field_name_values()).difference(reader.fieldnames)  # type: ignore
        if missed_fields:
            err_msg = f"File has missed fields or fields contain errors: {missed_fields}"
            raise FileValidationError(err_msg)


class ExchangeRatesGSMAFileProcessor(CSVFileProcessor):
    """
    ExchangeRatesGSMAFileProcessor imports exchange rates provided from GSMA. GSMA file contains
    exchange rates with pegged dates. As a result imported exchange rates will be with fixed dates
    that are allowed to be used in calculations without extra preparation.
    """

    FILE_DATE_FMT = "%Y%m%d"
    INVALID_COLUMNS_ERROR_MESSAGE = "Invalid number of columns. Must be {expected_columns_count}: {expected_columns}"

    class FieldNames(Enum):
        # unused columns
        TYPE = "Type"
        SOURCE = "Source"
        #
        CURRENCY_CODE = "Currency Code"
        RATE_VALUE = "Rate Value"
        DATE = "Date"

    def _validate(self) -> None:
        reader = self.get_file_reader()
        first_row = next(reader)
        expected_columns_count = len(self.field_name_values())

        if len(first_row) != expected_columns_count:
            error_message = self.INVALID_COLUMNS_ERROR_MESSAGE.format(
                expected_columns_count=expected_columns_count,
                expected_columns=self.field_name_values(),
            )
            raise FileValidationError(error_message)

    @atomic
    def process(self) -> None:
        reader = self.get_file_reader()

        for _, source, currency_code, value, date_str in reader:
            ExchangeRate.objects.update_or_create(
                currency_code=currency_code,
                date=self.get_fixed_date(date_str),
                defaults=dict(value=value),
            )

    def get_fixed_date(self, date_str: str) -> date:
        """
        File that is parsed by current processor, processes files from GSMA. And GSMA provides us
        exchange rates with pegged dates (date rate was added), but our system in calculations
        needs fixed exchange rates.

        :param date_str:
        :return: date with 1st day and previous month
        """

        date_obj = datetime.strptime(date_str, self.FILE_DATE_FMT)
        fixed_date: date = (date_obj.replace(day=1) + relativedelta(months=1)).date()

        return fixed_date
