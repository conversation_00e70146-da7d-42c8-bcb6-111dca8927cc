from typing import Any

from django.conf import settings
from import_export.admin import ImportMixin
from import_export.formats.base_formats import TablibFormat, TextFormat
from import_export.tmp_storages import BaseStorage, MediaStorage


class BinaryText(TablibFormat):
    @classmethod
    def create_from_format(cls, fmt: TablibFormat) -> "BinaryText":
        _format = cls()

        _format.TABLIB_MODULE = fmt.TABLIB_MODULE
        _format.CONTENT_TYPE = fmt.CONTENT_TYPE

        return _format


class S3MediaImportMixin(ImportMixin):
    """
    When IMPORT_EXPORT_TMP_STORAGE_CLASS for django_import_export is set MediaStorage, and S3 is
    used as file storage, django_storages will try to upload file into bucket.
    django-storages expects binary input stream for uploading, but django_import_export decodes
    binary input to string and passes it for saving. When django-storages gets string for
    uploading, it fails during getting md5 hash. In order to upload file to S3 storage,
    it is needed to omit data decoding. django_import_export decodes file body when:
    tmp storage class is MediaStorage and input format is not binary.

    S3MediaImportMixin overrides write_to_tmp_storage, and replaces text format (CSV, JSON, YAML,
    HTML) with format, that has binary properties (TablibFormat).
    """

    def write_to_tmp_storage(self, import_file: Any, input_format: TablibFormat) -> BaseStorage:
        """Overriden to adjust file input format if temp storage is S3."""

        input_format = self.adjust_input_format_if_storage_is_s3(input_format)

        return super().write_to_tmp_storage(import_file, input_format)

    def adjust_input_format_if_storage_is_s3(self, input_format: TablibFormat) -> TablibFormat:
        """If temp storage is S3 it requires binary input format instead of string."""

        input_is_text = isinstance(input_format, TextFormat)
        s3_enabled = settings.USE_S3
        tmp_storage_is_media = self.get_tmp_storage_class() is MediaStorage

        if input_is_text and s3_enabled and tmp_storage_is_media:
            input_format = BinaryText.create_from_format(input_format)

        return input_format
