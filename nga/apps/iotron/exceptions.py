from nga.core.exceptions import BaseNGAException

__all__ = [
    "ExternalAgreementDoesNotExist",
]


class ExternalAgreementDoesNotExist(BaseNGAException):
    _template = "External Agreement does not exist id={external_agreement_id}"

    def __init__(self, external_agreement_id: int) -> None:
        msg = self._template.format(external_agreement_id=external_agreement_id)
        super().__init__(msg)
