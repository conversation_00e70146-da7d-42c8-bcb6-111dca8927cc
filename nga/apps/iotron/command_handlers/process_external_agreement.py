from dependency_injector.wiring import Closing, Provide
from mediatr import Mediator

from nga.apps.iotron.commands import ProcessExternalAgreementCommand
from nga.apps.iotron.domain.models import ExternalAgreement
from nga.apps.iotron.domain.repositories.external_agreements import AbstractExternalAgreementRepository
from nga.apps.iotron.processing.service import AbstractExternalAgreementService


@Mediator.handler
class ProcessExternalAgreementCommandHandler:
    INTERSECTION_ERROR_TEMPLATE = "During processing were found {intersection_type} agreements: {ids_list}"

    def __init__(
        self,
        external_agreement_service: AbstractExternalAgreementService = Closing[Provide["external_agreement_service"]],
        external_agreement_repository: AbstractExternalAgreementRepository = Closing[
            Provide["external_agreement_repository"]
        ],
    ) -> None:
        self._external_agreement_repository = external_agreement_repository
        self._external_agreement_service = external_agreement_service

    def handle(self, cmd: ProcessExternalAgreementCommand) -> ExternalAgreement:
        external_agreement = self._external_agreement_repository.get_by_id(cmd.external_agreement_id)

        return self._external_agreement_service.process(external_agreement, budget_id=cmd.budget_id)
