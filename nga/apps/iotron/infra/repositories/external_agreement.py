from typing import Optional

from nga.apps.agreements.domain.repositories import AbstractAgreementNegotiatorRepository
from nga.apps.iotron.domain.dto import DiscountDict, DiscountParameterDict
from nga.apps.iotron.domain.models import ExternalAgreement
from nga.apps.iotron.domain.repositories.external_agreements import AbstractExternalAgreementRepository
from nga.apps.iotron.domain.utils import from_values_to_ids
from nga.apps.iotron.enums import ExternalAgreementProcessingStatusEnum
from nga.apps.iotron.exceptions import ExternalAgreementDoesNotExist
from nga.apps.iotron.infra.orm import models
from nga.apps.references.providers import (
    AbstractCountryProvider,
    AbstractOperatorProvider,
    AbstractTrafficSegmentProvider,
)
from nga.core.types import DatePeriod


class ExternalAgreementDjangoORMRepository(AbstractExternalAgreementRepository):
    def __init__(
        self,
        operator_provider: AbstractOperatorProvider,
        country_provider: AbstractCountryProvider,
        traffic_segment_provider: AbstractTrafficSegmentProvider,
        agreement_negotiator: AbstractAgreementNegotiatorRepository,
    ) -> None:
        countries = country_provider.get_many()
        operators = operator_provider.get_many()
        traffic_segments = traffic_segment_provider.get_many()
        negotiators = agreement_negotiator.get_many()

        self.operators_map = {o.pmn_code: o.id for o in operators}
        self.countries_map = {c.code: c.id for c in countries}

        reversed_operators_map = {_id: pmn_code for pmn_code, _id in self.operators_map.items()}
        self.traffic_segments_map = {
            f"{reversed_operators_map[ts.home_operator_id]}-{ts.name}": ts.id
            for ts in traffic_segments
            if reversed_operators_map.get(ts.home_operator_id)
        }

        self.negotiators_map = {n.name: n.id for n in negotiators}

    def get_many(
        self, ids: Optional[list[int]] = None, processing_status: Optional[ExternalAgreementProcessingStatusEnum] = None
    ) -> list[ExternalAgreement]:
        queryset = models.ExternalAgreement.objects.order_by("pk").all()

        if ids is not None:
            queryset = queryset.filter(id__in=ids)

        if processing_status is not None:
            queryset = queryset.filter(processing_status=processing_status)

        return list(map(self.from_orm_to_domain, queryset))

    def get_by_id(self, external_agreement_id: int) -> ExternalAgreement:
        try:
            orm_external_agreement = models.ExternalAgreement.objects.get(pk=external_agreement_id)
        except models.ExternalAgreement.DoesNotExist:
            raise ExternalAgreementDoesNotExist(external_agreement_id)

        return self.from_orm_to_domain(orm_external_agreement)

    def save(self, external_agreement: ExternalAgreement) -> ExternalAgreement:
        orm_external_agreement = models.ExternalAgreement.objects.get(pk=external_agreement.id)

        orm_external_agreement.processed_at = external_agreement.processed_at
        orm_external_agreement.processing_status = external_agreement.processing_status
        orm_external_agreement.failed_message = external_agreement.failed_message

        orm_external_agreement.save(update_fields=["processed_at", "processing_status", "failed_message"])
        orm_external_agreement.refresh_from_db()

        return self.from_orm_to_domain(orm_external_agreement)

    def from_orm_to_domain(self, orm_external_agreement: models.ExternalAgreement) -> ExternalAgreement:
        discounts = [
            DiscountDict(
                home_operators=from_values_to_ids(d["home_operators"], self.operators_map),
                partner_operators=from_values_to_ids(d["partner_operators"], self.operators_map),
                direction=d["direction"],
                service_types=d["service_types"],
                start_date=d["start_date"],
                end_date=d["end_date"],
                currency_code=d["currency_code"],
                tax_type=d["tax_type"],
                volume_type=d["volume_type"],
                settlement_method=d["settlement_method"],
                call_destinations=d["call_destinations"],
                qualifying_direction=d["qualifying_direction"],
                qualifying_service_types=d["qualifying_service_types"],
                qualifying_basis=d["qualifying_basis"],
                qualifying_lower_bound=d["qualifying_lower_bound"],
                imsi_count_type=d["imsi_count_type"],
                called_countries=(
                    from_values_to_ids(d["called_countries"], self.countries_map)
                    if d.get("called_countries") is not None
                    else None
                ),
                traffic_segments=(
                    from_values_to_ids(d["traffic_segments"], self.traffic_segments_map)
                    if d.get("traffic_segments") is not None
                    else None
                ),
                discount_parameters=[
                    DiscountParameterDict(
                        calculation_type=dp["calculation_type"],
                        basis=dp.get("basis"),
                        basis_value=dp.get("basis_value"),
                        balancing=dp.get("balancing"),
                        bound_type=dp.get("bound_type"),
                        lower_bound=dp.get("lower_bound"),
                        upper_bound=dp.get("upper_bound"),
                        toll_rate=dp.get("toll_rate"),
                        airtime_rate=dp.get("airtime_rate"),
                        fair_usage_rate=dp.get("fair_usage_rate"),
                        fair_usage_threshold=dp.get("fair_usage_threshold"),
                        access_fee_rate=dp.get("access_fee_rate"),
                        incremental_rate=dp.get("incremental_rate"),
                    )
                    for dp in d["discount_parameters"]
                ],
            )
            for d in orm_external_agreement.discounts
        ]

        return ExternalAgreement(
            id=orm_external_agreement.id,
            external_id=orm_external_agreement.external_id,
            name=orm_external_agreement.name,
            period=DatePeriod(orm_external_agreement.start_date, orm_external_agreement.end_date),
            home_operators=from_values_to_ids(orm_external_agreement.home_operators, self.operators_map),
            partner_operators=from_values_to_ids(orm_external_agreement.partner_operators, self.operators_map),
            negotiator=self.negotiators_map.get(orm_external_agreement.negotiator),
            do_not_calculate=orm_external_agreement.do_not_calculate,
            discounts=discounts,
            processed_at=orm_external_agreement.processed_at,
            processing_status=ExternalAgreementProcessingStatusEnum(orm_external_agreement.processing_status),
            failed_message=orm_external_agreement.failed_message,
            include_satellite=orm_external_agreement.include_satellite,
            include_premium=orm_external_agreement.include_premium,
            include_premium_in_commitment=orm_external_agreement.include_premium_in_commitment,
            is_rolling=orm_external_agreement.is_rolling,
            include_access_fee_in_sop_financial_inbound=bool(
                orm_external_agreement.include_access_fee_in_sop_financial_inbound
            ),
            include_access_fee_in_sop_financial_outbound=bool(
                orm_external_agreement.include_access_fee_in_sop_financial_outbound
            ),
            terminated_at=orm_external_agreement.terminated_at,
        )
