# Generated by Django 5.1.7 on 2025-04-17 21:01

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('iotron', '0001_initial'),
    ]

    operations = [
        migrations.SeparateDatabaseAndState(
            state_operations=[
                migrations.CreateModel(
                    name='ExternalAgreement',
                    fields=[
                        ('id',
                         models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                        ('external_id', models.IntegerField(verbose_name='External ID')),
                        ('name', models.CharField(max_length=248, verbose_name='Agreement Reference')),
                        ('start_date', models.DateField(verbose_name='Start Date')),
                        ('end_date', models.DateField(verbose_name='End Date')),
                        ('home_operators',
                         django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=5),
                                                                   size=None)),
                        ('partner_operators',
                         django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=5),
                                                                   size=None)),
                        ('discounts', models.JSONField(default=list)),
                        ('do_not_calculate', models.BooleanField(default=False)),
                        ('include_satellite', models.BooleanField(verbose_name='Include satellite')),
                        ('include_premium', models.BooleanField(verbose_name='Include premium')),
                        ('include_premium_in_commitment',
                         models.BooleanField(verbose_name='Include premium in commitment')),
                        ('is_rolling', models.BooleanField(default=True, verbose_name='Is rolling')),
                        ('include_access_fee_in_sop_financial_inbound', models.BooleanField(blank=True, null=True,
                                                                                            verbose_name='Include Access Fee in Send or Pay Financial for Inbound')),
                        ('include_access_fee_in_sop_financial_outbound', models.BooleanField(blank=True, null=True,
                                                                                             verbose_name='Include Access Fee in Send or Pay Financial for Outbound')),
                        ('negotiator',
                         models.CharField(blank=True, max_length=50, null=True, verbose_name='Agreement Negotiator')),
                        ('terminated_at', models.DateField(blank=True, null=True, verbose_name='Terminated date')),
                        ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Creation date')),
                        ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last updated date')),
                        ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='Processed date')),
                        ('processing_status', models.PositiveSmallIntegerField(
                            choices=[(1, 'NOT_PROCESSED'), (2, 'NEW_CREATED'), (3, 'MOVED_TO_LIVE'), (4, 'SKIPPED'),
                                     (5, 'INTERSECTED'), (6, 'UPDATED'), (7, 'TERMINATED'), (10, 'FAILED')], default=1,
                            verbose_name='Processing Status')),
                        ('failed_message', models.TextField(blank=True, null=True, verbose_name='Failed Message')),
                    ],
                    options={
                        'db_table': 'external_agreements',
                    },
                ),
            ],
            database_operations=[],
        )

    ]
