# Generated by Django 4.2 on 2025-02-06 10:47

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.PositiveSmallIntegerField(choices=[(1, 'HISTORICAL_TRAFFIC_LOADED'), (2, 'CALCULATION_FINISHED')])),
                ('payload', models.JSONField(blank=True, null=True)),
                ('received_at', models.DateTimeField(auto_now_add=True, verbose_name='Received at')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='Processed at')),
            ],
            options={
                'db_table': 'iotron_notifications',
            },
        ),
    ]
