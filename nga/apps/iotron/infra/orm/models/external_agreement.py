from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils.translation import gettext_lazy as _

from nga.apps.iotron.enums import ExternalAgreementProcessingStatusEnum
from nga.core.enums import get_choices


class ExternalAgreement(models.Model):
    external_id = models.IntegerField(_("External ID"))
    name = models.CharField(_("Agreement Reference"), max_length=248)

    start_date = models.DateField(_("Start Date"))
    end_date = models.DateField(_("End Date"))

    home_operators = ArrayField(models.CharField(max_length=5))
    partner_operators = ArrayField(models.CharField(max_length=5))

    discounts = JSONField(default=list)

    do_not_calculate = models.BooleanField(default=False)

    include_satellite = models.BooleanField(_("Include satellite"))

    include_premium = models.BooleanField(_("Include premium"))

    include_premium_in_commitment = models.BooleanField(_("Include premium in commitment"))

    is_rolling = models.BooleanField(_("Is rolling"), default=True)

    include_access_fee_in_sop_financial_inbound = models.BooleanField(
        _("Include Access Fee in Send or Pay Financial for Inbound"),
        null=True,
        blank=True,
    )

    include_access_fee_in_sop_financial_outbound = models.BooleanField(
        _("Include Access Fee in Send or Pay Financial for Outbound"),
        null=True,
        blank=True,
    )

    negotiator = models.CharField(_("Agreement Negotiator"), max_length=50, null=True, blank=True)

    terminated_at = models.DateField(_("Terminated date"), null=True, blank=True)

    created_at = models.DateTimeField(_("Creation date"), auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(_("Last updated date"), auto_now=True)

    processed_at = models.DateTimeField(_("Processed date"), null=True, blank=True)
    processing_status = models.PositiveSmallIntegerField(
        _("Processing Status"),
        choices=get_choices(ExternalAgreementProcessingStatusEnum),
        default=ExternalAgreementProcessingStatusEnum.NOT_PROCESSED.value,
    )

    failed_message = models.TextField(_("Failed Message"), null=True, blank=True)

    class Meta:
        db_table = "external_agreements"

    def __str__(self) -> str:
        return f"#{self.id} - {self.name}"

    def __repr__(self) -> str:
        return (
            f"<{self.__class__.__name__}("
            f"id={self.id}, "
            f"name={self.name}, "
            f"start_date={self.start_date}, "
            f"end_date={self.end_date}"
            f")>"
        )
