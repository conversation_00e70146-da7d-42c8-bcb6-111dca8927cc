import uuid

from django.db import models

from nga.apps.iotron.enums import NotificationTypeEnum
from nga.core.enums import get_choices


class Notification(models.Model):
    """Model describes notification from the IOTRON system."""

    id = models.UUIDField(default=uuid.uuid4, primary_key=True, editable=False)

    type = models.PositiveSmallIntegerField(choices=get_choices(NotificationTypeEnum))

    payload = models.JSONField(null=True, blank=True)

    received_at = models.DateTimeField(verbose_name="Received at", auto_now_add=True)

    processed_at = models.DateTimeField(verbose_name="Processed at", null=True, blank=True)

    class Meta:
        db_table = "iotron_notifications"
