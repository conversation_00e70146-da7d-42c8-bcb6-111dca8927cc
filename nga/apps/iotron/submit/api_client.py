import json
import logging
from dataclasses import asdict, dataclass, field
from typing import Protocol

from nga.apps.iotron.submit.http_client import IOTRONHttpClient
from nga.apps.iotron.submit.models import IOTRONAgreement


@dataclass
class IOTRONAgreementSubmitResult:
    succeeded: bool

    agreement_id: int = field(default=-1)

    error: str = field(default_factory=str)


class IOTRONAgreementAPIClientProtocol(Protocol):
    def submit(self, iotron_agreement: IOTRONAgreement) -> IOTRONAgreementSubmitResult:
        """Performs API call to IOTRON to submit Agreement."""


class IOTRONAgreementAPIClient:
    SUBMIT_URL = "api/nga/agreements/v1/data/"

    def __init__(self, api_url: str, client_id: str, client_secret: str) -> None:
        self._api_url = api_url
        self._client_id = client_id
        self._client_secret = client_secret

    def submit(self, iotron_agreement: IOTRONAgreement) -> IOTRONAgreementSubmitResult:
        with IOTRONHttpClient(
            client_id=self._client_id,
            client_secret=self._client_secret,
            base_url=self._api_url,
        ) as client:

            client.authenticate()

            content = json.dumps(asdict(iotron_agreement), default=str)

            logging.info(f"submitted agreement content: {content}")

            response = client.post(
                self.SUBMIT_URL,
                content=content,
                headers={"content-type": "application/json"},
            )

        result = IOTRONAgreementSubmitResult(succeeded=False)

        logging.info(f"submit response: {response.status_code} | {response.content.decode()}")

        if response.is_success:
            result.succeeded = True
            result.agreement_id = response.json()["agreement_id"]
        else:
            result.succeeded = False
            result.error = response.text

        return result
