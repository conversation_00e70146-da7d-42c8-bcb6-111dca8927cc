from typing import Any, Mapping

from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountQualifyingBasisEnum,
)
from nga.apps.iotron.submit.consts import (
    IOTRONCallDestinationEnum,
    IOTRONDiscountBalancingEnum,
    IOTRONDiscountBasisEnum,
    IOTRONDiscountBoundTypeEnum,
    IOTRONDiscountCalculationTypeEnum,
    IOTRONDiscountDirectionEnum,
    IOTRONDiscountQualifyingBasisEnum,
    IOTRONEventTypeEnum,
    IOTRONServiceTypeEnum,
)
from nga.core.enums import CallDestinationEnum, ServiceTypeEnum


def rev_mapper(mapper: Mapping[Any, Any]) -> Mapping[Any, Any]:
    return {v: k for k, v in mapper.items()}


IOTRON_TO_NGA_SERVICE_TYPE_MAP = {
    f"{IOTRONServiceTypeEnum.VOICE}_{IOTRONEventTypeEnum.MO}": ServiceTypeEnum.VOICE_MO,
    f"{IOTRONServiceTypeEnum.VOICE}_{IOTRONEventTypeEnum.MT}": ServiceTypeEnum.VOICE_MT,
    f"{IOTRONServiceTypeEnum.SMS}_{IOTRONEventTypeEnum.MO}": ServiceTypeEnum.SMS_MO,
    f"{IOTRONServiceTypeEnum.SMS}_{IOTRONEventTypeEnum.MT}": ServiceTypeEnum.SMS_MT,
    f"{IOTRONServiceTypeEnum.DATA}_{IOTRONEventTypeEnum.MB}": ServiceTypeEnum.DATA,
    f"{IOTRONServiceTypeEnum.VOLTE}_{IOTRONEventTypeEnum.MB}": ServiceTypeEnum.VOLTE,
    f"{IOTRONServiceTypeEnum.ACCESS_FEE}_{IOTRONEventTypeEnum.IMSI}": ServiceTypeEnum.ACCESS_FEE,
}
IOTRON_FROM_NGA_SERVICE_TYPE_MAP = rev_mapper(IOTRON_TO_NGA_SERVICE_TYPE_MAP)

IOTRON_TO_NGA_DISCOUNT_DIRECTION_MAP: dict[str, int] = {
    IOTRONDiscountDirectionEnum.BIDIRECTIONAL: DiscountDirectionEnum.BIDIRECTIONAL,
    IOTRONDiscountDirectionEnum.OUTBOUND: DiscountDirectionEnum.OUTBOUND,
    IOTRONDiscountDirectionEnum.INBOUND: DiscountDirectionEnum.INBOUND,
}
IOTRON_FROM_NGA_DISCOUNT_DIRECTION_MAP = rev_mapper(IOTRON_TO_NGA_DISCOUNT_DIRECTION_MAP)

IOTRON_TO_NGA_DISCOUNT_CALL_DESTINATION_MAP: dict[str, int] = {
    IOTRONCallDestinationEnum.HOME: CallDestinationEnum.HOME,
    IOTRONCallDestinationEnum.LOCAL: CallDestinationEnum.LOCAL,
    IOTRONCallDestinationEnum.INTERNATIONAL: CallDestinationEnum.INTERNATIONAL,
}
IOTRON_FROM_NGA_DISCOUNT_CALL_DESTINATION_MAP = rev_mapper(IOTRON_TO_NGA_DISCOUNT_CALL_DESTINATION_MAP)

IOTRON_TO_NGA_DISCOUNT_PARAMETER_BASIS_MAP: dict[str, int] = {
    IOTRONDiscountBasisEnum.VALUE: DiscountBasisEnum.VALUE,
    IOTRONDiscountBasisEnum.PERCENTAGE: DiscountBasisEnum.PERCENTAGE,
    IOTRONDiscountBasisEnum.NET_TRAFFIC_SENDER_TAP_RATE: DiscountBasisEnum.NET_TRAFFIC_SENDER_TAP_RATE,
    IOTRONDiscountBasisEnum.NET_TRAFFIC_RECEIVER_TAP_RATE: DiscountBasisEnum.NET_TRAFFIC_RECEIVER_TAP_RATE,
    IOTRONDiscountBasisEnum.DEDUCTION: DiscountBasisEnum.DEDUCTION,
    IOTRONDiscountBasisEnum.AVERAGE_IOT_RATE: DiscountBasisEnum.AVERAGE_IOT_RATE,
}
IOTRON_FROM_NGA_DISCOUNT_PARAMETER_BASIS_MAP = rev_mapper(IOTRON_TO_NGA_DISCOUNT_PARAMETER_BASIS_MAP)

IOTRON_TO_NGA_DISCOUNT_PARAMETER_BALANCING_MAP: dict[str, int] = {
    IOTRONDiscountBalancingEnum.BALANCED: DiscountBalancingEnum.BALANCED,
    IOTRONDiscountBalancingEnum.NO_BALANCING: DiscountBalancingEnum.NO_BALANCING,
    IOTRONDiscountBalancingEnum.UNBALANCED: DiscountBalancingEnum.UNBALANCED,
    IOTRONDiscountBalancingEnum.BALANCED_WITH_GROUP_COMPENSATION: DiscountBalancingEnum.BALANCED_WITH_GROUP_COMPENSATION,  # noqa
    IOTRONDiscountBalancingEnum.BALANCING_FOR_SOP_TRAFFIC: DiscountBalancingEnum.BALANCING_FOR_SOP_TRAFFIC,
    IOTRONDiscountBalancingEnum.INCREMENTAL_TRAFFIC: DiscountBalancingEnum.INCREMENTAL_TRAFFIC,
    IOTRONDiscountBalancingEnum.UNBALANCED_WITH_GROUP_COMPENSATION: DiscountBalancingEnum.UNBALANCED_WITH_GROUP_COMPENSATION,  # noqa
}
IOTRON_FROM_NGA_DISCOUNT_PARAMETER_BALANCING_MAP = {
    **rev_mapper(IOTRON_TO_NGA_DISCOUNT_PARAMETER_BALANCING_MAP),
    DiscountBalancingEnum.NO_BALANCING: IOTRONDiscountBalancingEnum.NO_BALANCING_SPECIFIED,
    DiscountBalancingEnum.UNBALANCED: IOTRONDiscountBalancingEnum.DASHED_UNBALANCED,
    None: IOTRONDiscountBalancingEnum.NO_BALANCING_SPECIFIED,
}

IOTRON_TO_NGA_DISCOUNT_PARAMETER_CALCULATION_TYPE_MAP: dict[str, int] = {
    IOTRONDiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE: DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
    IOTRONDiscountCalculationTypeEnum.ALL_YOU_CAN_EAT: DiscountCalculationTypeEnum.ALL_YOU_CAN_EAT,
    IOTRONDiscountCalculationTypeEnum.BACK_TO_FIRST: DiscountCalculationTypeEnum.BACK_TO_FIRST,
    IOTRONDiscountCalculationTypeEnum.CONTRIBUTION_TO_GROUP_SHORTFALL: DiscountCalculationTypeEnum.CONTRIBUTION_TO_GROUP_SHORTFALL,  # noqa
    IOTRONDiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL: DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
    IOTRONDiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL_ACCESS_FEE: DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,  # noqa
    IOTRONDiscountCalculationTypeEnum.SEND_OR_PAY_TRAFFIC: DiscountCalculationTypeEnum.SEND_OR_PAY_TRAFFIC,
    IOTRONDiscountCalculationTypeEnum.STEPPED_TIERED: DiscountCalculationTypeEnum.STEPPED_TIERED,
    IOTRONDiscountCalculationTypeEnum.ALL_YOU_CAN_EAT_MONTHLY: DiscountCalculationTypeEnum.ALL_YOU_CAN_EAT_MONTHLY,
    IOTRONDiscountCalculationTypeEnum.STEPPED_TIERED_MONTHLY: DiscountCalculationTypeEnum.STEPPED_TIERED_MONTHLY,
    IOTRONDiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_ABOVE_THRESHOLD: DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_ABOVE_THRESHOLD,  # noqa
    IOTRONDiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED: DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED,  # noqa
    IOTRONDiscountCalculationTypeEnum.PER_MONTH_PER_IMSI: DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI,
    IOTRONDiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_BACK_TO_FIRST: DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_BACK_TO_FIRST,  # noqa
    IOTRONDiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING: DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING,  # noqa
    IOTRONDiscountCalculationTypeEnum.ALL_YOU_CAN_EAT_DAILY: DiscountCalculationTypeEnum.ALL_YOU_CAN_EAT_DAILY,
    IOTRONDiscountCalculationTypeEnum.PER_DAY_PER_IMSI_ALL_YOU_CAN_EAT: DiscountCalculationTypeEnum.PER_DAY_PER_IMSI_ALL_YOU_CAN_EAT,  # noqa
    IOTRONDiscountCalculationTypeEnum.PER_DAY_PER_IMSI_CAPPED_CHARGE: DiscountCalculationTypeEnum.PER_DAY_PER_IMSI_CAPPED_CHARGE,  # noqa
    IOTRONDiscountCalculationTypeEnum.PER_DAY_PER_IMSI_CAPPED_CHARGE_HIGHEST: DiscountCalculationTypeEnum.PER_DAY_PER_IMSI_CAPPED_CHARGE_HIGHEST,  # noqa
}
IOTRON_FROM_NGA_DISCOUNT_PARAMETER_CALCULATION_TYPE_MAP = {
    **rev_mapper(IOTRON_TO_NGA_DISCOUNT_PARAMETER_CALCULATION_TYPE_MAP),
    **{DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL: IOTRONDiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL},
}

IOTRON_TO_NGA_DISCOUNT_PARAMETER_BOUND_TYPE: dict[str, int] = {
    IOTRONDiscountBoundTypeEnum.FINANCIAL_COMMITMENT: DiscountBoundTypeEnum.FINANCIAL_COMMITMENT,
    IOTRONDiscountBoundTypeEnum.VOLUME: DiscountBoundTypeEnum.VOLUME,
    IOTRONDiscountBoundTypeEnum.MARKET_SHARE: DiscountBoundTypeEnum.MARKET_SHARE,
    IOTRONDiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH: DiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH,
    IOTRONDiscountBoundTypeEnum.VOLUME_INCLUDED_IN_ACCESS_FEE: DiscountBoundTypeEnum.VOLUME_INCLUDED_IN_ACCESS_FEE,
}
IOTRON_FROM_NGA_DISCOUNT_PARAMETER_BOUND_TYPE = rev_mapper(IOTRON_TO_NGA_DISCOUNT_PARAMETER_BOUND_TYPE)

IOTRON_TO_NGA_DISCOUNT_PARAMETER_QUALIFYING_BASIS: dict[str, int] = {
    IOTRONDiscountQualifyingBasisEnum.VOLUME: DiscountQualifyingBasisEnum.VOLUME,
    IOTRONDiscountQualifyingBasisEnum.MARKET_SHARE_PERCENTAGE: DiscountQualifyingBasisEnum.MARKET_SHARE_PERCENTAGE,
    IOTRONDiscountQualifyingBasisEnum.UNIQUE_IMSI_COUNT_PER_MONTH: DiscountQualifyingBasisEnum.UNIQUE_IMSI_COUNT_PER_MONTH,  # noqa
    IOTRONDiscountQualifyingBasisEnum.AVERAGE_MONTHLY_USAGE_PER_IMSI: DiscountQualifyingBasisEnum.AVERAGE_MONTHLY_USAGE_PER_IMSI,  # noqa
}
IOTRON_FROM_NGA_DISCOUNT_PARAMETER_QUALIFYING_BASIS = rev_mapper(IOTRON_TO_NGA_DISCOUNT_PARAMETER_QUALIFYING_BASIS)
