from decimal import Decimal
from typing import Optional, Sequence

from nga.apps.agreements.domain.models import BudgetAgreement, Discount, DiscountParameter
from nga.apps.agreements.enums import DiscountModelTypeEnum
from nga.apps.iotron.submit.consts import (
    IOTRON_ALL_PMNS_VALUE,
    IOTRON_ALL_VALUE,
    IOTRONEventTypeEnum,
    IOTRONServiceTypeEnum,
)
from nga.apps.iotron.submit.mappings import (
    IOTRON_FROM_NGA_DISCOUNT_CALL_DESTINATION_MAP,
    IOTRON_FROM_NGA_DISCOUNT_DIRECTION_MAP,
    IOTRON_FROM_NGA_DISCOUNT_PARAMETER_BALANCING_MAP,
    IOTRON_FROM_NGA_DISCOUNT_PARAMETER_BASIS_MAP,
    IOTRON_FROM_NGA_DISCOUNT_PARAMETER_BOUND_TYPE,
    IOTRON_FROM_NGA_DISCOUNT_PARAMETER_CALCULATION_TYPE_MAP,
    IOTRON_FROM_NGA_DISCOUNT_PARAMETER_QUALIFYING_BASIS,
    IOTRON_FROM_NGA_SERVICE_TYPE_MAP,
)
from nga.apps.iotron.submit.models import (
    IOTRONCallDestinations,
    IOTRONDiscountAccessFee,
    IOTRONDiscountAirtimeToll,
    IOTRONDiscountBound,
    IOTRONDiscountFairUsage,
    IOTRONDiscountIntermediateDates,
    IOTRONDiscountParameter,
    IOTRONDiscountQualifyingService,
)
from nga.apps.references.providers import (
    AbstractCountryProvider,
    AbstractOperatorProvider,
    AbstractTrafficSegmentProvider,
)
from nga.core.enums import ServiceTypeEnum


class IOTRONDiscountMapper:
    def __init__(
        self,
        operator_provider: AbstractOperatorProvider,
        traffic_segment_provider: AbstractTrafficSegmentProvider,
        country_provider: AbstractCountryProvider,
    ) -> None:
        self._operator_provider = operator_provider

        self._traffic_segment_provider = traffic_segment_provider

        self._country_provider = country_provider

    def map(self, budget_agreement: BudgetAgreement, discount: Discount) -> list[IOTRONDiscountParameter]:
        home_operators = self._map_pmns(budget_agreement.home_operators, discount.home_operators)

        partner_operators = self._map_pmns(budget_agreement.partner_operators, discount.partner_operators)

        traffic_segments = self._map_traffic_segment_names(discount.traffic_segments)

        call_destinations = self._map_call_destinations(discount)

        initial_service_types = list(discount.service_types)

        if discount.model_type == DiscountModelTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING:
            initial_service_types.remove(ServiceTypeEnum.ACCESS_FEE)

        service, event_type = self._split_service_type(initial_service_types)

        qualifying_service_record = None

        if discount.qualifying_rule is not None:
            qualifying_service, qualifying_event_type = self._split_service_type(discount.qualifying_rule.service_types)

            qualifying_service_record = self._map_qualifying_service(
                discount=discount,
                service=qualifying_service,
                event_type=qualifying_event_type,
            )

        iotron_parameters = []

        for parameter in discount.parameters:

            iotron_parameter = IOTRONDiscountParameter(
                discount_id=discount.id,
                home_operators=home_operators,
                partner_operators=partner_operators,
                direction=IOTRON_FROM_NGA_DISCOUNT_DIRECTION_MAP[discount.direction],
                service_type=service,
                event_type=event_type,
                currency_code=discount.currency_code,
                tax_type=discount.tax_type.name,
                volume_type=discount.volume_type.name,
                calculation_type=IOTRON_FROM_NGA_DISCOUNT_PARAMETER_CALCULATION_TYPE_MAP[parameter.calculation_type],
                basis=IOTRON_FROM_NGA_DISCOUNT_PARAMETER_BASIS_MAP.get(parameter.basis),
                basis_value=optional_decimal_to_float(parameter.basis_value),
                bound_type=self._map_bound(parameter),
                balancing_type=IOTRON_FROM_NGA_DISCOUNT_PARAMETER_BALANCING_MAP[parameter.balancing],
                call_destinations=call_destinations,
                fair_usage=self._map_fair_usage(parameter),
                qualifying_service=qualifying_service_record,
                airtime_toll=self._map_airtime_toll(parameter),
                access_fee=self._map_access_fee(parameter),
                intermediate_dates=IOTRONDiscountIntermediateDates(
                    start_date=discount.period.start_date,
                    end_date=discount.period.end_date,
                ),
                traffic_segments=traffic_segments,
            )

            iotron_parameters.append(iotron_parameter)

        return iotron_parameters

    def _map_pmns(self, agreement_operators: Sequence[int], discount_operators: Sequence[int]) -> list[str]:
        if sorted(agreement_operators) == sorted(discount_operators):
            pmns = IOTRON_ALL_PMNS_VALUE
        else:
            operators = self._operator_provider.get_many(operators_ids=discount_operators)

            pmns = [o.pmn_code for o in operators]

        return pmns

    def _map_traffic_segment_names(self, traffic_segments: Optional[tuple[int, ...]]) -> Optional[list[str]]:
        if traffic_segments is None:
            return None

        segments = self._traffic_segment_provider.get_many(segments_ids=traffic_segments)

        names = [s.name for s in segments]

        return names

    def _map_call_destinations(self, discount: Discount) -> Optional[IOTRONCallDestinations]:
        if discount.call_destinations is None and discount.called_countries is None:
            return None

        iotron_call_destinations = IOTRONCallDestinations(call_destinations=None, called_countries=None)

        if discount.call_destinations:
            mapped_call_destinations = []

            for cd in discount.call_destinations:
                mapped_cd = IOTRON_FROM_NGA_DISCOUNT_CALL_DESTINATION_MAP.get(cd)

                if mapped_cd is not None:
                    mapped_call_destinations.append(mapped_cd)

            iotron_call_destinations.call_destinations = mapped_call_destinations

        if discount.called_countries:
            countries = self._country_provider.get_many()

            iotron_call_destinations.called_countries = [c.code for c in countries if c.id in discount.called_countries]

        return iotron_call_destinations

    def _map_qualifying_service(
        self,
        discount: Discount,
        service: str,
        event_type: str,
    ) -> Optional[IOTRONDiscountQualifyingService]:

        if discount.qualifying_rule is None:
            return None

        qualifying_service = IOTRONDiscountQualifyingService(
            direction=IOTRON_FROM_NGA_DISCOUNT_DIRECTION_MAP.get(discount.qualifying_rule.direction),
            service_type=service,
            event_type=event_type,
            basis=IOTRON_FROM_NGA_DISCOUNT_PARAMETER_QUALIFYING_BASIS.get(discount.qualifying_rule.basis),
            basis_value=optional_decimal_to_float(discount.qualifying_rule.lower_bound),
        )

        return qualifying_service

    @classmethod
    def _map_bound(cls, parameter: DiscountParameter) -> Optional[IOTRONDiscountBound]:
        if parameter.bound_type is None and parameter.lower_bound is None and parameter.upper_bound is None:
            return None

        bound = IOTRONDiscountBound(
            type=IOTRON_FROM_NGA_DISCOUNT_PARAMETER_BOUND_TYPE.get(parameter.bound_type),
            lower=optional_decimal_to_float(parameter.lower_bound),
            upper=optional_decimal_to_float(parameter.upper_bound),
        )

        return bound

    @classmethod
    def _map_fair_usage(cls, parameter: DiscountParameter) -> Optional[IOTRONDiscountFairUsage]:
        if parameter.fair_usage_rate is None and parameter.fair_usage_threshold is None:
            return None

        fair_usage = IOTRONDiscountFairUsage(
            rate=optional_decimal_to_float(parameter.fair_usage_rate),
            threshold=optional_decimal_to_float(parameter.fair_usage_threshold),
        )

        return fair_usage

    @classmethod
    def _map_airtime_toll(cls, parameter: DiscountParameter) -> Optional[IOTRONDiscountAirtimeToll]:
        if parameter.airtime_rate is None and parameter.toll_rate is None:
            return None

        airtime_toll = IOTRONDiscountAirtimeToll(
            rate=optional_decimal_to_float(parameter.toll_rate),
            airtime_rate=optional_decimal_to_float(parameter.airtime_rate),
        )

        return airtime_toll

    @classmethod
    def _map_access_fee(cls, parameter: DiscountParameter) -> Optional[IOTRONDiscountAccessFee]:
        if parameter.access_fee_rate is None and parameter.incremental_rate is None:
            return None

        access_fee = IOTRONDiscountAccessFee(
            access_fee_rate=optional_decimal_to_float(parameter.access_fee_rate),
            incremental_rate=optional_decimal_to_float(parameter.incremental_rate),
        )

        return access_fee

    @classmethod
    def _split_service_type(cls, service_types: Sequence[ServiceTypeEnum]) -> tuple[str, str]:
        total_service_types = len(service_types)

        sms_services = ServiceTypeEnum.sms_services()
        voice_services = ServiceTypeEnum.voice_services()

        if total_service_types == 1:
            service_event_type = IOTRON_FROM_NGA_SERVICE_TYPE_MAP[service_types[0]]

            service, event_type = service_event_type.split("_")

        elif all(sms in service_types for sms in sms_services) and total_service_types == len(sms_services):
            service, event_type = IOTRONServiceTypeEnum.SMS, IOTRONEventTypeEnum.MO_MT

        elif all(voice in service_types for voice in voice_services) and total_service_types == len(voice_services):
            service, event_type = IOTRONServiceTypeEnum.VOICE, IOTRONEventTypeEnum.MO_MT

        else:
            service, event_type = IOTRON_ALL_VALUE, IOTRON_ALL_VALUE

        return service, event_type


def optional_decimal_to_float(value: Optional[Decimal]) -> Optional[float]:
    return float(value) if value is not None else None
