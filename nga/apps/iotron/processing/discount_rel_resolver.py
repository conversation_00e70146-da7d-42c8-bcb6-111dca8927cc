"""
This module contains logic to establish valid relation with discount and sub-discount, and should be located alongside
with external agreement domain logic.
"""

from typing import Literal

from nga.apps.agreements.domain.discount_model_properties import DiscountModelProperties
from nga.apps.agreements.domain.mappers import from_discount_to_dto
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.enums import DiscountDirectionEnum


class DiscountRelationResolver:
    """
    This class is responsible to establish relationship between discount and sub-discount within single agreement.
    Flow:
        1. We need to define which discounts can have sub-discounts
        2. Set parent_id for potential sub-discounts that match by traffic parameters with parent discount
        3. Split discounts by direction to cover specific IOTRON setup case
    """

    def __init__(self, discount_repository: AbstractDiscountRepository) -> None:
        self._discount_repository = discount_repository

    def resolve_sub_discount_rel(self, agreement_id: int) -> None:
        """Establishes parent-child relationship by discount traffic parameters."""

        discounts = self._discount_repository.get_many(agreement_id)

        if not len(discounts):
            return None

        parent_discounts, potential_sub_discounts = [], []

        for discount in discounts:
            discount_model_properties = DiscountModelProperties(discount)

            if discount_model_properties.supports_sub_discounts:
                parent_discounts.append(discount)
            else:
                potential_sub_discounts.append(discount)

        self._set_parent_id_for_sub_discounts(agreement_id, parent_discounts, potential_sub_discounts)

    def _set_parent_id_for_sub_discounts(
        self,
        agreement_id: int,
        potential_parent_discounts: list[Discount],
        potential_sub_discounts: list[Discount],
    ) -> None:
        """Establishes parent-child relationship for sub-discount by setting parent_id value for discount."""

        for parent_discount in potential_parent_discounts:
            if parent_discount.is_bidirectional:
                directions = [direction for direction in DiscountDirectionEnum]
            else:
                # we add bidirectional to cover IOTRON specific case, when single direction discount can have
                # bidirectional sub-discounts
                directions = [parent_discount.direction, DiscountDirectionEnum.BIDIRECTIONAL]

            sub_discounts = self._filter_sub_discounts(parent_discount, potential_sub_discounts, directions=directions)

            for sub_discount in sub_discounts:
                bidirectional_in_single_direction = (
                    sub_discount.is_bidirectional and not parent_discount.is_bidirectional
                )

                if bidirectional_in_single_direction:
                    in_sub_discount, out_sub_discount = self._split_discount_by_direction(sub_discount, agreement_id)

                    if in_sub_discount.direction == parent_discount.direction:
                        sub_discount = in_sub_discount
                        potential_sub_discounts.append(out_sub_discount)
                    else:
                        sub_discount = out_sub_discount
                        potential_sub_discounts.append(in_sub_discount)

                direction_matched = (
                    parent_discount.direction == sub_discount.direction or parent_discount.is_bidirectional
                )

                if not (
                    set(sub_discount.home_operators).issubset(parent_discount.home_operators)
                    and set(sub_discount.partner_operators).issubset(parent_discount.partner_operators)
                ):
                    sub_discount = self._split_discount_by_operators(parent_discount, sub_discount)

                if direction_matched:
                    sub_discount.parent_id = parent_discount.id

                    self._discount_repository.save(sub_discount)

    @classmethod
    def _filter_sub_discounts(
        cls,
        discount: Discount,
        potential_sub_discounts: list[Discount],
        *,
        directions: list[DiscountDirectionEnum],
    ) -> list[Discount]:

        matched_sub_discounts = []

        hpmns = set(discount.home_operators)
        ppmns = set(discount.partner_operators)
        service_types = set(discount.service_types)

        for sb in potential_sub_discounts:
            service_types_matched = set(sb.service_types).issubset(service_types)
            hpmns_matched = bool(set(sb.home_operators).intersection(hpmns))
            ppmns_matched = bool(set(sb.partner_operators).intersection(ppmns))

            if (
                sb.agreement_id == discount.agreement_id
                and hpmns_matched
                and ppmns_matched
                and service_types_matched
                and sb.direction in directions
                and sb.period in discount.period
            ):
                matched_sub_discounts.append(sb)

        return matched_sub_discounts

    def _split_discount_by_direction(self, in_discount: Discount, agreement_id: int) -> tuple[Discount, Discount]:
        """
        This method contains logic to cover specific IOTRON case. In IOTRON single direction discounts (e.x INBOUND)
        can contain BIDIRECTIONAL sub-discounts. NGA system does not allow this structure, because sub-discount will
        exceed parent discount by traffic parameters. In that case we need to split bidirectional sub-discounts
        by direction (separately INBOUND OUTBOUND) and make extra split discounts (that exceed parent) as parent
        discounts on the agreement lvl.

        Case:
            * BIDIRECTIONAL sub-discount

        Expected output:
            * INBOUND  sub-discount
            * OUTBOUND sub-discount
        """

        in_discount.direction = DiscountDirectionEnum.INBOUND
        self._discount_repository.save(in_discount)

        out_discount_dto = from_discount_to_dto(in_discount)
        out_discount_dto.direction = DiscountDirectionEnum.OUTBOUND
        out_discount = self._discount_repository.create(agreement_id, out_discount_dto)

        return in_discount, out_discount

    def _split_discount_by_operators(
        self,
        parent_discount: Discount,
        sub_discount: Discount,
    ) -> Discount:
        """
        This method contains logic to split matched discount into multiple discounts,
        to set up one of these as a sub discount.

        Case:
                                 Home Operators     Partner Operators
            * parent_discount | HOME1, HOME3     |  TEST1, TEST2
              sub_discount    | HOME1, HOME2     |  TEST1, TEST2, TEST3

        Expected discounts in the database:
                                Home Operators     Partner Operators
            * parent_discount | HOME1, HOME3     |  TEST1, TEST2
              sub_discount    | HOME1            |  TEST1, TEST2
              split_discount1 | HOME2            |  TEST1, TEST2
              split_discount2 | HOME1, HOME2     |  TEST3

        """
        hpmn_diff = set(sub_discount.home_operators).difference(parent_discount.home_operators)
        ppmn_diff = set(sub_discount.partner_operators).difference(parent_discount.partner_operators)

        if hpmn_diff:
            home_operators = tuple(set(parent_discount.home_operators).intersection(sub_discount.home_operators))
            partner_operators = parent_discount.partner_operators

            self._create_split_discount(parent_discount, sub_discount, diff_operators="home")

            if ppmn_diff:
                partner_operators = tuple(
                    set(parent_discount.partner_operators).intersection(sub_discount.partner_operators)
                )

                self._create_split_discount(parent_discount, sub_discount, diff_operators="partner")

        else:
            home_operators = parent_discount.home_operators
            partner_operators = tuple(
                set(parent_discount.partner_operators).intersection(sub_discount.partner_operators)
            )

            self._create_split_discount(parent_discount, sub_discount, diff_operators="partner")

        sub_discount.home_operators = tuple(home_operators)
        sub_discount.partner_operators = tuple(partner_operators)
        self._discount_repository.save(sub_discount)

        return sub_discount

    def _create_split_discount(
        self,
        parent_discount: Discount,
        child_discount: Discount,
        *,
        diff_operators: Literal["home", "partner"],
    ) -> None:
        discount_dto = from_discount_to_dto(child_discount)
        if diff_operators == "home":
            discount_dto.home_operators = tuple(
                set(child_discount.home_operators).difference(parent_discount.home_operators)
            )
            discount_dto.partner_operators = parent_discount.partner_operators

        else:
            discount_dto.partner_operators = tuple(
                set(child_discount.partner_operators).difference(parent_discount.partner_operators)
            )

        self._discount_repository.create(parent_discount.agreement_id, discount_dto)
