from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository


class DiscountQualifyingUpperBoundFiller:
    def __init__(self, discount_repository: AbstractDiscountRepository) -> None:
        self._discount_repository = discount_repository

        self.processed_discount_ids: set[int] = set()

    def fill_qualifying_upper_bound(self, discounts: list[Discount]) -> None:
        discounts_with_qualifying_rule = [d for d in discounts if d.qualifying_rule is not None]

        if not discounts_with_qualifying_rule:
            return None

        for discount in discounts_with_qualifying_rule:

            if discount.id in self.processed_discount_ids:
                continue

            equal_discounts = [
                d
                for d in discounts_with_qualifying_rule
                if sorted(d.home_operators) == sorted(discount.home_operators)
                and sorted(d.partner_operators) == sorted(discount.partner_operators)
                and d.direction == discount.direction
                and sorted(d.service_types) == sorted(discount.service_types)
                and d.period == discount.period
                and d.call_destinations == discount.call_destinations
                and d.called_countries == discount.called_countries
                and d.traffic_segments == discount.traffic_segments
                and d.imsi_count_type == discount.imsi_count_type
                and d.qualifying_rule.direction == discount.qualifying_rule.direction  # type: ignore[union-attr]
                and (
                    sorted(d.qualifying_rule.service_types)  # type: ignore[union-attr]
                    == sorted(discount.qualifying_rule.service_types)  # type: ignore[union-attr]
                )
                and d.qualifying_rule.basis == discount.qualifying_rule.basis  # type: ignore[union-attr]
            ]

            if not equal_discounts or len(equal_discounts) == 1:
                continue

            self._set_qualifying_upper_bound_for_equal_discounts(equal_discounts)

    def _set_qualifying_upper_bound_for_equal_discounts(
        self,
        equal_discounts: list[Discount],
    ) -> None:
        equal_discounts = sorted(
            equal_discounts, key=lambda d: d.qualifying_rule.lower_bound  # type: ignore[union-attr]
        )

        previous_discount = equal_discounts[0]
        self.processed_discount_ids.add(previous_discount.id)

        for i, equal_discount in enumerate(equal_discounts[1:]):

            previous_upper_bound = equal_discount.qualifying_rule.lower_bound  # type: ignore[union-attr]

            previous_discount.qualifying_rule.upper_bound = previous_upper_bound  # type: ignore[union-attr]

            self._discount_repository.save(previous_discount)

            previous_discount = equal_discount
