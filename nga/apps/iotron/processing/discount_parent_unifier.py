from nga.apps.agreements.domain.discount_model_properties import DiscountModelProperties
from nga.apps.agreements.domain.mappers import from_discount_parameter_to_dto
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.models.discount import CommitmentDistributionParameter
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.utils.collections import equal


class DiscountParentUnifier:
    """
    This class is responsible for merging parent discounts into one with creating or updating
    Commitment Distribution Parameters.
    Flow:
    1. Find all parent discounts.
    2. Identify similar discounts and merge them:
        2.1. Update/Create the Commitment Distribution Parameters with data from existing and new discounts.
        2.2. Set the Lower Bound as the sum of Commitment Distribution charges.
    """

    def __init__(self, discount_repository: AbstractDiscountRepository) -> None:
        self._discount_repository = discount_repository

    def unify_parent_discounts(self, agreement_id: int) -> None:
        discounts = self._discount_repository.get_many(agreement_id)

        if not len(discounts):
            return None

        parent_discounts = []

        for discount in discounts:
            discount_model_properties = DiscountModelProperties(discount)

            if discount_model_properties.supports_sub_discounts:
                parent_discounts.append(discount)

        self._filter_and_combine_suitable_parent_discounts(parent_discounts)

    def _filter_and_combine_suitable_parent_discounts(
        self,
        parent_discounts: list[Discount],
    ) -> None:
        ids_for_remove = []

        for discount in parent_discounts:
            for pd in parent_discounts:
                if pd.id in ids_for_remove or discount.id in ids_for_remove:
                    continue

                if (
                    pd.period == discount.period
                    and pd.direction == discount.direction
                    and sorted(pd.service_types) == sorted(discount.service_types)
                    and equal(discount.call_destinations, pd.call_destinations)
                    and equal(discount.called_countries, pd.called_countries)
                    and equal(discount.traffic_segments, pd.traffic_segments)
                    and pd.id != discount.id
                ):
                    new_cd_parameter = CommitmentDistributionParameter(
                        home_operators=pd.home_operators,
                        partner_operators=pd.partner_operators,
                        charge=pd.parameters[0].lower_bound,  # type: ignore[arg-type]
                    )

                    if discount.commitment_distribution_parameters is None:
                        discount.commitment_distribution_parameters = (
                            CommitmentDistributionParameter(
                                home_operators=discount.home_operators,
                                partner_operators=discount.partner_operators,
                                charge=discount.parameters[0].lower_bound,  # type: ignore[arg-type]
                            ),
                            new_cd_parameter,
                        )
                    else:
                        discount.commitment_distribution_parameters = (
                            *discount.commitment_distribution_parameters,
                            new_cd_parameter,
                        )

                    discount.home_operators = tuple(set(discount.home_operators).union(set(pd.home_operators)))
                    discount.partner_operators = tuple(set(discount.partner_operators).union(set(pd.partner_operators)))

                    discount.parameters[0].lower_bound += pd.parameters[0].lower_bound  # type: ignore[operator]
                    discount_parameter_dto = from_discount_parameter_to_dto(discount.parameters[0])

                    ids_for_remove.append(pd.id)

                    self._discount_repository.save(discount)
                    self._discount_repository.set_parameters(discount.id, parameters_dtos=(discount_parameter_dto,))

        for _id in ids_for_remove:
            self._discount_repository.delete_by_id(_id)
