from typing import Optional, TypedDict

__all__ = ["DiscountDict", "DiscountImportDict", "DiscountParameterDict"]


class DiscountParameterDict(TypedDict):
    calculation_type: str

    basis: Optional[str]
    basis_value: Optional[str]

    balancing: Optional[str]

    bound_type: Optional[str]
    lower_bound: Optional[str]
    upper_bound: Optional[str]

    toll_rate: Optional[str]
    airtime_rate: Optional[str]

    fair_usage_rate: Optional[str]
    fair_usage_threshold: Optional[str]

    access_fee_rate: Optional[str]
    incremental_rate: Optional[str]


class BaseDiscountDict(TypedDict):

    direction: str
    service_types: list[str]

    start_date: str
    end_date: str

    currency_code: str

    tax_type: str
    volume_type: str

    settlement_method: str

    call_destinations: Optional[list[str]]

    qualifying_direction: Optional[str]
    qualifying_service_types: Optional[list[str]]
    qualifying_basis: Optional[str]
    qualifying_lower_bound: Optional[str]

    imsi_count_type: Optional[str]

    discount_parameters: list[DiscountParameterDict]


class DiscountDict(BaseDiscountDict):
    home_operators: list[int]
    partner_operators: list[int]

    called_countries: Optional[list[int]]
    traffic_segments: Optional[list[int]]


class DiscountImportDict(BaseDiscountDict):
    home_operators: list[str]
    partner_operators: list[str]

    called_countries: Optional[list[str]]
    traffic_segments: Optional[list[str]]
