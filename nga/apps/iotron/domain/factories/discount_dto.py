from datetime import datetime

from nga.apps.agreements.consts import FULL_DATE_FORMAT
from nga.apps.agreements.domain.dto import DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.domain.value_objects import DiscountQualifyingRule
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.iotron.domain.dto import DiscountDict
from nga.apps.iotron.domain.utils import to_decimal
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import Month


class DiscountDTOFactory:
    @classmethod
    def create_from_external_discount(
        cls,
        discount: DiscountDict,
        include_access_fee_in_sop_financial_inbound: bool,
        include_access_fee_in_sop_financial_outbound: bool,
    ) -> DiscountDTO:
        discount_dto = cls._from_discount_dict_to_discount_dto(discount)

        discount_dto = cls._adjust_if_model_is_pmpi_with_incremental_charging(discount_dto)

        discount_dto = cls._adjust_if_model_is_pmpi_stepped_tiered(discount_dto)

        discount_dto = cls._adjust_if_model_sop_financial(
            discount_dto,
            include_access_fee_in_sop_financial_inbound,
            include_access_fee_in_sop_financial_outbound,
        )

        return discount_dto

    @classmethod
    def _adjust_if_model_is_pmpi_with_incremental_charging(cls, discount_dto: DiscountDTO) -> DiscountDTO:
        """
        If discount parameters contain parameter that fits PMPI with incremental charging discount model, then we need
        to manually set imsi count type to DATA and append ACCESS_FEE service type.
        """

        if len(discount_dto.parameters) != 1:
            return discount_dto

        p = discount_dto.parameters[0]

        if p.calculation_type != DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING:
            return discount_dto

        if ServiceTypeEnum.ACCESS_FEE not in discount_dto.service_types:
            discount_dto.service_types = tuple((*discount_dto.service_types, ServiceTypeEnum.ACCESS_FEE))

        if discount_dto.imsi_count_type is None:
            discount_dto.imsi_count_type = IMSICountTypeEnum.DATA

        return discount_dto

    @classmethod
    def _adjust_if_model_is_pmpi_stepped_tiered(cls, discount_dto: DiscountDTO) -> DiscountDTO:
        """
        If discount parameters contain parameter that fit PMPI Stepped Tiered model, then we need to set manually
        imsi count type to DATA.
        """

        calculation_types_are_valid = any(
            p.calculation_type == DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED
            for p in discount_dto.parameters
        )

        if not calculation_types_are_valid:
            return discount_dto

        if discount_dto.imsi_count_type is None:
            discount_dto.imsi_count_type = IMSICountTypeEnum.DATA

        return discount_dto

    @classmethod
    def _adjust_if_model_sop_financial(
        cls,
        discount_dto: DiscountDTO,
        include_access_fee_in_sop_financial_inbound: bool,
        include_access_fee_in_sop_financial_outbound: bool,
    ) -> DiscountDTO:
        """
        If discount parameters contain parameter that fit Send or Pay Financial model, then we need to include/exclude
        manually Access Fee service type.
        """

        if len(discount_dto.parameters) != 1:
            return discount_dto

        p = discount_dto.parameters[0]

        if p.calculation_type != DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL:
            return discount_dto

        if discount_dto.direction == DiscountDirectionEnum.INBOUND:
            should_adjust_access_fee = include_access_fee_in_sop_financial_inbound

        elif discount_dto.direction == DiscountDirectionEnum.OUTBOUND:
            should_adjust_access_fee = include_access_fee_in_sop_financial_outbound

        else:
            should_adjust_access_fee = (
                include_access_fee_in_sop_financial_inbound or include_access_fee_in_sop_financial_outbound
            )

        if should_adjust_access_fee and ServiceTypeEnum.ACCESS_FEE not in discount_dto.service_types:
            discount_dto.service_types = tuple((*discount_dto.service_types, ServiceTypeEnum.ACCESS_FEE))

        elif (
            not should_adjust_access_fee
            and ServiceTypeEnum.ACCESS_FEE in discount_dto.service_types
            and len(discount_dto.service_types) > 1
        ):
            discount_dto.service_types = tuple(
                st for st in discount_dto.service_types if st != ServiceTypeEnum.ACCESS_FEE
            )

        return discount_dto

    @classmethod
    def _from_discount_dict_to_discount_dto(cls, discount: DiscountDict) -> DiscountDTO:
        call_destinations = None
        if _cds := discount.get("call_destinations"):
            call_destinations = tuple(CallDestinationEnum[cd] for cd in _cds)

        qualifying_direction = None
        if _q_direction := discount.get("qualifying_direction"):
            qualifying_direction = DiscountDirectionEnum[_q_direction]

        qualifying_service_types = None
        if _q_service_types := discount.get("qualifying_service_types"):
            qualifying_service_types = tuple(ServiceTypeEnum[st] for st in _q_service_types)

        qualifying_basis = None
        if _q_qualifying_basis := discount.get("qualifying_basis"):
            qualifying_basis = DiscountQualifyingBasisEnum[_q_qualifying_basis]

        qualifying_lower_bound = to_decimal(discount.get("qualifying_lower_bound"))

        if (
            qualifying_direction is not None
            and qualifying_service_types is not None
            and qualifying_basis is not None
            and qualifying_lower_bound is not None
        ):
            qualifying_rule = DiscountQualifyingRule(
                direction=qualifying_direction,
                service_types=qualifying_service_types,
                basis=qualifying_basis,
                volume_type=VolumeTypeEnum[discount["volume_type"]],
                lower_bound=qualifying_lower_bound,
                upper_bound=None,
            )
        else:
            qualifying_rule = None

        parameters = [
            DiscountParameterDTO(
                calculation_type=DiscountCalculationTypeEnum[p["calculation_type"]],
                basis=DiscountBasisEnum[p["basis"]] if p.get("basis", "") else None,  # type: ignore[misc]
                basis_value=to_decimal(p["basis_value"]),
                balancing=DiscountBalancingEnum[p["balancing"]] if p.get("balancing") else None,  # type: ignore[misc]
                bound_type=(
                    DiscountBoundTypeEnum[p["bound_type"]] if p.get("bound_type") else None  # type: ignore[misc]
                ),
                lower_bound=to_decimal(p["lower_bound"]),
                upper_bound=to_decimal(p["upper_bound"]),
                toll_rate=to_decimal(p["toll_rate"]),
                airtime_rate=to_decimal(p["airtime_rate"]),
                fair_usage_rate=to_decimal(p["fair_usage_rate"]),
                fair_usage_threshold=to_decimal(p["fair_usage_threshold"]),
                access_fee_rate=to_decimal(p["access_fee_rate"]),
                incremental_rate=to_decimal(p["incremental_rate"]),
            )
            for p in discount["discount_parameters"]
        ]

        parameters = sorted(parameters, key=lambda p: (p.lower_bound is not None, p.lower_bound))

        return DiscountDTO(
            home_operators=tuple(discount["home_operators"]),
            partner_operators=tuple(discount["partner_operators"]),
            direction=DiscountDirectionEnum[discount["direction"]],
            service_types=tuple(ServiceTypeEnum[service_type] for service_type in discount["service_types"]),
            start_date=Month.create_from_date(datetime.strptime(discount["start_date"], FULL_DATE_FORMAT)),
            end_date=Month.create_from_date(datetime.strptime(discount["end_date"], FULL_DATE_FORMAT)),
            currency_code=discount["currency_code"],
            tax_type=TaxTypeEnum[discount["tax_type"]],
            volume_type=VolumeTypeEnum[discount["volume_type"]],
            settlement_method=DiscountSettlementMethodEnum[discount["settlement_method"]],
            call_destinations=call_destinations,
            called_countries=tuple(discount["called_countries"]) if discount["called_countries"] else None,
            traffic_segments=tuple(discount["traffic_segments"]) if discount["traffic_segments"] else None,
            imsi_count_type=IMSICountTypeEnum[discount["imsi_count_type"]] if discount["imsi_count_type"] else None,
            qualifying_rule=qualifying_rule,
            parameters=tuple(parameters),
            model_type=None,
            inbound_market_share=None,
            above_commitment_rate=None,
            commitment_distribution_parameters=None,  # TODO: https://nextgenclearing.atlassian.net/browse/SNB-2406
        )
