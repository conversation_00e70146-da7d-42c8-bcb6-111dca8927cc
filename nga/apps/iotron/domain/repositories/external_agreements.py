from abc import ABC, abstractmethod
from typing import Optional

from nga.apps.iotron.domain.models import ExternalAgreement
from nga.apps.iotron.enums import ExternalAgreementProcessingStatusEnum


class AbstractExternalAgreementRepository(ABC):
    @abstractmethod
    def get_many(
        self, ids: Optional[list[int]] = None, processing_status: Optional[ExternalAgreementProcessingStatusEnum] = None
    ) -> list[ExternalAgreement]:
        """Return list of External Agreements."""

    @abstractmethod
    def get_by_id(self, external_agreement_id: int) -> ExternalAgreement:
        """Return External Agreement by provided ID."""

    @abstractmethod
    def save(self, external_agreement: ExternalAgreement) -> ExternalAgreement:
        """Persists External Agreement to storage."""
