import datetime
from dataclasses import dataclass
from typing import Optional

from nga.apps.iotron.domain.dto import DiscountDict
from nga.apps.iotron.enums import ExternalAgreementProcessingStatusEnum
from nga.core.types import DatePeriod
from nga.utils.dt import get_current_datetime_utc

__all__ = [
    "ExternalAgreement",
]


@dataclass
class ExternalAgreement:
    id: int

    external_id: int
    name: str

    period: DatePeriod

    home_operators: list[int]
    partner_operators: list[int]

    negotiator: Optional[int]

    do_not_calculate: bool

    include_satellite: bool

    include_premium: bool

    include_premium_in_commitment: bool

    is_rolling: bool

    include_access_fee_in_sop_financial_inbound: bool

    include_access_fee_in_sop_financial_outbound: bool

    discounts: list[DiscountDict]

    terminated_at: Optional[datetime.date]

    processed_at: Optional[datetime.datetime]
    processing_status: ExternalAgreementProcessingStatusEnum

    failed_message: Optional[str]

    @property
    def is_terminated(self) -> bool:
        return bool(self.terminated_at)

    def set_processing_status(self, status: ExternalAgreementProcessingStatusEnum) -> None:
        self.processing_status = status
        self.processed_at = get_current_datetime_utc()
