from nga.apps.agreements.enums import (
    DiscountBoundTypeEnum,
    DiscountDirectionEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
)
from nga.apps.common.types import Mapper
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, VolumeTypeEnum

__all__ = [
    "DISCOUNT_CALL_DESTINATION_MAP",
    "DISCOUNT_SETTLEMENT_METHOD_MAP",
    "DISCOUNT_DIRECTION_MAP",
    "DISCOUNT_IMSI_COUNT_TYPE_MAP",
    "DISCOUNT_PARAMETER_BOUND_TYPE_MAP",
    "DISCOUNT_PARAMETER_QUALIFYING_BASIS_MAP",
    "DISCOUNT_TAX_MAP",
    "DISCOUNT_VOLUME_TYPE_MAP",
]

DISCOUNT_SETTLEMENT_METHOD_MAP: Mapper = {
    "Credit Note EoA": DiscountSettlementMethodEnum.CREDIT_NOTE_EOA,
    "TAP Level (DCH)": DiscountSettlementMethodEnum.TAP_LVL_DCH,
    "Credit Note Monthly": DiscountSettlementMethodEnum.CREDIT_NOTE_MONTHLY,
}

DISCOUNT_TAX_MAP: Mapper = {
    "Net": False,
    "Gross": True,
}

DISCOUNT_VOLUME_TYPE_MAP: Mapper = {
    "Actual": VolumeTypeEnum.ACTUAL,
    "Billed": VolumeTypeEnum.BILLED,
}
DISCOUNT_IMSI_COUNT_TYPE_MAP: Mapper = {
    "Data": IMSICountTypeEnum.DATA,
    "No data": IMSICountTypeEnum.NO_DATA,
}
DISCOUNT_DIRECTION_MAP = {
    "Bi-Directional": DiscountDirectionEnum.BIDIRECTIONAL,
    "Customer/Outbound": DiscountDirectionEnum.OUTBOUND,
    "Visitor/Inbound": DiscountDirectionEnum.INBOUND,
}
DISCOUNT_PARAMETER_BOUND_TYPE_MAP = {
    "Volume": DiscountBoundTypeEnum.VOLUME,
    "Financial Commitment": DiscountBoundTypeEnum.FINANCIAL_COMMITMENT,
    "Market Share %": DiscountBoundTypeEnum.MARKET_SHARE,
    "Unique IMSI Count per Month": DiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH,
    "Volume included in Access Fee": DiscountBoundTypeEnum.VOLUME_INCLUDED_IN_ACCESS_FEE,
}
DISCOUNT_CALL_DESTINATION_MAP = {
    "HOM": CallDestinationEnum.HOME,
    "LOC": CallDestinationEnum.LOCAL,
    "INT": CallDestinationEnum.INTERNATIONAL,
    "UNKNOWN": CallDestinationEnum.UNKNOWN,
}
DISCOUNT_PARAMETER_QUALIFYING_BASIS_MAP = {
    "Volume": DiscountQualifyingBasisEnum.VOLUME,
    "Market Share %": DiscountQualifyingBasisEnum.MARKET_SHARE_PERCENTAGE,
    "Unique IMSI Count per Month": DiscountQualifyingBasisEnum.UNIQUE_IMSI_COUNT_PER_MONTH,
    "Average Monthly Usage per IMSI": DiscountQualifyingBasisEnum.AVERAGE_MONTHLY_USAGE_PER_IMSI,
}
