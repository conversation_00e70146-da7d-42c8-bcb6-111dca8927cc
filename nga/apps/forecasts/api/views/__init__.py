from nga.apps.forecasts.api.views.forecast_rule_api import ForecastRuleAPIView
from nga.apps.forecasts.api.views.forecast_rule_copy import ForecastRuleCopyAPIView
from nga.apps.forecasts.api.views.forecast_rule_copy_bulk import ForecastRuleCopyBulkAPIView
from nga.apps.forecasts.api.views.forecast_rule_coverage import ForecastRuleCoverageAPIView
from nga.apps.forecasts.api.views.forecast_rule_create import ForecastRuleCreateAPIView
from nga.apps.forecasts.api.views.forecast_rule_create_default import ForecastRuleCreateDefaultAPIView
from nga.apps.forecasts.api.views.forecast_rule_delete import ForecastRuleDeleteAPIView
from nga.apps.forecasts.api.views.forecast_rule_delete_bulk import ForecastRuleDeleteBulkAPIView
from nga.apps.forecasts.api.views.forecast_rule_filters import ForecastRuleFiltersAPIView
from nga.apps.forecasts.api.views.forecast_rule_list import ForecastRuleListAPIView
from nga.apps.forecasts.api.views.forecast_rule_update import ForecastRulePatchAPIView

__all__ = [
    "ForecastRuleAPIView",
    "ForecastRuleListAPIView",
    "ForecastRuleCreateAPIView",
    "ForecastRulePatchAPIView",
    "ForecastRuleDeleteAPIView",
    "ForecastRuleDeleteBulkAPIView",
    "ForecastRuleCopyAPIView",
    "ForecastRuleCopyBulkAPIView",
    "ForecastRuleCreateDefaultAPIView",
    "ForecastRuleFiltersAPIView",
    "ForecastRuleCoverageAPIView",
]
