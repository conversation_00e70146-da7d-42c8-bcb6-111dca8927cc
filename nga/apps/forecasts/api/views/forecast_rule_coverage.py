from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.common.view_mixins import QueryParametersMixin
from nga.apps.forecasts.api.serializers.coverage import ForecastRulesCoverageSerializer
from nga.apps.forecasts.api.serializers.forecast_rule_crud import (
    ForecastRuleQuerySerializer,
)
from nga.apps.forecasts.coverage import AbstractForecastRuleCoverageProvider, ForecastRuleCoverageOptions
from nga.core.types import DatePeriod


class ForecastRuleCoverageAPIView(QueryParametersMixin, GenericAPIView):
    serializer_class = ForecastRulesCoverageSerializer
    query_serializer_class = ForecastRuleQuerySerializer

    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        forecast_rule_coverage_provider: AbstractForecastRuleCoverageProvider = Closing[
            Provide["forecast_rule_coverage_provider"]
        ],
        **kwargs: dict[str, Any],
    ) -> None:
        super().__init__(**kwargs)

        self._forecast_rule_coverage_provider = forecast_rule_coverage_provider

    @swagger_auto_schema(
        query_serializer=query_serializer_class(),
        responses={status.HTTP_200_OK: serializer_class()},
    )
    def get(self, request: Request, *args: tuple[Any, ...], **kwargs: dict[str, Any]) -> Response:
        params = self.get_query_params()

        start_date, end_date = params.get("start_date"), params.get("end_date")

        if start_date and end_date:
            period = DatePeriod(start_date, end_date)
        else:
            period = None

        options = ForecastRuleCoverageOptions(
            budget_id=params["budget_id"],
            home_operators=params["home_operators"],
            partner_operators=params["partner_operators"],
            partner_countries=params["partner_countries"],
            period=period,
        )

        coverage = self._forecast_rule_coverage_provider.get_coverage(options)

        serializer = self.get_serializer(coverage)

        return Response(serializer.data)
