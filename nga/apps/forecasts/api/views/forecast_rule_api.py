from typing import Any

from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.filters import <PERSON><PERSON>ilter
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.common.views import BaseManageView
from nga.apps.forecasts.api.filters import ForecastRuleFilterSet
from nga.apps.forecasts.api.serializers.forecast_rule_crud import ForecastRuleQueryListSerializer

from .forecast_rule_create import ForecastRuleCreateAPIView
from .forecast_rule_list import ForecastRuleListAPIView


class ForecastRuleAPIView(BaseManageView):
    """API View that provides actions to Forecast Rule collection."""

    VIEWS_BY_METHOD = {
        "GET": ForecastRuleListAPIView,
        "POST": ForecastRuleCreateAPIView,
    }

    @swagger_auto_schema(
        query_serializer=ForecastRuleQueryListSerializer(),
        filters=[ForecastRuleFilterSet(), SearchFilter()],
        responses={status.HTTP_200_OK: VIEWS_BY_METHOD["GET"].serializer_class(many=True)},
    )
    def get(self, request: Request, *args: tuple[Any, ...], **kwargs: dict[str, Any]) -> Response:
        """Returns list of Forecast Rules."""

    @swagger_auto_schema(
        responses={status.HTTP_201_CREATED: VIEWS_BY_METHOD["POST"].serializer_class()},
        request_body=VIEWS_BY_METHOD["POST"].query_serializer_class(),
    )
    def post(self, request: Request, *args: Any, **kwargs: dict[str, Any]) -> Response:
        """Creates Forecast Rule."""
