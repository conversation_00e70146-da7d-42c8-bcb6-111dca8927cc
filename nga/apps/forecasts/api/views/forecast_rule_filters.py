from typing import Any

from django.contrib.postgres.aggregates import A<PERSON>y<PERSON>gg
from django.db.models import Max, Min
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.common.queryset_utils import period_intersection_query
from nga.apps.common.view_mixins import QueryParametersMixin
from nga.apps.forecasts.api.filters import ForecastRuleFilterSet
from nga.apps.forecasts.api.serializers.filters import ForecastFilterParametersSchemaSerializer
from nga.apps.forecasts.api.serializers.forecast_rule_crud import ForcastRulePeriodQuerySerializer
from nga.apps.forecasts.infra.orm import models
from nga.core.types import DatePeriod


class ForecastRuleFiltersAPIView(QueryParametersMixin, GenericAPIView):
    serializer_class = ForecastFilterParametersSchemaSerializer
    query_serializer_class = ForcastRulePeriodQuerySerializer

    permission_classes = [IsAuthenticated]

    filter_backends = [DjangoFilterBackend]
    filterset_class = ForecastRuleFilterSet

    queryset = models.ForecastRule.objects.prefetch_related(
        "home_operators",
        "partner_operators",
        "partner_countries",
    ).order_by("-pk")

    @swagger_auto_schema(
        query_serializer=query_serializer_class(),
        responses={status.HTTP_200_OK: serializer_class()},
    )
    def get(self, request: Request, *args: tuple[Any, ...], **kwargs: dict[str, Any]) -> Response:
        queryset = self.filter_queryset(self.get_queryset())

        params = self.get_query_params()

        start_date, end_date = params.get("start_date"), params.get("end_date")

        if start_date and end_date:
            queryset = queryset.filter(period_intersection_query(DatePeriod(start_date, end_date)))

        forecast_filter_parameters = queryset.aggregate(
            start_date_min=Min("start_date"),
            start_date_max=Max("start_date"),
            end_date_min=Min("end_date"),
            end_date_max=Max("end_date"),
            traffic_directions=ArrayAgg("traffic_direction", distinct=True, default=[]),
            service_types=ArrayAgg("service_type", distinct=True, default=[]),
            models=ArrayAgg("model", distinct=True, default=[]),
            distribution_models=ArrayAgg("distribution_model", distinct=True, default=[]),
        )

        serializer = self.get_serializer(forecast_filter_parameters)

        return Response(serializer.data)
