from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from django.db.transaction import atomic
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.view_utils import get_budget_or_404
from nga.apps.forecasts.api.serializers.forecast_rule_crud import ForecastRuleListDeleteSerializer
from nga.apps.forecasts.api.views.utils import send_budget_update_command
from nga.apps.forecasts.infra.orm import models


class ForecastRuleDeleteAPIView(CreateAPIView):
    serializer_class = ForecastRuleListDeleteSerializer
    permission_classes = [IsAuthenticated]

    queryset = models.ForecastRule.objects.all()

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: dict[str, Any],
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._budget_provider = budget_provider

    @swagger_auto_schema(responses={status.HTTP_204_NO_CONTENT: ""})
    @atomic
    def post(self, request: Request, *args: tuple[Any, ...], **kwargs: dict[str, Any]) -> Response:
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        qs = self.get_queryset()

        qs = qs.filter(pk__in=serializer.validated_data["ids"])

        budget_ids = qs.values_list("budget_id", flat=True)

        if len(set(budget_ids)) > 1:
            raise ValidationError("Removal from multiple budgets is not allowed.")

        budget_id = budget_ids[0]

        budget = get_budget_or_404(
            self._budget_provider,
            budget_id,
            raise_403_if_master=True,
        )

        qs.delete()

        send_budget_update_command(self._mediator, budget.id)

        return Response(status=status.HTTP_204_NO_CONTENT)
