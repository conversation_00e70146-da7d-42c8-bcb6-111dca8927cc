from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import Search<PERSON>ilter
from rest_framework.generics import ListAPIView
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated

from nga.apps.forecasts.api.filters import ForecastRuleFilterSet
from nga.apps.forecasts.api.serializers.forecast_rule_crud import (
    ForecastRuleListSerializer,
)
from nga.apps.forecasts.infra.orm import models


class ForecastRuleListPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = "page_size"
    max_page_size = 100


class ForecastRuleListAPIView(ListAPIView):
    serializer_class = ForecastRuleListSerializer
    permission_classes = [IsAuthenticated]

    pagination_class = ForecastRuleListPagination

    queryset = models.ForecastRule.objects.prefetch_related(
        "home_operators",
        "partner_operators",
        "partner_countries",
    ).order_by("-pk")

    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = ForecastRuleFilterSet

    search_fields = (
        "id__icontains",
        "home_operators__pmn_code__icontains",
        "partner_operators__pmn_code__icontains",
        "partner_countries__name__icontains",
    )
