import logging
import traceback
from typing import Any, cast

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework import status
from rest_framework.exceptions import APIException
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.view_utils import get_budget_or_404
from nga.apps.forecasts.api.schemas import ForecastRuleCopySchema
from nga.apps.forecasts.api.serializers.forecast_rule_copy import (
    ForecastCopyResponseSerializer,
    ForecastCopySchemaSerializer,
)
from nga.apps.forecasts.commands import BulkCopyForecastRuleCommand
from nga.core.exceptions import BaseNGAException


class ForecastRuleCopyAPIView(CreateAPIView):
    serializer_class = ForecastCopyResponseSerializer
    query_serializer_class = ForecastCopySchemaSerializer

    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: dict[str, Any],
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._budget_provider = budget_provider

    @swagger_auto_schema(
        request_body=query_serializer_class(),
        responses={status.HTTP_201_CREATED: serializer_class()},
    )
    def post(self, request: Request, *args: tuple[Any, ...], **kwargs: dict[str, Any]) -> Response:
        serializer = self.query_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        forecast_copy_schema = cast(ForecastRuleCopySchema, serializer.save())

        target_budget = get_budget_or_404(
            budget_provider=self._budget_provider,
            budget_id=forecast_copy_schema.target_budget_id,
            raise_403_if_master=True,
        )

        try:
            number_of_copied_rules = self._mediator.send(
                BulkCopyForecastRuleCommand(
                    target_budget_id=target_budget.id,
                    forecast_rule_ids=forecast_copy_schema.forecast_rule_ids,
                    source_budget_id=None,
                )
            )
        except BaseNGAException as e:
            logging.error(e.message)
            raise APIException(detail=e.message)
        except Exception as e:
            traceback.print_exc()
            raise APIException(detail=str(e))

        response_serializer = self.get_serializer({"number_of_copied_rules": number_of_copied_rules})

        return Response(data=response_serializer.data, status=status.HTTP_201_CREATED)
