from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework import status
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budget_background_jobs.command_factories import ScheduleBudgetBackgroundJobCommandFactory
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.view_utils import get_budget_or_404
from nga.apps.forecasts.api.schemas import ForecastRuleSetupSchema
from nga.apps.forecasts.api.serializers.forecast_rule_setup import ForecastRuleSetupRequestSerializer


class ForecastRuleCreateDefaultAPIView(CreateAPIView):
    """Starts Budget Background Job that creates collection of Forecast Rules based on the provided setup."""

    serializer_class = ForecastRuleSetupRequestSerializer

    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: dict[str, Any],
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._budget_provider = budget_provider

    @swagger_auto_schema(
        request_body=serializer_class(),
        responses={status.HTTP_202_ACCEPTED: ""},
    )
    def post(self, request: Request, *args: tuple[Any, ...], **kwargs: dict[str, Any]) -> Response:

        serializer = self.get_serializer(data=request.data)

        serializer.is_valid(raise_exception=True)

        setups_schema: ForecastRuleSetupSchema = serializer.save()

        budget = get_budget_or_404(self._budget_provider, budget_id=setups_schema.budget_id, raise_403_if_master=True)

        create_default_forecast_rules_cmd = ScheduleBudgetBackgroundJobCommandFactory.create_default_forecast_rules(
            budget_id=budget.id,
            setups=setups_schema.setups,
        )

        self._mediator.send(create_default_forecast_rules_cmd)

        return Response(status=status.HTTP_202_ACCEPTED)
