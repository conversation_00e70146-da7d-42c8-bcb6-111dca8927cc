from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework import status
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budget_background_jobs.command_factories import ScheduleBudgetBackgroundJobCommandFactory
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.view_utils import get_budget_or_404
from nga.apps.forecasts.api.schemas import ForecastRuleBulkCopySchema
from nga.apps.forecasts.api.serializers.forecast_rule_copy import (
    ForecastBulkCopySchemaSerializer,
)


class ForecastRuleCopyBulkAPIView(CreateAPIView):
    serializer_class = ForecastBulkCopySchemaSerializer

    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: dict[str, Any],
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._budget_provider = budget_provider

    @swagger_auto_schema(
        request_body=serializer_class(),
        responses={status.HTTP_202_ACCEPTED: ""},
    )
    def post(self, request: Request, *args: tuple[Any, ...], **kwargs: dict[str, Any]) -> Response:
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        forecast_copy_schema: ForecastRuleBulkCopySchema = serializer.save()

        target_budget = get_budget_or_404(
            budget_provider=self._budget_provider,
            budget_id=forecast_copy_schema.target_budget_id,
            raise_403_if_master=True,
        )

        cmd = ScheduleBudgetBackgroundJobCommandFactory.bulk_copy_forecast_rules(
            target_budget_id=target_budget.id,
            source_budget_id=forecast_copy_schema.source_budget_id,
        )

        self._mediator.send(cmd)

        return Response(status=status.HTTP_202_ACCEPTED)
