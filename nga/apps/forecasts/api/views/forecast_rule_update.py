from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from mediatr import Mediator
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.mixins import UpdateModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.view_utils import get_budget_or_404
from nga.apps.forecasts.api.serializers.forecast_rule_crud import (
    ForecastRuleListSerializer,
    ForecastRulePatchSchemaSerializer,
    ForecastRuleSerializer,
)
from nga.apps.forecasts.api.views.utils import send_budget_update_command
from nga.apps.forecasts.infra.orm import models


class ForecastRulePatchAPIView(UpdateModelMixin, GenericAPIView):
    permission_classes = [IsAuthenticated]

    serializer_class = ForecastRuleSerializer
    response_serializer_class = ForecastRuleListSerializer

    queryset = models.ForecastRule.objects.all()

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: dict[str, Any],
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator

        self._budget_provider = budget_provider

    @swagger_auto_schema(
        request_body=ForecastRulePatchSchemaSerializer(),
        responses={status.HTTP_200_OK: response_serializer_class()},
    )
    def patch(self, request: Request, *args: tuple[Any, ...], **kwargs: dict[str, Any]) -> Response:
        """Partial update of Forecast Rule."""

        forecast_rule = self.get_object()

        budget = get_budget_or_404(self._budget_provider, forecast_rule.budget_id, raise_403_if_master=True)

        super().partial_update(request, *args, **kwargs)

        send_budget_update_command(self._mediator, budget.id)

        response_data = self.response_serializer_class(self.get_object()).data

        return Response(response_data)
