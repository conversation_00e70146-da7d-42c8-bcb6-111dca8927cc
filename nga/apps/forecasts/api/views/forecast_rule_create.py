from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator
from rest_framework import status
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.view_utils import get_budget_or_404
from nga.apps.forecasts.api.serializers.forecast_rule_crud import ForecastRuleListSerializer, ForecastRuleSerializer
from nga.apps.forecasts.api.views.utils import send_budget_update_command


class ForecastRuleCreateAPIView(CreateAPIView):
    serializer_class = ForecastRuleListSerializer
    query_serializer_class = ForecastRuleSerializer

    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        **kwargs: dict[str, Any],
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator

        self._budget_provider = budget_provider

    def post(self, request: Request, *args: tuple[Any, ...], **kwargs: dict[str, Any]) -> Response:
        serializer = self.query_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        budget = get_budget_or_404(
            budget_provider=self._budget_provider,
            budget_id=serializer.validated_data["budget"].id,
            raise_403_if_master=True,
        )

        forecast_rule = serializer.save()

        send_budget_update_command(self._mediator, budget.id)

        response_data = self.serializer_class(forecast_rule).data

        headers = self.get_success_headers(response_data)

        return Response(response_data, status=status.HTTP_201_CREATED, headers=headers)
