from decimal import Decimal

from nga.apps.agreements.domain.models import Discount
from nga.apps.calculation.discounts.models.sop_financial.result import SubDiscountResults


class FinancialThresholdCalculator:
    """
    Financial threshold logic within SoP Financial discount model is applied to the result of applied sub-discounts.
    Once threshold is exceeded we need to recalculate discount charge for each sub-discount
    by applying above threshold rate.
    """

    @classmethod
    def apply(cls, discount: Discount, sub_discount_results: SubDiscountResults) -> SubDiscountResults:
        """Applies financial threshold to the result charge of sub-discounts."""

        financial_threshold = discount.financial_threshold

        if financial_threshold is None:
            return sub_discount_results

        total_discount_charge = sub_discount_results.calculate_total_charge()

        for result in sub_discount_results:
            if total_discount_charge == 0:
                charge_share = Decimal(1 / len(discount.sub_discounts))
            else:
                charge_share = result.charge / total_discount_charge

            distribution_of_threshold = charge_share * financial_threshold

            volume_inside_threshold = result.volume * financial_threshold / total_discount_charge

            if financial_threshold > total_discount_charge:
                result.charge = distribution_of_threshold

            else:
                above_threshold_rate = result.sub_discount.above_financial_threshold_rate

                if above_threshold_rate is not None:
                    volume_above_threshold = result.volume - volume_inside_threshold

                    result.charge = distribution_of_threshold + volume_above_threshold * above_threshold_rate

        sub_discount_results.initial_total_charge = sub_discount_results.calculate_total_charge()

        return sub_discount_results
