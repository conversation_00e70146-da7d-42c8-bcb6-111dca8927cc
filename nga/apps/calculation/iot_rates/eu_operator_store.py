import abc


class EUOperatorStore:
    def __init__(self, eu_operators: tuple[int, ...]) -> None:
        self._eu_operators = eu_operators

    @property
    def operators(self) -> tuple[int, ...]:
        return self._eu_operators

    def is_eu_operator(self, operator_id: int) -> bool:
        return operator_id in self._eu_operators


class AbstractEUOperatorProvider(abc.ABC):
    @abc.abstractmethod
    def get_many(self) -> EUOperatorStore:
        """Returns the collection of EU operators."""
