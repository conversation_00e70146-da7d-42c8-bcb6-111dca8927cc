# 7. Messaging

Date: 2023-06-01

Updated: 2024-10-10

## Status

Accepted

## Context

It's needed to create a message stream for publishing an integration events for 3rd-party applications 
and for notifying about changes in our system.

## Decision

* Introduce messaging architectural style. Create a MessageStream abstraction.
* Use `Redis` as transport.

## Consequences
* Appeared new infrastructure dependency - `Redis`.
