# 5. Master budget

Date: 2023-03-23

## Status

Accepted

## Context

System has Budget entity. Budget can be of two types: Master Budget and Simulated Budget.  
After Master Budget is created, user is forbidden to:
- Create new Master Budget.
- Mark Master Budget as Simulated Budget (set master=False).
- Remove Master Budget.

## Decision
1. Add to ORM model unique constraint.
2. Update Django Budget Admin model to hide `is_master` field when it already exist.
3. Update Django Admin Budget model to forbid removing existing Master Budget.

## Consequences
* Master Budget Logic leaked to framework implementation.
