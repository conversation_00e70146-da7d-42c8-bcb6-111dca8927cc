# 2. User authorization for API

Date: 2022-10-6

## Status

Accepted

## Context

We have to add rules for user authorization in Budget & Simulations API by using Connected Platform (CP)
mechanisms.

## Decision

1. Use OIDC provider along with Django Authorization system.
2. Map CP user roles on Django Authorization system.
3. Override default authorization backend by using `mozilla-django-oidc` package.
4. Extend custom OIDC authorization backend with adding a token introspection step.

## Consequences

#### 1. Django User model password field
* As user password is not needed after integration with OIDC provider, this field is not
    used in the system and stays empty.
* Field is excluded from all forms and other places.
* Field does not take part in user authorization process.

#### 1. CP user roles mapped on Django Authorization system in next way:

*  If users have `DistributorAdmin` role, they will become Django Superusers 
    (`user.is_superuser is True`).

*  If users have `DistributorUser` role, they will become Django Staff Users
    (`user.is_staff is True`).

*  If users have any `Client*` role, they will become Django Regular Users (without any permissions).

*  If user role has not been mapped to any predefined roles, that User will be created with it
    and will have not any permissions like client user.

####  2. `PlatformAuthenticationBackend` have been inherited from `mozilla-django-oidc.OIDCAuthenticationBackend` in order to perform next actions:
* Apply mapping of user roles on Django Authorization System.
* Do access token validation by making token introspection call to OIDC Provider.

#### 3. Implementation depends on access token structure:
```json
{
  "sub": "<user-id",
  "email": "<user-email>",
  "organization": {
    "id": "<org-id>"
  },
  "realm_access": {
    "roles": [
      "role-1"
    ]
  }
}
```
