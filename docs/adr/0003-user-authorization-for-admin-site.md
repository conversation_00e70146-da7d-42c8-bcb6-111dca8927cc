# 3. User authorization for Django Admin Site

Date: 2022-10-14

## Status

Accepted

## Context

We have to define rules and approaches for accessing Django Admin Site by using SSO.

## Decision

1. Configure `mozilla-django-oidc` for protecting Admin Site by OIDC Provider. 
2. Store in Django session OIDC access token and introspect it on every attempt access Admin Site.

## Consequences

#### Override default Django login & logout views
* These auth views have been replaced with `mozilla-django-oidc` views, in order to add redirects
   to OIDC provider and start auth process.

#### Django default Admin Site has been overridden
1. Admin Site has been overriden in order to verify users during accessing site:
   * They are not anonymous.
   * They have Django session and an access token there
   * Their token is valid by making introspection call
   * They are staff or superusers.

2. If none of above rules are true, user will get a 403 error. It was done in order to break 
    endless chain of redirects to OIDC Provider, when user have been authenticated in Provider,
    but does not have access to Django Admin Site (have non distributor user role).
